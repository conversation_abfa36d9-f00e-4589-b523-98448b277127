using NPOI.SS.Formula.Functions;
using ToolsFramework.Settings;

namespace Fragulizer.Common.NewToolsFramework
{
    public class SettingsServiceWithPreviewMode : ToolsFramework.Settings.ISettingsService
    {
        private string _toolName;
        private string _GeneralPath;
        private string _ToolPath;

        public SettingsServiceWithPreviewMode(string toolName, string aGeneralPath, string aToolPath)
        {
            _toolName = toolName;
            _GeneralPath = aGeneralPath;
            _ToolPath = aToolPath;
        }

        public ToolCompanySettings GetSettings(string aCompanyCode)
        {
            return new ToolCompanySettings2(aCompanyCode, _toolName, _GeneralPath, _ToolPath);
        }
    }
}
