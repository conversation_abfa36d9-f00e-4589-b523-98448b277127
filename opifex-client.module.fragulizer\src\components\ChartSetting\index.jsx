import { Form, Card, Row, Col, Select, ColorPicker } from "antd";
import { useInputStyle } from "../../hooks/useInputStyle";
import { FormItemInput } from "../FormSettingItem/FormItemInput";
import { FormItemTextArea } from "../FormSettingItem/FormItemTextArea";

const PERIOD_OPTIONS = [
  { value: "LIVE", label: "LIVE" },
  { value: "1M", label: "1M" },
  { value: "3M", label: "3M" },
  { value: "6M", label: "6M" },
  { value: "1Y", label: "1Y" },
];

const DEFAULT_CHART_COLORS = [
  "#4572A7", // Xanh dương đậm - <PERSON>àu chính
  "#AA4643", // Đỏ đậm - <PERSON>àu thứ hai
  "#89A54E", // Xanh lá - <PERSON><PERSON>u thứ ba
  "#80699B", // Tí<PERSON> - <PERSON><PERSON><PERSON> thứ tư
  "#3D96AE", // Xanh ngọ<PERSON> - <PERSON><PERSON><PERSON> thứ năm
  "#DB843D", // <PERSON> - <PERSON><PERSON><PERSON> thứ sáu
  "#92A8CD", // Xanh nhạt - Màu thứ bảy
  "#A47D7C", // Nâu hồng - Màu thứ tám
  "#B5CA92", // Xanh lá nhạt - Màu thứ chín
  "#F15C80", // Hồng - Màu thứ mười
];

const getChartColorsForMaxInstruments = (maxInstruments) => {
  const colors = [];
  for (let i = 0; i < maxInstruments; i++) {
    colors.push(DEFAULT_CHART_COLORS[i % DEFAULT_CHART_COLORS.length]);
  }
  return colors;
};

const getMaxInstrumentsInGroup = (instrumentGroups) => {
  if (!instrumentGroups || !Array.isArray(instrumentGroups)) return DEFAULT_CHART_COLORS.length;

  return instrumentGroups.reduce((max, group) => {
    const instrumentCount = Array.isArray(group?.InstrumentIDs) ? group.InstrumentIDs.length : 0;
    return Math.max(max, instrumentCount);
  }, DEFAULT_CHART_COLORS.length);
};

export const ChartSetting = ({ form }) => {
  const { inputStyle } = useInputStyle();

  const instrumentGroups = Form.useWatch(["InstrumentGroups"], form) || [];
  const maxInstruments = getMaxInstrumentsInGroup(instrumentGroups);
  const requiredColors = getChartColorsForMaxInstruments(maxInstruments);

  return (
    <div>
      <Card title="Default Period" size="small" style={{ marginBottom: 16 }}>
        <Form.Item
          name={["DefaultPeriod"]}
          label="Change default period for Fragmentation and Market Share sections"
          rules={[
            { required: true, message: "Please select a default period" },
          ]}
        >
          <Select
            placeholder="Select default period"
            style={inputStyle}
            options={PERIOD_OPTIONS}
          />
        </Form.Item>
      </Card>

      <Card title="Chart Appearance" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name={["NumberOfYearOnColumnChart"]}
              label="Number of years on column chart"
            >
              <FormItemInput
                standalone={false}
                type="number"
                min={1}
                max={10}
                placeholder="3"
                style={inputStyle}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Card title="Chart Colors Configuration" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          {requiredColors.map((defaultColor, index) => (
            <Col span={8} key={index}>
              <Form.Item
                name={["ChartColors", index]}
                label={`Color ${index + 1}`}
                initialValue={defaultColor}
              >
                <ColorPicker
                  showText
                  format="hex"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          ))}
        </Row>

        <div style={{ marginTop: 16, padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
          <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>
            <strong>Note:</strong> Colors will be applied in order to the instruments within each group.
            If a group contains more instruments than the number of configured colors, the colors will repeat from the beginning.
          </p>
        </div>
      </Card>
    </div>
  );
};
