import { Form, Card, Row, Col, Select, ColorPicker } from "antd";
import { useInputStyle } from "../../hooks/useInputStyle";
import { FormItemInput } from "../FormSettingItem/FormItemInput";
import { FormItemTextArea } from "../FormSettingItem/FormItemTextArea";

const PERIOD_OPTIONS = [
  { value: "LIVE", label: "LIVE" },
  { value: "1M", label: "1M" },
  { value: "3M", label: "3M" },
  { value: "6M", label: "6M" },
  { value: "1Y", label: "1Y" },
];

export const ChartSetting = ({ form }) => {
  const { inputStyle } = useInputStyle();

  return (
    <div>
      <Card title="Default Period" size="small" style={{ marginBottom: 16 }}>
        <Form.Item
          name={["DefaultPeriod"]}
          label="Change default period for Fragmentation and Market Share sections"
          rules={[
            { required: true, message: "Please select a default period" },
          ]}
        >
          <Select
            placeholder="Select default period"
            style={inputStyle}
            options={PERIOD_OPTIONS}
          />
        </Form.Item>
      </Card>
      <Card title="Chart Appearance" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name={["NumberOfYearOnColumnChart"]}
              label="Number of years on column chart"
            >
              <FormItemInput
                standalone={false}
                type="number"
                min={1}
                max={10}
                placeholder="3"
                style={inputStyle}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </div>
  );
};
