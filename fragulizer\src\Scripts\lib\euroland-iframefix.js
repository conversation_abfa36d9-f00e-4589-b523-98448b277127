﻿// ----------------------------------------------------------------------
// <copyright file="euroland-iframefix.js" company="Euroland.com">
//     Copyright (c) Euroland.com. All rights reserved.
// </copyright>
// <author><EMAIL></author>
// <created>Tuesday, July 02, 2013 10:27</created>
// <lastedit>Tuesday, July 02, 2013 10:27</lastedit>
// <changes>
// </changes>
// -----------------------------------------------------------------------

/// <reference path="jquery-1.6.2.js" />
/// <reference path="../JsExtentions.js" />
/// <reference path="highstock.js" />

/*
* Dependencies: JsExtentions.js, highstock.js
*/

/**
* IFrame Fix for webpage on Safari on touch device when flip between portrait and lanscape
*/
(function ($) {
    function eulog(msg) {
        if (!$('#log').length) {
            $('<div/>').attr('id', 'log').css({
                position: 'fixed',
                top: 0,
                left: 0,
                opacity: 0.6,
                'max-height': '200px',
                'overflow-y': 'auto',
                'background-color': '#fff'
            }).appendTo(document.body);
        }

        $('#log').append('<div>' + msg + '</div>');
    }

    function viewport() {
        var e = window, a = 'inner';
        if (!('innerWidth' in window)) {
            a = 'client';
            e = document.documentElement || document.body;
        }
        return { width: e[a + 'Width'], height: e[a + 'Height'] };
    }

    function logViewport() {
        var vp = viewport();
        eulog('Viewport w:' + vp.width + ' h:' + vp.height);
    }

    $.log = function (msg) {
        eulog(msg);
    }

    $.viewportSize = function () {
        return viewport();
    }

    $.touchDevices.IosInsideIframe = (window.self !== window.top) && (/Ip(ad|hone|od)/i.test(navigator.userAgent));

    if ($.touchDevices.IosInsideIframe) {
        function getDocumentWidth() {
            return Math.min($(document).width(), $(document.body).width(), viewport().width);
        }

        function onresizeX(e) {
            //logViewport();
            /*$('.wrapper:first').css({
            'overflow-x': 'hidden',
            width: getDocumentWidth()
            });*/
            //$('.wrapper').hide();
        }

        //$(window).bind('resize', onresizeX);

        //$(function () {
        $('html').css('-webkit-text-size-adjust', '100%');

        $(document.body).css({
            padding: 0,
            margin: 0,
            width: '99.5%',
            overflow: 'hidden',
            '-webkit-text-size-adjust': '100%'
        });

        onresizeX();
    }
})(jQuery);

/**
* Highstock Fix
*/
(function () {
    if (!Highcharts) {
        return;
    }

    var doc = document,
	    win = window,
        HC = Highcharts,
	    Chart = HC.Chart,
	    addEvent = HC.addEvent,
        removeEvent = HC.removeEvent,
	    createElement = HC.createElement,
	    discardElement = HC.discardElement,
	    Renderer = HC.Renderer,
	    css = HC.css,
	    merge = HC.merge,
	    each = HC.each,
	    extend = HC.extend,
	    math = Math,
	    mathMax = math.max,
	    mathMin = math.min,
	    pick = HC.pick,
	    hasTouch = 'ontouchstart' in doc.documentElement,
	    timeFactor = 1, // 1 = JavaScript time, 1000 = Unix time
	    M = 'M',
	    L = 'L',
	    DIV = 'div',
	    UNDEFINED,
	    HIDDEN = 'hidden',
	    RELATIVE = 'relative',
	    NONE = 'none',
	    PREFIX = 'highcharts-',
	    ABSOLUTE = 'absolute',
	    NORMAL_STATE = '',
	    HOVER_STATE = 'hover',
	    SELECT_STATE = 'select',
	    PX = 'px',
	    userAgent = navigator.userAgent,
	    isIE = /msie/i.test(userAgent) && !win.opera,
	    options = HC.getOptions(),
	    useUTC = options.global.useUTC;

    function defined(obj) {
        return obj !== UNDEFINED && obj !== null;
    };
    function isString(s) {
        return typeof s === 'string';
    }
    // Disable animation for chart on touch devices to improve rendering performance
    // TODO: Should be better to disable animation on mobiles only
    if (!$.touchDevices) throw 'iframefix needs JsExtentions.js library';
    if ($.touchDevices.IosInsideIframe) {
        //if (true) {
        HC.setOptions({
            chart: {
                animation: false
                // Disable 'resize' event which fired automatically 
                // when device flip between portrait and lanscape orientation.
                // This requires developer must to redraw manually chart when
                // resize event fired
                //,reflow: false
            },
            tooltip: {
                animation: false
            }
        });

        var originalInit = HC.Chart.prototype.init,
            originalInitReflow = HC.Chart.prototype.initReflow,
			reflowTimeoutF;

        extend(HC.Chart.prototype, {
            init: function (userOptions, callback) {
                /*userOptions.chart = userOptions.chart || {};
                var renderTo = isString(userOptions.chart.renderTo) ? $('#' + userOptions.chart.renderTo) : $(userOptions.chart.renderTo);
                var width = renderTo.width() - (renderTo.outerWidth() - renderTo.innerWidth()),
                bodyWidth = Math.max($(document).width(), $(document.body).width());*/

                originalInit.apply(this, [userOptions, callback]);
            },
            // Override initReflow to make own-call
            initReflow: function () {
                var chart = this,
					optionsChart = chart.options.chart,
					renderTo = isString(optionsChart.renderTo) ? $('#' + optionsChart.renderTo) : $(optionsChart.renderTo),
					reflowTimeout;
                function reflow(e) {
                    var width = optionsChart.width || renderTo.width(),
                        height = optionsChart.height || renderTo.height(),
                        target = e ? e.target : win; // #805 - MooTools doesn't supply e
                    if (!chart.hasUserSize && width && height && (target === win || target === doc)) {
                        if (width !== chart.containerWidth || height !== chart.containerHeight) {
                            if (chart.container)
                                chart.container.style.display = 'none';

                            clearTimeout(reflowTimeout);
                            chart.reflowTimeout = reflowTimeout = setTimeout(function () {
                                if (chart.container) { // It may have been destroyed in the meantime (#1257)
                                    chart.cleanRedraw();
                                    chart.hasUserSize = null;
                                }
                            }, 100);
                        }
                        chart.containerWidth = width;
                        chart.containerHeight = height;
                    }
                }

                addEvent(win, 'resize', reflow);
                addEvent(chart, 'destroy', function () {
                    removeEvent(win, 'resize', reflow);
                });
            },

            // There a bug of Highstock on Iphone is when device flip
            // between portrait and lanscape, the chart (in an iframe)
            // fires 'redraw' event many times (because 'resize' event is
            // fired many times) to reduce sequently by per pixel the size of chart's container.
            // This fix is simple, copy entire the chart's options, data, and then
            // destroy current chart, then re-creat chart on the same container. 
            cleanRedraw: function () {
                if (!doc.createElementNS) {
                    doc.createElementNS = function (ns, tagName) {
                        var elem = doc.createElement(tagName);
                        elem.getBBox = function () {
                            return chart.renderer.Element.prototype.getBBox.apply({ element: elem });
                        };
                        return elem;
                    };
                }
                var chart = this,
                    seriesOptions,
				    config,
				    pointOptions,
				    pointMarker,
                    options;

                // Clone current chart data before destroying
                options = merge(chart.options, {});

                // prepare for replicating the chart
                options.series = [];
                each(chart.series, function (serie) {
                    seriesOptions = serie.options;

                    seriesOptions.animation = false; // turn off animation
                    seriesOptions.showCheckbox = false;

                    // remove image markers
                    if (seriesOptions && seriesOptions.marker && /^url\(/.test(seriesOptions.marker.symbol)) {
                        seriesOptions.marker.symbol = 'circle';
                    }

                    seriesOptions.data = [];

                    each(serie.data, function (point) {

                        // extend the options by those values that can be expressed in a number or array config
                        config = point.config;
                        pointOptions = {
                            x: point.x,
                            y: point.y,
                            color: point.color,
                            name: point.name
                        };

                        if (typeof config == 'object' && point.config && config.constructor != Array) {
                            extend(pointOptions, config);
                        }

                        seriesOptions.data.push(pointOptions); // copy fresh updated data

                        // remove image markers
                        pointMarker = point.config && point.config.marker;
                        if (pointMarker && /^url\(/.test(pointMarker.symbol)) {
                            delete pointMarker.symbol;
                        }
                    });

                    options.series.push(seriesOptions);
                });

                chart.destroy();
                chart.init(options, chart.callback);
            }
        });
    }
})();