﻿using System;
using ToolsFramework;

public static class Translations
{
	private static readonly ILanguageService langService = Services.Get<ILanguageService>();
	public static string PhraseTranslation(string strPhrase, string lang)
    {
        return langService.GetTranslationByLanguageString(strPhrase, "en").TranslationMap[lang];
    }
    /// <summary>
	/// Fragulizer
	/// </summary>
	public static System.Web.HtmlString HEADING_TEXT { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("HEADING_TEXT")); } }
    /// <summary>
	/// Date & Time:
	/// </summary>
	public static System.Web.HtmlString DATE_TIME { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("DATE_TIME")); } }
    /// <summary>
	/// Select Share
	/// </summary>
	public static System.Web.HtmlString SELECT_SHARE { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("SELECT_SHARE")); } }
    /// <summary>
	/// Market
	/// </summary>
	public static System.Web.HtmlString MARKET { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("MARKET")); } }
    /// <summary>
	/// Last
	/// </summary>
	public static System.Web.HtmlString LAST { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("LAST")); } }
    /// <summary>
	/// Change %
	/// </summary>
	public static System.Web.HtmlString CHANGE { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("CHANGE")); } }
    /// <summary>
	/// High
	/// </summary>
	public static System.Web.HtmlString HIGH { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("HIGH")); } }
    /// <summary>
	/// Low
	/// </summary>
	public static System.Web.HtmlString LOW { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("LOW")); } }
    /// <summary>
	/// Volume
	/// </summary>
	public static System.Web.HtmlString VOLUME { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("VOLUME")); } }
    /// <summary>
	/// Time
	/// </summary>
	public static System.Web.HtmlString TIME { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("TIME")); } }
    /// <summary>
	/// Currency
	/// </summary>
	public static System.Web.HtmlString CURRENCY { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("CURRENCY")); } }
    /// <summary>
	/// Fragmentation
	/// </summary>
	public static System.Web.HtmlString FRAGMENTATION { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("FRAGMENTATION")); } }
    /// <summary>
	/// Select time period
	/// </summary>
	public static System.Web.HtmlString SELECT_TIME_PERIOD { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("SELECT_TIME_PERIOD")); } }
    /// <summary>
	/// Market share
	/// </summary>
	public static System.Web.HtmlString MARKET_SHARE { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("MARKET_SHARE")); } }
    /// <summary>
	/// %
	/// </summary>
	public static System.Web.HtmlString PERCENTAGE { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("PERCENTAGE")); } }
    /// <summary>
	/// Activity trend
	/// </summary>
	public static System.Web.HtmlString ACTIVITY_TREND { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("ACTIVITY_TREND")); } }
    /// <summary>
	/// Supplied by © @
	/// </summary>
	public static System.Web.HtmlString SUPPLIED_BY_TEXT { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("SUPPLIED_BY_TEXT")); } }
    /// <summary>
	/// Live
	/// </summary>
	public static System.Web.HtmlString LIVE { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("LIVE")); } }
    /// <summary>
	/// No data found for  {0}. Showing data for {1}
	/// </summary>
	public static System.Web.HtmlString NO_DATA_FOUND { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("NO_DATA_FOUND")); } }
    /// <summary>
	/// For terms of use and data vendors information, see {0}
	/// </summary>
	public static System.Web.HtmlString TERM_DATA_VENDOR_INFORMATION { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("TERM_DATA_VENDOR_INFORMATION")); } }
    /// <summary>
	/// Disclaimer
	/// </summary>
	public static System.Web.HtmlString DISCLAIMER { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("DISCLAIMER")); } }
    /// <summary>
	/// Data delayed at least {0} minutes
	/// </summary>
	public static System.Web.HtmlString ALL_DATA_DELAYED { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("ALL_DATA_DELAYED")); } }
    /// <summary>
	/// Data delayed at least {0} minutes ({1})
	/// </summary>
	public static System.Web.HtmlString DATA_DELAYED { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("DATA_DELAYED")); } }
    /// <summary>
	/// Data is real time
	/// </summary>
	public static System.Web.HtmlString ALL_DATA_REAL_TIME { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("ALL_DATA_REAL_TIME")); } }
    /// <summary>
	/// Data is real time ({0})
	/// </summary>
	public static System.Web.HtmlString DATA_REAL_TIME { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("DATA_REAL_TIME")); } }
    /// <summary>
	/// Data is end of day
	/// </summary>
	public static System.Web.HtmlString ALL_DATA_END_OF_DAY { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("ALL_DATA_END_OF_DAY")); } }
    /// <summary>
	/// Data is end of day ({0})
	/// </summary>
	public static System.Web.HtmlString DATA_END_OF_DAY { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("DATA_END_OF_DAY")); } }
    /// <summary>
	/// Data provided by @
	/// </summary>
	public static System.Web.HtmlString DATA_PROVIDED { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("DATA_PROVIDED")); } }
    /// <summary>
	/// Share
	/// </summary>
	public static System.Web.HtmlString SHARE { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("SHARE")); } }
    /// <summary>
	/// Date
	/// </summary>
	public static System.Web.HtmlString DATE { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("DATE")); } }
    /// <summary>
	/// Create a Custom Timeframe
	/// </summary>
	public static System.Web.HtmlString CREATE_A_CUSTOM_TIMEFRAME { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("CREATE_A_CUSTOM_TIMEFRAME")); } }
    /// <summary>
	/// No Data Available
	/// </summary>
	public static System.Web.HtmlString NO_DATA_AVAILABLE { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("NO_DATA_AVAILABLE")); } }
    /// <summary>
	/// Selected period
	/// </summary>
	public static System.Web.HtmlString SELECTED_PERIOD { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("SELECTED_PERIOD")); } }
    /// <summary>
	/// Last update
	/// </summary>
	public static System.Web.HtmlString LAST_UPDATE { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("LAST_UPDATE")); } }
    /// <summary>
	/// 1 M
	/// </summary>
	public static System.Web.HtmlString PERIOD_1_MONTH { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("PERIOD_1_MONTH")); } }
    /// <summary>
	/// 3 M
	/// </summary>
	public static System.Web.HtmlString PERIOD_3_MONTH { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("PERIOD_3_MONTH")); } }
    /// <summary>
	/// 6 M
	/// </summary>
	public static System.Web.HtmlString PERIOD_6_MONTH { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("PERIOD_6_MONTH")); } }
    /// <summary>
	/// 1 Y
	/// </summary>
	public static System.Web.HtmlString PERIOD_1_YEAR { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("PERIOD_1_YEAR")); } }
    /// <summary>
	/// Print
	/// </summary>
	public static System.Web.HtmlString PRINT { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("PRINT")); } }
    /// <summary>
	/// Export as JPG
	/// </summary>
	public static System.Web.HtmlString EXPORT_TO_JPG { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("EXPORT_TO_JPG")); } }
    /// <summary>
	/// Export all the data into an Excel
	/// </summary>
	public static System.Web.HtmlString EXPORT_TO_EXCEL { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("EXPORT_TO_EXCEL")); } }
    /// <summary>
	/// Export as PDF
	/// </summary>
	public static System.Web.HtmlString EXPORT_TO_PDF { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("EXPORT_TO_PDF")); } }
    /// <summary>
	/// Cookie policy
	/// </summary>
	public static System.Web.HtmlString COOKIES_POLICY { get { return new System.Web.HtmlString(langService.GetTranslationWithCustomPhraseName("COOKIES_POLICY")); } }
}
