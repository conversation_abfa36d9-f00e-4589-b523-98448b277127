﻿using ToolsFramework.Settings;
using Euroland.NetCore.ToolsFramework.Setting;
using Euroland.NetCore.ToolsFramework.Setting.Abstractions;
using System.Linq;
using Microsoft.Extensions.Options;
using System.Web;
using System.IO;
using System.Configuration;
using System.Collections.Generic;
using System.Runtime;
using System;

namespace Fragulizer.Common.NewToolsFramework
{
    public class ToolCompanySettings2 : ToolCompanySettings
    {
        const string REQUEST_CACHE_KEY = "____ToolCompanySettings2____";
        //private Dictionary<string, Setting> settingDict = new Dictionary<string, Setting>(StringComparer.OrdinalIgnoreCase);
        private bool _transformed = false;
        public ToolCompanySettings2(string companyCode, string aToolName, string aGeneralPath, string aToolPath)
            : base(companyCode, aToolName, aGeneralPath, aToolPath)
        {
            //var builder = new Euroland.NetCore.ToolsFramework.Setting.SettingManagerBuilder();
        }

        //protected override Setting GetSetting(string aKey)
        //{
        //    aKey = aKey.ToLower();
        //    if (string.IsNullOrEmpty(aKey) || !settingDict.ContainsKey(aKey))
        //    {
        //        return new Setting(aKey, string.Empty);
        //    }

        //    return settingDict[aKey];
        //}

        private void TransformSetting()
        {
            if(_transformed) return;

            var originalSettingObj = GetSetting();
            
            foreach (var item in originalSettingObj.GetChildren())
            {
                TransformSettingRecursive(item, CreateSetting(item.Name));
            }

            // Silly code, but this is to fix base method this.getStyleFileUri() to get StyleURI if possible.
            string name = this.ToolCompanySettingsFileName;
            _transformed = true;
        }

        private void TransformSettingRecursive(ISettingItem newParent, Setting parent)
        {
            if (!newParent.GetChildren().Any())
            {
                if (parent.Key.ToLower() == "decimalseparator" || parent.Key.ToLower() == "thousandsseparator")
                {
                    if (newParent.Value == "space")
                    {
                        parent.Set(" ");
                        return;
                    }
                }
                parent.Set(newParent.Value);
                return;
            }
            else
            {
                foreach (var item in newParent.GetChildren())
                {
                    TransformSettingRecursive(item, parent.Create(item.Name));
                }
            }
        }

        private ISetting GetSetting()
        {
            // Try to get setting object from current request
            var ctx = GetHttpContext();
            var settingCtxAccessor = ctx.Items.Contains(REQUEST_CACHE_KEY)
                ? ctx.Items[REQUEST_CACHE_KEY] as ISettingContextAccessor
                : new SettingContextAccessor();
            // If setting object not exists, just create a new one and 
            // cache for the sub-sequent accesses.
            if (!ctx.Items.Contains(REQUEST_CACHE_KEY))
            {
                var builder = CreateSettingBuilder();
                settingCtxAccessor.SettingManager = builder.SettingManager;
                settingCtxAccessor.Setting = builder.SettingManager.Create();

                ctx.Items[REQUEST_CACHE_KEY] = settingCtxAccessor;
            }

            return settingCtxAccessor.Setting;
        }

        protected override bool Read()
        {
            TransformSetting();
            
            return false;
        }

        private ISettingManagerBuilder CreateSettingBuilder()
        {
            //System.Web.HttpContext.Current.it
            var builder = new SettingManagerBuilder();

            var generalSettingFile = System.IO.Path.Combine(GeneralPath, "setting.xml");
            var toolSettingFile = System.IO.Path.Combine(ToolPath, string.Format("{0}.xml", NameTool));
            var generalCompanySettingDirectory = System.IO.Path.Combine(GeneralPath, "Company");
            var toolCompanySettingDirectory = System.IO.Path.Combine(ToolPath, "Company");

            if (HttpContextExtensions.IsPreviewMode(HttpContext.Current.Request))
            {
                var opifexToolCompanySettingsPath = ConfigurationManager.AppSettings["Opifex.ToolCompanyXmlPath"];
                var opifexGeneralSettingDirectory = ConfigurationManager.AppSettings["Opifex.ToolGeneralXmlPath"];

                toolCompanySettingDirectory = Path.Combine(opifexToolCompanySettingsPath);
                generalCompanySettingDirectory = Path.Combine(opifexGeneralSettingDirectory);

            }

            var generalCompanySettingProviderFactory = new RequestSettingProviderFactory(generalCompanySettingDirectory, optional: true)
            {
                ResourceType = SettingResourceType.Xml,
                RequestFinder = new CompanyCodeRequestSettingProvider(),
                Optional = true
            };
            var toolSettingProviderFactory = new RequestSettingProviderFactory(toolCompanySettingDirectory, optional: false)
            {
                ResourceType = SettingResourceType.Xml,
                RequestFinder = new CompanyCodeRequestSettingProvider(),
                Optional = false
            };

            var toolSettingFile4 = System.IO.Path.Combine(ToolPath, ToolSettingsFileName);

            return builder
                .UseSettingProvider(CreateOrGetGeneralSettingFileProviderFactory(generalSettingFile))
                .UseSettingProvider(generalCompanySettingProviderFactory)
                .UseSettingProvider(CreateOrGetToolSettingFileProviderFactory(toolSettingFile))
                .UseSettingProvider(toolSettingProviderFactory);

        }

        private static ISettingProviderFactory _generalProviderFactory;
        private ISettingProviderFactory CreateOrGetGeneralSettingFileProviderFactory(string path)
        {
            if(_generalProviderFactory == null)
            {
                _generalProviderFactory = new StaticFileSettingProviderFactory(path, true);
            }

            return _generalProviderFactory;
        }

        private static ISettingProviderFactory _toolSettingProviderFactory;
        private ISettingProviderFactory CreateOrGetToolSettingFileProviderFactory(string path)
        {
            if (_toolSettingProviderFactory == null)
            {
                _toolSettingProviderFactory = new StaticFileSettingProviderFactory(path, false);
            }

            return _toolSettingProviderFactory;
        }

        protected virtual System.Web.HttpContext GetHttpContext()
        {
            return System.Web.HttpContext.Current;
        }
    }
}