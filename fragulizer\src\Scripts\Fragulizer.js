﻿/// <reference path="lib/jquery.js" />
/// <reference path="JsExtentions.js" />
/* region: Settings */

/*! Fragulizer.js ******* */

/*
 * *******: Fixed: 
 *      #1 Kesko A note still remains on Kesko B page
 *      #2 order shares on chart's tooltips likes on grid
 */

var
    // Total activity of the page (ajax request, chart drawing) should to be done
    // then let DownloadPdf tool know that page has rendered completely.
    _TotalPageActivity = 0,
    _TotalPageActivityTimeout,
    _IsExporting = false;
function checkActivity()
{
    //if (!_IsExporting)
        //return;

    _TotalPageActivityTimeout && clearTimeout(_TotalPageActivityTimeout);
    _TotalPageActivityTimeout = setTimeout(function () {
        if (_TotalPageActivity == 0) {
            $('#fakeAsyncActivity').remove();
        }
    }, 500);
    
}
var Settings = {
    downloadUrl: '',
    companyCode: null,
    dateFormat: 'MM/dd/yyyy',
    longDateFormat: 'MMMM/dd/yyyy',
    dateSeparator: '/',
    decimalSeparator: '.',
    thousandSeparator: ',',
    decimalDigits: 2,
    sharepriceNumberDecimalDigits: '2',
    sharePricePercentageDecimalDigits: '3',
    negativeNumberFormat: '(n)',
    shortTime: 'HH:mm',
    longTime: 'HH:mm.ss',
    pagetimezone: 0,
    servertimezone: 0,
    enableHeading: false,
    enableInstrumentsTable: false,
    enableActivityTrend: false,
    OpenTime: null,
    CloseTime: null,
    SelectedPeriod: 'live',
    fontStyle: {
        fontFamily: 'arial',
        fontSize: '11px',
        color: 'black'
    },
    chartColors: ['#4693BC', '#FE1600', '#FC9E00', '#F7D404', '#F0FF08'],
    ChartBGColor: 'none'
};

var Labels = {
    msgNoDataAvailable: 'No Data Available',
    selectedperiod: 'Selected period',
    lastupdate: 'Last update'
};

/* region: Utility */
var Utility = {
    getWindowDimentions: function () {

        var ret = new Object();
        ret.width = 0;
        ret.height = 0;

        if (typeof (window.innerWidth) == 'number') {
            //Non-IE
            ret.width = window.innerWidth;
            ret.height = window.innerHeight;
        } else if (document.documentElement && (document.documentElement.clientWidth || document.documentElement.clientHeight)) {
            //IE 6+ in 'standards compliant mode'
            ret.width = document.documentElement.clientWidth;
            ret.height = document.documentElement.clientHeight;
        } else if (document.body && (document.body.clientWidth || document.body.clientHeight)) {
            //IE 4 compatible
            ret.width = document.body.clientWidth;
            ret.height = document.body.clientHeight;
        }

        ret.scrollLeft = document.documentElement.scrollLeft;
        ret.scrollTop = document.documentElement.scrollTop;
        if (ret.scrollLeft == ret.scrollTop && ret.scrollLeft == 0) {
            //Webkit browsers hold the scroller info in the document.body and not the document.documentElement
            ret.scrollLeft = document.body.scrollLeft;
            ret.scrollTop = document.body.scrollTop;
        }
        return ret;
    },
    /* function:    displayDate
    ** input:       date object
    ** output:      - input date is today, show time only,
    **              - input date is past day, show date and time in setting format
    **              - take pageTimezone into account when displaying time
    */
    displayDate: function (datetime) {
        var me = this;
        var currentDate = new Date();
        var dateToTimezone = me.convertToServerTimezone(currentDate);
        var newCurrentDate = new Date(
                dateToTimezone.getFullYear(),
                dateToTimezone.getMonth(),
                dateToTimezone.getDate()
         );
        var newCurrentDateFormated = $.format.date(newCurrentDate, Settings.dateFormat + ' ' + Settings.shortTime);
        var timeFormat = $.format.date(newCurrentDate, Settings.shortTime);
        var datetimeFormated = $.format.date(datetime, Settings.dateFormat + ' ' + Settings.shortTime);
        var time = datetimeFormated.replace(newCurrentDateFormated.replace(timeFormat, ''), '');
        return time != '' ? time : datetimeFormated;
    },

    /* function:    toPageTimezone
    ** input:       date object in local time zone 
    ** output:      date object in page time zone
    */
    toPageTimezone: function (datetime) {
        var me = this;
        if (!$.isDate(datetime))
            return datetime;

        var clientTimezone = new Date().getTimezoneOffset() / 60;
        var date = new Date(
                datetime.getFullYear(),
                datetime.getMonth(),
                datetime.getDate(),
                datetime.getHours(),
                datetime.getMinutes(),
                datetime.getSeconds(),
                datetime.getMilliseconds()
            );
        date.addMinutes((Settings.pagetimezone + clientTimezone) * 60);
        return date;
    },

    /* function:    convertToServerTimezone
    ** input:       date object in local time zone
    ** output:      date object in server time zone
    */
    convertToServerTimezone: function (datetime) {
        var me = this;
        if (!$.isDate(datetime))
            return datetime;

        var clientTimezone = new Date().getTimezoneOffset() / 60;
        var date = new Date(
                datetime.getFullYear(),
                datetime.getMonth(),
                datetime.getDate(),
                datetime.getHours(),
                datetime.getMinutes(),
                datetime.getSeconds(),
                datetime.getMilliseconds()
            );
        date.addMinutes((Settings.servertimezone + clientTimezone) * 60);
        return date;
    },

    /* function:    isRtl
    ** return:      direction of text
    ** description: check whether language are written right-to-left such as Hebrew and Arabic 
    */
    isRtl: function () {
        var _isArabic = document.body.getAttribute('dir') && document.body.getAttribute('dir').toLowerCase() == 'rtl';
        var _isUseLatin = document.body.getAttribute('latin') && document.body.getAttribute('latin').toLowerCase() == 'true';
        return (_isArabic && !_isUseLatin);
    },

    GetNumber: function (input) {
        return this.isRtl() == true ? String.toArabicNumber(input) : input;
    },

    /* function:    styliseHtmlInputControl
    ** description: apply style to checkbox or radio button
    */
    styliseHtmlInputControl: function (container) {
        if (!container)
            return;
        var $checkboxs = $(':checkbox:not(:disabled), :radio:not(:disabled)', container);

        //.wrap('<span class="checkbox"/>').click(function () {

        function onClick(e) {
            var $this = $(this.target);
            var checkedClass = $this.is(':radio') ? 'radio-checked' : 'checkbox-checked';
            if ($this.is(':checkbox')) {
                if ($this.parent().hasClass(checkedClass)) {
                    $this.parent().removeClass(checkedClass);
                }
                else {
                    $this.parent().addClass(checkedClass);
                }
            } else {
                if (!$this.parent().hasClass(checkedClass)) {
                    $this.parent().addClass(checkedClass);
                    // Uncheck all the controls which have the same group name
                    this.list.filter('[name="' + $this.attr('name') + '"]:not(:checked)').parent('.' + checkedClass).removeClass(checkedClass);
                }
            }
        };

        $checkboxs.each(function () {
            //if()
            var $chk = $(this).wrap('<span class="' + ($(this).is(':radio') ? 'radio' : 'checkbox') + '" />').click($.proxy(onClick, {
                target: this,
                list: $checkboxs
            }));
            if ($chk.is(':checked')) {
                var checkedClass = $chk.is(':radio') ? 'radio-checked' : 'checkbox-checked';
                $chk.parent().addClass(checkedClass);
            }
            //$chk.click(onClick);
            $chk.parent().siblings('label').click($.proxy(function (e) { $(this).trigger('click'); }, this));

            if ($.browser.msie && $.browser.version < 8) {
                var float = Utility.isRtl() ? 'right' : 'left';
                $chk.parent().css({
                    display: 'block',
                    float: float
                }).siblings('label')
            .css({
                float: float
            });
            }
        });

    },

    /* function:    dateDiff
    ** description: difference between two dates, ignoring time of day
    */
    dateDiff: function (first, second) {
        var firstYear = first.getFullYear(),
            firstMonth = first.getMonth() < 9 ? '0' + Number(first.getMonth() + 1) : first.getMonth() + 1,
            firstDate = first.getDate() < 10 ? '0' + Number(first.getDate()) : first.getDate(),
            secondYear = second.getFullYear(),
            secondMonth = second.getMonth() < 9 ? '0' + Number(second.getMonth() + 1) : second.getMonth() + 1,
            secondDate = second.getDate() < 10 ? '0' + second.getDate() : second.getDate();

        first = firstYear.toString() + firstMonth.toString() + firstDate.toString();
        first = Number(first);

        second = secondYear.toString() + secondMonth.toString() + secondDate.toString();
        second = Number(second);

        return second - first;
    },

    addMonth: function (date, number) {
        var returnDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        returnDate.setMonth(returnDate.getMonth() + number);

        return returnDate;
    },

    getPeriodDate: function (period) {
        var startDate = new Date(),
            endDate = new Date();

        switch (period) {
            case '1m':
                endDate = new Date();
                startDate = this.addMonth(endDate, -1);
                break;
            case '3m':
                endDate = new Date();
                startDate = this.addMonth(endDate, -3);
                break;
            case '6m':
                endDate = new Date();
                startDate = this.addMonth(endDate, -6);
                break;
            case '1y':
                endDate = new Date();
                startDate = new Date(endDate.getFullYear() - 1, endDate.getMonth(), endDate.getDate());
                break;
            case 'custom':
                break;
            default:
                break;
        }

        return {
            StartDate: startDate,
            EndDate: endDate
        };
    },

    /* function: getTickOfCurrentYear
    **
    */
    getTickOfCurrentYear: function () {
        var currentDate = new Date();
        var dtDate1 = currentDate.getFullYear() + "-01-01"
        var dtDate2 = (currentDate.getFullYear() + 1) + "-01-01"

        var nDifference = Math.abs(new Date(dtDate1) - new Date(dtDate2));
        return nDifference;
    },
    /*
    **
    */
    removeDateFormatPart: function (datepart) {
        var separate = Settings.dateSeparator;
        var mustReplace = '';
        var x = Settings.dateFormat.indexOf(datepart);
        var y = Settings.dateFormat.lastIndexOf(datepart);
        for (var i = x; i <= y; i++) {
            mustReplace += datepart;
        }
        var regex;
        if (Settings.dateFormat.indexOf(mustReplace + separate) != -1) {
            regex = new RegExp(mustReplace + separate, 'g');
        }
        else {
            regex = new RegExp(separate + mustReplace, 'g');
        }
        var text = Settings.dateFormat.replace(regex, '');

        return text;
    },
    /*
    **
    */
    getXAxisDateFormat: function (type) {
        if (type == 1) {
            var datepart = 'd';
            return this.removeDateFormatPart(datepart);
        }
        else if (type == 2) {
            var datepart = 'y';
            return this.removeDateFormatPart(datepart);
        }
        else {
            return 'yyyy';
        }
    }
};


/* region: DataCentre */
var DataCentre = {
    defaultOptions: {
        // default url to get data
        dataUrl: '',
        latestTradingDataUrl: '',
        // Interval time (in second) to get automatically new request to server. 
        // Default value is 1 minute
        params: {
            firstTime: true,
            takeActivityData: true,
            instrumentIDs: '',
            period: '',
            startDate: null,
            endDate: null,
            companyCode: null,
            lang: null
        },
        requestInterval: 60,
        // Callback function after data is returned from server
        fragmentationDataReady: function (data) { },
        marketShareDataReady: function (data) { },
        activityTrendDataReady: function (data) { },
        latestTradingDataReady: function (data) { },
        // Callback function if has any error while requesting data
        dataError: function (textStatus, error) { }
    },
    xhr: undefined,
    interval: null,

    /*
    ** client-side response data caching
    */
    cachedActivityData: {},
    cachedHistoricalData: {},

    addToCache: function (instrumentIds, data, startDate, endDate) {
        this.cachedHistoricalData[instrumentIds] = {
            Start: startDate,
            End: endDate,
            Data: data
        };
    },

    isExistingInCache: function (instrumentIds, from, to) {
        var cache = this.cachedHistoricalData[instrumentIds];
        if (cache == undefined) return false;
        if (Utility.dateDiff(from, cache.Start) > 0 || Utility.dateDiff(to, cache.End) < 0) return false;

        return true;
    },

    getFromCache: function (instrumentIds, from, to) {
        if (!this.isExistingInCache(instrumentIds, from, to)) return null;

        var fragmentationData = [],
            marketShareData = [],
            cache = this.cachedHistoricalData[instrumentIds];
        if (cache.Data) {
            $.each(cache.Data, function () {
                var marketId = this.MarketID,
                marketName = this.MarketName,
                data = [],
                volume = 0;

                $.each(this.Data, function () {
                    var date = this.Time.toDate();
                    if (Utility.dateDiff(from, date) >= 0 && Utility.dateDiff(to, date) <= 0) {
                        volume += this.Volume;
                        data.add({
                            Time: this.Time,
                            Volume: this.Volume
                        });
                    }
                });

                fragmentationData.add({
                    MarketID: marketId,
                    MarketName: marketName,
                    Data: data
                });
                marketShareData.add({
                    MarketID: marketId,
                    MarketName: marketName,
                    Volume: volume
                });
            });
        }

        return {
            FragmentationData: {
                data: fragmentationData,
                message: '',
                isRetrieveLiveData: false
            },
            MarketShareData: marketShareData
        };
    },
    /* end caching */

    getDefaultOptions: function () {
        return $.extend({}, this.defaultOptions);
    },

    setOptions: function (options) {
        if (!$.isObject(options))
            return;
        this.defaultOptions = $.extend({}, this.defaultOptions, options);
    },

    getParams: function () {
        return $.extend({}, this.defaultOptions.params);
    },

    setParams: function (values) {
        if (!$.isObject(values))
            return;
        var tmp = $.extend({}, values, true);
        var paramArray = [];
        for (var param in this.defaultOptions.params) {
            if (tmp[param] !== undefined) {
                this.defaultOptions.params[param] = tmp[param];
            }
            if (this.defaultOptions.params[param])
                paramArray.push(param + '=' + this.defaultOptions.params[param]);
        }
        
        var hash = '#/Home/GetData/?' + paramArray.join('&');
        
        this.updateExportUrl(hash);
    },

    getData: function () {
        var me = this;
        var opts = me.getDefaultOptions();
        var startDate = opts.params.startDate,
            endDate = opts.params.endDate;

        /*if (opts.params.period.toLowerCase() == 'custom') {
            startDate = startCal.selection.getDates()[0],
            endDate = endCal.selection.getDates()[0];
        }*/

        if (opts.params.period.toLowerCase() != 'live') {
            startDate = Utility.getPeriodDate(opts.params.period.toLowerCase()).StartDate;
            endDate = Utility.getPeriodDate(opts.params.period.toLowerCase()).EndDate;
        }

        // check data existing in cache
        var instrumentIds = opts.params.instrumentIDs;
        var checkCacheFirst = false;
        if (opts.params.period.toLowerCase() != 'live' && DataCentre.isExistingInCache(instrumentIds, startDate, endDate)) { checkCacheFirst = true; }
        if (opts.params.period.toLowerCase() == 'custom' && checkCacheFirst) { checkCacheFirst = (Utility.dateDiff(startDate, endDate) != 0); }

        if (checkCacheFirst) {
            var data = DataCentre.getFromCache(instrumentIds, startDate, endDate);
            if (data && $.isFunction(opts.fragmentationDataReady)) {
                opts.fragmentationDataReady(data.FragmentationData);
            }

            if (data && $.isFunction(opts.marketShareDataReady)) {
                opts.marketShareDataReady(data.MarketShareData);
            }

            this.getLatestTradingData();

            var activityData = DataCentre.cachedActivityData[instrumentIds];
            if (activityData && $.isFunction(opts.activityTrendDataReady)) {
                opts.activityTrendDataReady(activityData);
            }
        }
        else {
            // get data from server
            if (me.xhr != undefined)
                me.xhr.abort();

            _TotalPageActivity++;

            opts.params.takeActivityData = DataCentre.cachedActivityData[opts.params.instrumentIDs] == undefined;

            var searchParams = new URLSearchParams({
                'companyCode': opts.params.companyCode,
                'lang': opts.params.lang
            });

            me.xhr = $.ajax({
                url: opts.dataUrl + '?' + searchParams.toString(),
                type: 'POST',
                dataType: 'json',
                data: opts.params,
                beforeSend: function () {
                    me.showLoading();
                },
                complete: function () {
                    me.hideLoading();
                    //fakeAsyncActivity
                    _TotalPageActivity--;
                    checkActivity();
                },
                success: function (data) {
                    // latest trading
                    if (data.LatestTradingData
                        && data.LatestTradingData.length > 0
                        && $.isFunction(opts.latestTradingDataReady)) {
                        var latestTradingData = {
                            data: data.LatestTradingData,
                            message: data.LatestTradingMessage
                        };
                        opts.latestTradingDataReady(latestTradingData);
                    }

                    // fragmentation and market share sections
                    if (me.getParams().firstTime) {
                        Settings.OpenTime = data.OpenTime;
                        Settings.CloseTime = data.CloseTime;
                    }

                    // historical data
                    var isHistorical = true;
                    if (opts.params.period.toLowerCase() == 'live') isHistorical = false;
                    if (opts.params.period.toLowerCase() == 'custom' && isHistorical) {
                        isHistorical = Utility.dateDiff(startDate, endDate) > 0;
                    }

                    // Reorder data array of chart in order to be same with data array of grid
                    if (data.FragmentationData && data.LatestTradingData) {
                        var gridDataLength = data.LatestTradingData.length,
                            graphDataLength = data.FragmentationData.length,
                            shareDataLength = data.MarketShareData.length,
                            foundIndex = -1, marketID;
                        for (var i = 0; i < gridDataLength; i++) {
                            marketID = data.LatestTradingData[i].MarketId;
                            foundIndex = data.FragmentationData.getIndex(function () {
                                return this.MarketID == marketID;
                            });

                            if (foundIndex >= 0 && i < graphDataLength) {
                                data.FragmentationData.insert(data.FragmentationData.splice(foundIndex, 1)[0], i);
                            }

                            foundIndex = -1;
                            foundIndex = data.MarketShareData.getIndex(function () {
                                return this.MarketId == marketID;
                            });

                            if (foundIndex >= 0 && i < shareDataLength) {
                                data.MarketShareData.insert(data.MarketShareData.splice(foundIndex, 1)[0], i);
                            }
                        }
                    }

                    if (isHistorical) {
                        // add historical data to cache
                        DataCentre.addToCache(instrumentIds, data.FragmentationData, startDate, endDate);

                        // fragmentation section
                        var cachedHistoricalData = DataCentre.getFromCache(instrumentIds, startDate, endDate);
                        if ($.isFunction(opts.fragmentationDataReady)) {
                            opts.fragmentationDataReady(cachedHistoricalData.FragmentationData);
                        }

                        // market share section
                        if ($.isFunction(opts.marketShareDataReady)) {
                            opts.marketShareDataReady(cachedHistoricalData.MarketShareData);
                        }
                    }
                        // live data
                    else {
                        // fragmentation section
                        var fragData = {
                            data: data.FragmentationData,
                            message: data.Message,
                            isRetrieveLiveData: data.IsRetrieveLiveData
                        };

                        if (data.FragmentationData
                            && data.FragmentationData.length > 0
                            && $.isFunction(opts.fragmentationDataReady)) {
                            opts.fragmentationDataReady(fragData);
                        }

                        // market share section
                        if (data.MarketShareData
                            && data.MarketShareData.length > 0
                            && $.isFunction(opts.marketShareDataReady)) {
                            opts.marketShareDataReady(data.MarketShareData);
                        }
                        else {
                            // generating empty market share table
                            var trEmpty = '<tr>' +
                                            '<td class="table-row market-share-row td-message-no-data border-inline" colspan="3">' +
                                            Labels.msgNoDataAvailable +
                                            '</td>';
                            $('.market-info-body').html(trEmpty);
                            MarketShare.PieChart.destroy();
                        }
                    }

                    // activity trend: column charts
                    if (me.getParams().takeActivityData
                        && data.ActivityTrendData
                        && data.ActivityTrendData.length > 0
                        && $.isFunction(opts.activityTrendDataReady)) {
                        DataCentre.cachedActivityData[instrumentIds] = data.ActivityTrendData;
                    }
                    opts.activityTrendDataReady(DataCentre.cachedActivityData[instrumentIds]);

                    // next time, will not get data for market share and activity trend sections
                    me.setParams({
                        firstTime: false,
                        OpenTime: Settings.OpenTime.Hours + ':' + Settings.OpenTime.Minutes,
                        CloseTime: Settings.CloseTime.Hours + ':' + Settings.CloseTime.Minutes
                    });
                },
                error: function (jqxhr, textStatus, error) {
                    if ($.isFunction(opts.dataError)) {
                        opts.dataError(textStatus, error);
                    }
                }
            });
        }
    },

    getLatestTradingData: function () {
        var opts = this.getDefaultOptions(),
            params = {
                instrumentIDs: opts.params.instrumentIDs,
                companyCode: opts.params.companyCode,
                lang: opts.params.lang
            };
        var me = this;
        $.ajax({
            url: opts.latestTradingDataUrl,
            type: 'POST',
            dataType: 'json',
            data: params,
            beforeSend: function () {
                me.showLoading();
            },
            complete: function () {
                me.hideLoading();
            },
            success: function (data) {
                // latest trading
                if (data.LatestTradingData && data.LatestTradingData.length > 0 && $.isFunction(opts.latestTradingDataReady)) {
                    var latestTradingData = {
                        data: data.LatestTradingData,
                        message: data.LatestTradingMessage
                    };
                    opts.latestTradingDataReady(latestTradingData);
                }
            },
            error: function (jqxhr, textStatus, error) {
                if ($.isFunction(opts.dataError)) {
                    opts.dataError(textStatus, error);
                }
            }
        });
    },

    startRequest: function () {
        var me = this;
        me.getData();
    },

    startIntervalRequest: function () {
        var me = this;
        var opts = me.getDefaultOptions();
        if (Settings.SelectedPeriod.toLowerCase() == 'live') {
            me.interval = setInterval(function () {
                me.startRequest();
            }, (opts.requestInterval > 0 ? opts.requestInterval : 60) * 1000);
        }
    },

    cancelIntervalRequest: function () {
        var me = this;
        me.interval && clearInterval(me.interval);
    },

    showLoading: function () {
        $('.loading').removeClass('display-none');
        $('#chart-container, #live-alert, #legend-info, .market-info-body, .pie-chart-container, .share-info-body')
        .stop(true)
        .animate({
            opacity: 0.5
        }, 1000);
    },

    hideLoading: function () {
        $('.loading').addClass('display-none');
        $('#chart-container, #live-alert, #legend-info, .market-info-body, .pie-chart-container, .share-info-body')
        .stop(true)
        .animate({
            opacity: 1
        }, 1000);
    },
    updateExportUrl: function (hash) {
        var exportToolUrl = Settings.downloadUrl.replace(/[?&/]$/g, ''),
            exportQuery = '?',
            $exportButtonContainer = $('.command-button'),
            $pdfButton = $exportButtonContainer.find('.pdf-icon'),
            $jpgButton = $exportButtonContainer.find('.jpg-icon'),
            url,
            lo = document.location;
        
        url = lo.protocol + '//' + lo.host + baseUri + 'Export/FileType' + lo.search + hash;
        
        url = encodeURIComponent(url);
        
        $jpgButton.attr('href', exportToolUrl + '?fileType=jpg&url=' + url);
        $pdfButton.attr('href', exportToolUrl + '?fileType=pdf&url=' + url);
    }
};

/* region: LiveChart */
var LiveChart = {
    containerId: 'chart-container',
    lineChartObject: null,
    isLiveMode: false,
    lineChartOptions: {
        global: {
            useUTC: true
        },
        chart: {
            backgroundColor: Settings.ChartBGColor,
            type: 'spline',
            events: {
                load: function () {
                    $('#legend-info').html('');
                    var legend = '<ul class="legend-group">';
                    $.each(this.series, function (index, val) {
                        legend += '<li>';
                        legend += '<table cellpadding="0" cellspacing="0"><tr>';
                        legend += '<td><div class="legend-color" style="background-color:' + val.color + ';">&nbsp;</div></td>'
                        legend += '<td><div class="legend-name">' + val.name + '</div></td>';
                        legend += '</tr></table>';
                        legend += '</li>';
                    });
                    legend += '</ul>';

                    $('#legend-info').html(legend);

                    function resizeLegendItem() {
                        $('#legend-info').find('li').each(function () {
                            var float = Utility.isRtl() ? 'right' : 'left';
                            $(this).css({ 'width': 'auto', 'float': float, 'padding': '0', 'margin': '0' });
                        });
                        var legendContainer = $('#legend-info'),
                        containerWidth = legendContainer.width(),
                        maxItemWidth = 0,
                        totalWidth = 0;
                        $('#legend-info').find('li').each(function () {
                            var li = $(this),
                            table = $('table', li).first(),
                            itemWidth = table.outerWidth(true);

                            totalWidth += itemWidth;
                            if (itemWidth > maxItemWidth) maxItemWidth = itemWidth;
                        });

                        if (maxItemWidth > containerWidth / 2) {
                            $('#legend-info').find('li').each(function () {
                                $(this).css({ 'width': '100%' });
                            });
                        }
                        else if (totalWidth > containerWidth) {
                            $('#legend-info').find('li').each(function () {
                                $(this).css({ 'width': '50%' });
                            });
                        }
                    }
                    resizeLegendItem();
                    $(window).off('resize.legenditem').on('resize.legenditem', function () {
                        resizeLegendItem();
                    });

                    _TotalPageActivity--;
                    checkActivity();
                }
            }
            //marginLeft: 65
        },
        credits: {
            enabled: false
        },
        legend: {
            enabled: false,
            layout: 'horizontal',
            verticalAlign: 'bottom',
            borderWidth: 0
        },
        scrollbar: {
            enabled: false
        },
        navigator: {
            enabled: false
        },
        rangeSelector: {
            selected: 0,
            enabled: false
        },
        title: {
            text: ''
        },
        xAxis: {

        },
        yAxis: {
            title: {
                text: ''
            },
            min: 0,
            labels: {
                useHTML: true,
                formatter: function () {
                    if (this.value > 1000000) {
                        return '<div style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">' + Utility.GetNumber((this.value / 1000000) + 'M') + '</div>'
                    }
                    else if (this.value > 1000) {
                        return '<div style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">' + Utility.GetNumber((this.value / 1000) + 'K') + '</div>'
                    }
                    else {
                        return '<div style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">' + Utility.GetNumber(this.value) + '</div>'
                    }
                },
                style: Settings.fontStyle
            }
        },
        exporting: {
            enabled: false
        },
        plotOptions: {
            series: {
                connectNulls: true
            },
            spline: {
                lineWidth: 2,
                shadow: false,
                states: {
                    hover: {
                        enabled: true,
                        lineWidth: 2
                    }
                },
                marker: {
                    enabled: false,
                    symbol: 'circle',
                    states: {
                        hover: {
                            enabled: true,
                            radius: 4
                        }
                    }
                }
            }
        },
        tooltip: {
            crosshairs: {
                color: '#CCC',
                dashStyle: 'solid'
            },
            useHTML: true,
            shared: true,
            formatter: function () {
                var str = '';
                if (LiveChart.isLiveMode) {
                    str += '<div style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">'
                    str += Utility.isRtl() == true ? String.toArabicNumber($.format.date(new Date(this.x), Settings.shortTime)) : $.format.date(new Date(this.x), Settings.shortTime)
                    str += '</div>';
                }
                else {
                    str += '<div style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">'
                    str += Utility.isRtl() == true ? String.toArabicNumber($.format.date(new Date(this.x), Settings.dateFormat)) : $.format.date(new Date(this.x), Settings.dateFormat)
                    str += '</div>';
                }
                str += '<div><table>';
                for (var i = 0; i < this.points.length; i++) {
                    p = this.points[i];
                    str += '<tr><td style="color:' + p.series.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">' + p.series.name + ':</td>';
                    str += '<td style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '; text-align:right;"><b>'
                    str += Utility.isRtl() == true ? String.toArabicNumber($.format.number(p.y, 'N0')) : $.format.number(p.y, 'N0') + '</b></td>';
                    str += '</tr>';
                }
                str += '</table></div>';
                return str;
            }
        },
        series: []
    },

    /* function:    addSeries
    ** input:       chart series{id, name, data}
    ** description: add series into fragmentation chart
    **              - insert if new
    **              - update if existing
    */
    addSeries: function (series) {
        var existingSeries = null;
        var me = this;
        if (me.lineChartObject) {
            $.each(this.lineChartOptions.series, function (i, val) {
                if (val.id == series.id) {
                    existingSeries = me.lineChartObject.get(val.id);
                    return false; // break loop
                }
            });
        }
        if (existingSeries == null) {
            this.lineChartOptions.series.push(series);
        }
        else {
            me.lineChartObject.get(series.id).update({
                id: series.id,
                name: series.name,
                data: series.data
            }, false);
        }
    },

    /* function:    drawChart
    ** input:       raw data
    */
    drawChart: function (rawData) {
        if (!$.isArray(rawData)) {
            rawData = [rawData];
        }

        if (rawData.length == 0) return; // need alert here

        // update axis
        this.rebuildxAxis(rawData);

        // - history chart: clear all old series, then insert brand new series below
        // - live chart: just update data for each series, insert new if it's not existing
        if (Settings.SelectedPeriod.toLowerCase() != 'live') {
            this.lineChartOptions.series = [];
        }

        // process for each series
        for (var i = 0; i < rawData.length; i++) {
            // detech data of a certain series
            var lstLiveData = rawData[i];
            if (!lstLiveData || lstLiveData.Data.length == 0) break;

            // take instrumentId as series Id
            var instrumentId = lstLiveData.MarketID;

            var data = [];
            $.each(lstLiveData.Data, function (i, val) {
                //NOTE
                var dataDate = Utility.convertToServerTimezone(val.Time.toDate()),
                    xValue = new Date(dataDate.getFullYear()
                                    , dataDate.getMonth()
                                    , dataDate.getDate()
                                    , dataDate.getHours()
                                    , dataDate.getMinutes(), 0, 0);

                var x = xValue.getTime(),
                    y = val.Volume;

                data.push([x, y]);
            });
            this.addSeries({
                id: instrumentId,
                name: lstLiveData.MarketName,
                color: Settings.chartColors[i],
                data: data
            });
        }

        if (Settings.SelectedPeriod.toLowerCase() != 'live') {
            LiveChart.destroy();
        }

        _TotalPageActivity++;

        // draw at first time
        this.lineChartOptions.chart.renderTo = this.containerId;
        this.lineChartOptions.chart.backgroundColor = Settings.ChartBGColor;
        if (!this.lineChartObject) {
            this.lineChartObject = new Highcharts.Chart(this.lineChartOptions);
        }
            // redraw chart when chart is existing
        else {
            this.lineChartObject.xAxis[0].update(this.lineChartOptions.xAxis);
            this.lineChartObject.yAxis[0].update(this.lineChartOptions.yAxis);
            this.lineChartObject.redraw();
        }
    },

    /* function:    rebuildxAxis 
    ** description: need update axis when chart has new data
    */
    rebuildxAxis: function (data) {
        // make xaxis
        var startTime = new Date();
        var endTime = new Date();
        var dataLength = 0;
        for (var i = 0; i < data.length; i++) {
            if (data[i].Data.length > 0) {
                dataLength = data[i].Data.length;
                startTime = Utility.convertToServerTimezone(data[i].Data[0].Time.toDate());
                endTime = Utility.convertToServerTimezone(data[i].Data[dataLength - 1].Time.toDate());
                break;
            }
        }
        this.isLiveMode = ((Settings.SelectedPeriod.toLowerCase() == 'live')
                        || ((Utility.dateDiff(startTime, endTime) == 0) && dataLength > 1));

        // rebuild xAxis with time return from data
        var openTime = new Date(startTime.getFullYear()
                                , startTime.getMonth()
                                , startTime.getDate()
                                , Settings.OpenTime.Hours
                                , Settings.OpenTime.Minutes, 00).getTime(),
            closeTime = new Date(startTime.getFullYear()
                                , startTime.getMonth()
                                , startTime.getDate()
                                , Settings.CloseTime.Hours
                                , Settings.CloseTime.Minutes, 00).getTime();

        var documentWidth = $(document).width();

        var xAxis = {
            min: openTime,
            max: closeTime,
            tickInterval: (documentWidth <= 480 ? 120 * 60 * 1000 : 60 * 60 * 1000),
            labels: {
                useHTML: true,
                formatter: function () {
                    if (this.value == openTime) {
                        return '<div class="label_first" style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">' + Utility.GetNumber($.format.date(new Date(this.value), Settings.shortTime)) + '</div>';
                    }
                    else if (this.value == closeTime) {
                        return '<div class="label_last" style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">' + Utility.GetNumber($.format.date(new Date(this.value), Settings.shortTime)) + '</div>';
                    }
                    else {
                        return '<div style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">' + Utility.GetNumber($.format.date(new Date(this.value), Settings.shortTime)) + '</div>';
                    }
                }
            }
        }

        // set options to livechart
        if (this.isLiveMode) {
            LiveChart.lineChartOptions = $.extend(true, LiveChart.lineChartOptions, { 'xAxis': xAxis, 'yAxis': { tickInterval: null } });
        }
        else {
            LiveChart.lineChartOptions = $.extend(true, LiveChart.lineChartOptions,
                    {
                        'xAxis': {
                            min: null
                                  , max: null
                                  , tickInterval: null
                                  , labels: {
                                      useHTML: true
                                            , formatter: function () {
                                                if (Settings.SelectedPeriod.toLocaleLowerCase() == 'custom') {
                                                    if (Math.abs(endCal.selection.getDates()[0] - startCal.selection.getDates()[0]) >= Utility.getTickOfCurrentYear() * 5) {
                                                        return '<div style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">' + Utility.GetNumber($.format.date(new Date(this.value), Utility.getXAxisDateFormat(3))) + '</div>';
                                                    }
                                                    else if (Math.abs(endCal.selection.getDates()[0] - startCal.selection.getDates()[0]) >= Utility.getTickOfCurrentYear()
                                                  && Math.abs(endCal.selection.getDates()[0] - startCal.selection.getDates()[0]) < Utility.getTickOfCurrentYear() * 5) {
                                                        return '<div style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">' + Utility.GetNumber($.format.date(new Date(this.value), Utility.getXAxisDateFormat(1))) + '</div>';
                                                    }
                                                    else {
                                                        return '<div style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">' + Utility.GetNumber($.format.date(new Date(this.value), Utility.getXAxisDateFormat(2))) + '</div>';
                                                    }
                                                }
                                                else {
                                                    return '<div style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">' + Utility.GetNumber($.format.date(new Date(this.value), Utility.getXAxisDateFormat(2))) + '</div>';
                                                }
                                            }
                                  }
                        },
                        'yAxis': { tickInterval: null }
                    });
        }
    },

    /**/
    destroy: function () {
        this.lineChartObject && $.isFunction(this.lineChartObject.destroy) && this.lineChartObject.destroy();
        this.lineChartObject = null;
    }
};

/* region: TableOfInstrument */
var TableOfInstrument = {
    createShareInfo: function (data) {
        var me = this;
        // display date and time
        var strCurrentDateAndTime,
                    rightNow = new Date();
        strCurrentDateAndTime = $.format.date(Utility.toPageTimezone(rightNow), 'dddd') + ', ' +
                            $.format.date(Utility.toPageTimezone(rightNow), Settings.longDateFormat) + ' ' +
                            $.format.date(Utility.toPageTimezone(rightNow), Settings.longTime) + ' ' +
                            '(GMT+' + Settings.pagetimezone + ')';
        if (Utility.isRtl())
            strCurrentDateAndTime = String.toArabicNumber(strCurrentDateAndTime);
        $('.date-time-heading').find('.time').html(strCurrentDateAndTime);

        // generating table of instrument
        $('.share-info-body').html('');
        $.each(data, function (i, val) {
            me.addToTableShare(val);
        });
    },
    /*
    * Method: addToTableShare
    * Description: add data to html at table share NOTE
    */
    addToTableShare: function (dataLatestTrading) {
        var tableShareTemplate = $('.table-share-info-temp tbody').html();
        var changePercentCss = '';
        if (dataLatestTrading.Change > 0)
            changePercentCss = 'increase-value';
        else
            if (dataLatestTrading.Change < 0)
                changePercentCss = 'decrease-value';
        var dateTimeZone = Utility.convertToServerTimezone(dataLatestTrading.Time.toDate());
        var datetimeFormat = Utility.displayDate(dateTimeZone);
        /*var changeValue = dataLatestTrading.Change > 0 ?
        '+' + $.format.number(dataLatestTrading.Change, 'N' + Settings.sharepriceNumberDecimalDigits)
        : $.format.number(dataLatestTrading.Change, 'N' + Settings.sharepriceNumberDecimalDigits);*/
        var changeProValue = /*dataLatestTrading.Change > 0 ?
                        '+' + $.format.number(dataLatestTrading.ChangePro, 'N' + Settings.sharePricePercentageDecimalDigits)
                        :*/$.format.number(dataLatestTrading.ChangePro, 'N' + Settings.sharePricePercentageDecimalDigits);
        var lastValue = $.format.number(dataLatestTrading.Last, 'N' + Settings.sharepriceNumberDecimalDigits);
        var highValue = $.format.number(dataLatestTrading.High, 'N' + Settings.sharepriceNumberDecimalDigits);
        var lowValue = $.format.number(dataLatestTrading.Low, 'N' + Settings.sharepriceNumberDecimalDigits);

        var rowTemplate = String.applyTemplate(tableShareTemplate, {
            InstrumenId: dataLatestTrading.InstrumentID,
            MarketId: dataLatestTrading.MarketId,
            MarketName: dataLatestTrading.MarketName,
            Currency: dataLatestTrading.CurrencyCode,
            Last: Utility.isRtl() == true ? String.toArabicNumber(lastValue) : lastValue,
            /*Change: Utility.isRtl() == true ? String.toArabicNumber(changeValue) : changeValue,*/
            ChangePro: Utility.isRtl() == true ? String.toArabicNumber(changeProValue) : changeProValue,
            High: Utility.isRtl() == true ? String.toArabicNumber(highValue) : highValue,
            Low: Utility.isRtl() == true ? String.toArabicNumber(lowValue) : lowValue,
            Volume: Utility.isRtl() == true ? String.toArabicNumber($.format.number(dataLatestTrading.Volume, 'N0')) : $.format.number(dataLatestTrading.Volume, 'N0'),
            Time: Utility.isRtl() == true ? String.toArabicNumber(datetimeFormat) : datetimeFormat,
            change_percent: changePercentCss
        });

        $('.share-info-body').append($(rowTemplate));
    }
};

/* region: MarketShare */
var MarketShare = {
    /*
    * Method: Create market share table
    */
    createMarketInfo: function (data) {
        var me = this;
        $('.market-info-body').html('');
        var totalVol = 0;
        $.each(data, function (i, val) {
            totalVol = totalVol + val.Volume;
        });
        //sort by Volume
        data = data.sort(function (obj1, obj2) {
            return obj2.Volume - obj1.Volume;
        });
        $.each(data, function (i, val) {
            val.ChangePro = totalVol != 0 ? (val.Volume / totalVol) * 100 : 0;
            me.addToTableMarket(val);
        });
    },
    /*
    * Method: addToTableMarket
    * Description: apply data to html at table market share 
    */
    addToTableMarket: function (dataLatestTrading) {
        var tableMarketTemplate = $('.table-market-share-temp tbody').html();
        var changePro = $.format.number(dataLatestTrading.ChangePro, 'N' + Settings.sharePricePercentageDecimalDigits);
        var rowTempate = String.applyTemplate(tableMarketTemplate, {
            MarketName: dataLatestTrading.MarketName,
            Volume: Utility.isRtl() == true ? String.toArabicNumber($.format.number(dataLatestTrading.Volume, 'N0')) : $.format.number(dataLatestTrading.Volume, 'N0'),
            ChangePro: Utility.isRtl() == true ? String.toArabicNumber(changePro) : changePro
        });
        $('.market-info-body').append($(rowTempate));
    },
    /*
    * Method: apply style for row tables
    */
    initStyleForTableData: function (tableContainer) {
        var i = 0;
        if (!tableContainer || tableContainer.length <= 0) {
            throw "tableContainer is null or empty";
        }
        tableContainer.find('tbody > tr').each(function () {
            var currentClass = $(this).children(':first-child').css('display');
            if (currentClass != 'none') {
                if (i % 2 == 0) {
                    $(this).find('td').addClass('table-share-row-even');
                }
                else {
                    $(this).find('td').addClass('table-share-row-odd');
                }
                i++;
            }
        });
    },
    /* region: Pie chart */
    PieChart: {
        containerId: '',
        pieChartObject: null,
        pieChartOptions: {
            chart: {
                backgroundColor: Settings.ChartBGColor,
                renderTo: '',
                plotBackgroundColor: null,
                plotBorderWidth: null,
                plotShadow: false
            },
            global: {
                useUTC: true
            },
            credits: {
                enabled: false
            },
            legend: {
                enabled: false
            },
            scrollbar: {
                enabled: false
            },
            rangeSelector: {
                enabled: false
            },
            navigator: {
                enabled: false
            },
            exporting: {
                enabled: false
            },
            title: {
                text: ''
            },
            tooltip: {
                useHTML: true,
                formatter: function () {
                    var yVal = Utility.isRtl() ? String.toArabicNumber($.format.number(this.point.y, 'N0')) : $.format.number(this.point.y, 'N0');
                    var tiptext = '<div class="chart-tooltip" style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">'
                              + this.point.name + '</br><b>'
                              + yVal + '</b></div>';
                    var $tmpDiv = $('<div/>').css({
                        'visibility': 'hidden',
                        'white-space': 'nowrap',
                        'width': 'auto',
                        'float': 'left'
                    }).appendTo(document.body).html(tiptext);
                    var bestWidth = $tmpDiv.width() + 30;
                    $tmpDiv.remove();
                    return $('<div/>').append(tiptext).children().width(bestWidth).parent().html();
                },
                percentageDecimals: 1
            },
            plotOptions: {
                pie: {
                    startAngle: $(document).width() <= 320 ? 60 : 45,
                    allowPointSelect: true,
                    cursor: 'pointer',
                    dataLabels: {
                        useHTML: true,
                        enabled: true,
                        color: '#000000',
                        connectorColor: '#787878',
                        formatter: function () {
                            var percentFormat = $.format.number(this.percentage, 'N' + Settings.sharePricePercentageDecimalDigits);
                            var percent = Utility.isRtl() ? String.toArabicNumber(percentFormat) : percentFormat;
                            if ($(document).width() > 420) {
                                return '<div style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">' + this.point.name + ': ' + percent + ' % </div>';
                            }
                            else {
                                return '<div style="color: ' + Settings.fontStyle.color + '; font-family:' + Settings.fontStyle.fontFamily + '; font-size: ' + Settings.fontStyle.fontSize + '">' + this.point.name + ': <br/>' + percent + ' % </div>';
                            }
                        },
                        style: Settings.fontStyle
                    }
                }
            },
            series: [{
                type: 'pie',
                data: []
            }]
        },
        drawChart: function (data) {
            var pieChartOptions;
            if (Settings.pieBorderWidth >= 0)
                pieChartOptions = $.extend(true, { plotOptions: { pie: { borderWidth: Settings.pieBorderWidth } } }, this.pieChartOptions);
            else
                pieChartOptions = $.extend(true, {}, this.pieChartOptions);

            pieChartOptions.chart.renderTo = this.containerId;
            pieChartOptions.chart.backgroundColor = Settings.ChartBGColor;
            if (data && data.length > 0) {
                var dataPieChart = [];
                $.each(data, function (i, val) {
                    var objTicker = this;
                    //if (objTicker.Volume != 0) {
                    dataPieChart.push({
                        name: objTicker.MarketName,
                        y: objTicker.Volume,
                        color: Settings.chartColors[i]
                    });
                    //}
                });
                if (dataPieChart.length > 0) {
                    //sort by Volume
                    dataPieChart.sort(function (obj1, obj2) {
                        return obj2.y - obj1.y;
                    });
                    pieChartOptions.series[0].data = dataPieChart;
                    if (!this.pieChartObject) {
                        this.pieChartObject = new Highcharts.Chart(pieChartOptions);
                    } else {
                        this.pieChartObject.series[0].setData(dataPieChart);
                    }
                }
                else {
                    $('#' + this.containerId).html('<center id="nodata">' + Labels.msgNoDataAvailable + '</center>');
                    return;
                }
            }
            else {
                $('#' + this.containerId).html('<center id="nodata">' + Labels.msgNoDataAvailable + '</center>');
                return;
            }
        },
        destroy: function () {
            this.pieChartObject && $.isFunction(this.pieChartObject.destroy) && this.pieChartObject.destroy();
            this.pieChartObject = null;
        }
    }
};

/* region: ActivityTrend */
function ColumnChart() {

};
ColumnChart.prototype = {
    /*
    * Global variable charts
    */
    charts: [],
    /*
    * Method: prepareChartContainers
    * Description:  building automatic containers for column chart
    */
    prepareChartContainers: function (numberOfChart) {
        if (numberOfChart == 0) return;

        // custom style sheet for each share type
        var typeIndex = $('.share-item:checked').attr('typeIndex'),
            mainChartClass = 'main-activity-chart-' + typeIndex,
            otherChartClass = 'other-activity-chart-' + typeIndex,
            otherChartContainerClass = 'other-activity-chart-container-' + typeIndex;

        var activityChartsContainer = $('#columnChartArea'),
            mainChartContainer = $('<div/>').attr('class', 'main-activity-chart ' + mainChartClass),
            otherChartContainer = $('<div/>').attr('class', 'other-activity-chart-container ' + otherChartContainerClass);

        var idArr = [],
            idPrefix = 'columnChartContainer';
        activityChartsContainer.html('');
        activityChartsContainer.append(mainChartContainer);
        if (numberOfChart > 1) {
            activityChartsContainer.append(otherChartContainer);
        }

        for (var i = 0; i < numberOfChart; i++) {
            var chartId = idPrefix + i;
            idArr.push(chartId);

            if (i === 0) {
                mainChartContainer.attr('id', chartId);
            }
            else {
                var chartContainer = $('<div/>').attr({ 'id': chartId, 'class': 'other-activity-chart ' + otherChartClass });
                otherChartContainer.append(chartContainer);
            }
        }
        activityChartsContainer.append($('<div/>').css({ 'clear': 'both' }));

        return idArr;
    },

    checkEmptyData: function (data) {
        var isEmpty = false;
        for (var i = 0; i < data.length; i++) {
            if (data[i].Volume === 0) {
                isEmpty = true;
                break;
            }
        }
        return isEmpty;
    },

    /*
    * Method: initializeChart
    * Description: 
    */
    initializeChart: function (graphData) {
        var shareCount = 0;
        var me = this;
        if (graphData && $.isArray(graphData)) {

            shareCount = graphData.length;
            if (shareCount > 0) {
                var idContainers = me.prepareChartContainers(shareCount);
                var series = [];
                var opts = {}, nData;

                while (me.charts.length > 0) {
                    this.charts.pop().destroy();
                }

                for (var i = 0; i < shareCount; i++) {
                    if (me.checkEmptyData(graphData[i])) {
                        $('#columnChartContainer' + i).remove();
                    }
                    else {
                        opts = me.getDeafultOptions();
                        opts.chart.renderTo = idContainers[i];
                        nData = me.normalizeData(graphData[i]);
                        if (nData) {
                            nData.series.color = Settings.chartColors[i];
                            if (nData.series.name) {
                                opts.series = [nData.series];
                                opts.xAxis.categories = nData.catergories;
                                opts.title.text = '<div dir="ltr">' + nData.series.name + '</div>';
                                _TotalPageActivity++;
                                me.charts.push(new Highcharts.Chart(opts));
                            }
                        }
                    }
                }
            }
        }
    },

    /*
    * Method: normalizeData
    * Description: 
    */

    normalizeData: function (rawSeriesData) {
        var data = [],
            catergories = [],
            len = rawSeriesData.length;

        for (var i = 0; i < len; i++) {
            if (rawSeriesData[i].Volume != 0) {
                var percent = rawSeriesData[i].TotalVolume != 0 ? (rawSeriesData[i].Volume / rawSeriesData[i].TotalVolume) * 100 : 0;
                data.push(percent);
                catergories.push(rawSeriesData[i].Year);
            }
        }
        var marketName = rawSeriesData[0] ? rawSeriesData[0].MarketName : null;

        if (rawSeriesData[0].Volume != 0)
            return {
                series: {
                    name: marketName,
                    data: data,
                    color: Settings.chartColors[0]
                },
                catergories: catergories
            };
    },

    /*
    * Method: getDeafultOptions
    * Description: 
    */
    getDeafultOptions: function () {
        var fontStyles = $.extend({}, Settings.fontStyle, { fontWeight: 'normal' });
        return {
            chart: {
                backgroundColor: Settings.ChartBGColor,
                defaultSeriesType: 'column',
                style: fontStyles,
                events: {
                    load: function () {
                        _TotalPageActivity--;
                        checkActivity();
                        var isStable = false,
                            count = 0,
                            container = $(this.renderTo),
                            me = this,
                            intervalID = setInterval(function () {
                                var chartWidth = me.chartWidth,
                                    paddingLR = parseInt(container.css('padding-left')) + parseInt(container.css('padding-right')),
									paddingTB = parseInt(container.css('padding-top')) + parseInt(container.css('padding-bottom')),
                                    containerWidth = container.innerWidth() - (paddingLR || 0),
									containerHeight = container.innerHeight() - (paddingTB || 0);
                                if (Math.abs(chartWidth - containerWidth) <= 1) {
                                    count++;
                                }
                                else {
                                    if (typeof (me.reflow) == 'function') {
                                        me.reflow();
                                    }
                                    else {
                                        if (containerWidth > 0 && containerHeight > 0)
                                            me.setSize(containerWidth, containerHeight, false);
                                    }
                                }

                                if (count >= 200) {
                                    isStable = true;
                                }
                                if (isStable) {
                                    clearInterval(intervalID);
                                }
                            }, 100);
                    }
                }
            },
            title: {
                useHTML: false,
                text: null,
                style: fontStyles
            },
            credits: {
                enabled: false
            },
            legend: {
                enabled: false
            },
            exporting: {
                enabled: false
            },
            plotOptions: {
                column: {
                    cursor: 'pointer',
                    /*pointWidth: 18,*/
                    shadow: false,
                    states: {
                        hover: {
                            brightness: 0.3
                        }
                    },
                    dataLabels: {
                        useHTML: true,
                        enabled: true,
                        style: fontStyles,
                        formatter: function () {
                            var percent = $.format.number(this.y, 'N' + Settings.sharePricePercentageDecimalDigits);
                            var yVal = Utility.isRtl() == true ? String.toArabicNumber(percent) + '%' : percent + '%';
                            var tiptext = '<div class="chart-tooltip">'
                              + yVal + '</div>';
                            var $tmpDiv = $('<div/>').css({
                                'visibility': 'hidden',
                                'white-space': 'nowrap',
                                'width': 'auto',
                                'float': Utility.isRtl() == true ? 'right' : 'left'
                            }).appendTo(document.body).html(tiptext);
                            var bestWidth = $tmpDiv.width();
                            $tmpDiv.remove();
                            return $('<div/>').append(tiptext).children().width(bestWidth).parent().html();
                        }
                    }
                }
            },
            xAxis: {
                labels: {
                    formatter: function () {
                        return Utility.isRtl() == true ? String.toArabicNumber(this.value) : this.value;
                    },
                    enabled: true,
                    rotation: 0,
                    style: fontStyles
                },
                title: {
                    text: null,
                    enabled: true,
                    useHTML: true,
                    style: fontStyles
                },
                tickWidth: 0,
                tickmarkPlacement: 'on'
            },
            yAxis: {
                title: {
                    text: null,
                    rotation: 270,
                    style: fontStyles
                },
                labels: {
                    enabled: false
                },
                gridLineWidth: 0
            },
            tooltip: {
                enabled: false,
                formatter: function () {
                    var percent = $.format.number(this.y, 'N' + Settings.sharePricePercentageDecimalDigits);
                    return Utility.isRtl() == true ? String.toArabicNumber(percent) + '%' : percent + '%'
                },
                borderRadius: 1,
                borderColor: '#CCCCCC',
                borderWidth: 1,
                style: fontStyles,
                backgroundColor: 'rgba(255, 255, 255, 1)'
            },
            series: [],
            navigator: {
                enabled: false
            },
            scrollbar: {
                enabled: false
            },
        };
    }
};

var Exporting = {
    ExportType: {
        PDF: 'PDF',
        JPG: 'JPG'
    },
    generateDocument: function (fileType) {        
        //return;
        //Instance the Destination PDf Class, with the URL what procces the data and build the pdf file.
        var pdfdownload = new PdfDownload({
            downloadUrl: Settings.DownloadUrl,
            fileType: fileType.toLowerCase() == 'pdf' ? 'pdf' : 'jpeg',
            documentAttributes: {
                fileName: 'Fragulizer_' + (new Date()).getTime(),
                MarginTop: 15,
                MarginLeft: 10,
                MarginRight: 0,
                MarginBottom: 0
            }
        });

        // pdfdownload.setContent(sentHtml);
        $('#chart-container').find('.highcharts-container').css('margin', '0 auto');
        $('.etooltip').remove();

        pdfdownload.selfPdfDownload(function (doc) {
            if (!doc)
                return;
            var $body = $(doc.body);
            $body.find('.legend-group li').attr('style', 'width: auto; float: left; padding: 0px; margin: 0px;');
            $body.find('.wrapper').addClass('exporting');

            if (fileType.toLowerCase() == 'pdf') {
            	$body.find('.wrapper').addClass('pdf');
            }

            if (fileType.toLowerCase() == 'jpeg') {
            	$body.find('.wrapper').addClass('jpeg');
            }
        });       
        $('#chart-container').find('.highcharts-container').removeCss('margin');
    }
}

/* region: document*/
var startCal = null,
    endCal = null,
    svgColumnCharts = [];

$(document).ready(function () {
    // fix bug cannot download file in IE6
    if ($.browser.msie && $.browser.version <= 6) {
        $('.command-button a').each(function () {
            var me = $(this);
            if (!me.hasClass('print-icon')) {
                me.attr('href', '#');
            }
        });
    }

    // apply style to share type selection
    Utility.styliseHtmlInputControl($('.share-type-selection'));

    //Add event for custom period button
    $('#customperiod').off('click').on('click', function () {
        if (!Utility.isRtl()) {
            $('.custom-range-calendar').css({ 'left': $(this).offset().left + $(this).width() - ($('.custom-range-calendar').width() + 20) });
        }
        else {
            $('.custom-range-calendar').css({ 'left': 10 });
        }
        $('.custom-range-calendar').show();
        if (startCal == null && endCal == null) {
            var currentDate = new Date();
            var minDate = new Date(currentDate.getFullYear() - 10, currentDate.getMonth(), currentDate.getDate());
            var fromDate = new Date(currentDate.getFullYear() - 1, currentDate.getMonth(), currentDate.getDate());
            startCal = Calendar.setup({
                cont: "from_calendar",
                date: Calendar.dateToInt(fromDate),
                selection: Calendar.dateToInt(fromDate),
                min: minDate,
                max: currentDate
            });
            endCal = Calendar.setup({
                cont: "to_calendar",
                selection: Calendar.dateToInt(new Date()),
                min: minDate,
                max: currentDate
            });
        }


    });

    $('.close-icon').off('click')
    .on('click', function () {
        //Hide custom range panel
        $('.custom-range-calendar').hide();
    });

    $('.show-data', $('.calendar-Container'))
    .off('click')
    .on('click', function () {
        var start = startCal.selection.getDates()[0],
            end = endCal.selection.getDates()[0],
            rightNow = new Date();

        if (Utility.dateDiff(start, rightNow) == 0 && Utility.dateDiff(end, rightNow) == 0) {
            $('#periodLive').trigger('click');
            $('.custom-range-calendar').css({ display: 'none' });
            return;
        }

        var startDate = start.toJSON();
        var endDate = end.toJSON();
        Settings.SelectedPeriod = 'custom';
        $('.period-selected').removeClass('period-selected');
        var newParams = {
            firstTime: true,
            instrumentIDs: $('.share-item:checked').attr('instruments'),
            period: 'custom',
            startDate: startDate,
            endDate: endDate,
            OpenTime: null,
            CloseTime: null,
            lang: lang
        };
        LiveChart.destroy();
        LiveChart.lineChartOptions.series = [];

        // update params
        DataCentre.setParams(newParams);
        // get data
        DataCentre.startRequest();

        $('.custom-range-calendar').css({ display: 'none' });
    });

    // bind event to radio in share type selection
    $('.share-item')
    .on('click', function () {
        var instrumentList = $(this).attr('instruments'),
            label = $(this).parents('label'),
            selectedGroup = $('.selected-instrument-group');

        if (label.length > 0 && selectedGroup.length > 0) {
            selectedGroup.text(label.text());
        }

        LiveChart.destroy();
        LiveChart.lineChartOptions.series = [];
        var newParam = {
            instrumentIDs: instrumentList,
            firstTime: true,
            OpenTime: null,
            CloseTime: null,
            lang: lang
        };
        DataCentre.setParams(newParam);
        DataCentre.startRequest();
    });

    // declare option for getting data
    var dataCentreOptions = {
        dataUrl: baseUri + 'Home/GetData',
        latestTradingDataUrl: baseUri + 'Home/GetLatestTradingData',
        params: {
            firstTime: true,
            instrumentIDs: $('.share-item:checked').attr('instruments'),
            period: Settings.SelectedPeriod,
            startDate: null,
            endDate: null,
            OpenTime: null,
            CloseTime: null,
            companyCode: Settings.companyCode,
            lang: lang
        },
        requestInterval: 60,
        fragmentationDataReady: function (data) {
            if (data && data.data.length > 0) {
                //Get first data date to display on title
                var startTime = new Date();
                var endTime = new Date();
                for (var i = 0; i < data.data.length; i++) {
                    if (data.data[i].Data.length > 0) {
                        startTime = Utility.convertToServerTimezone(data.data[i].Data[0].Time.toDate());
                        endTime = Utility.convertToServerTimezone(data.data[i].Data[data.data[i].Data.length - 1].Time.toDate());
                        break;
                    }
                }
                LiveChart.drawChart(data.data);
                $('#live-alert').hide();

                var period = DataCentre.getParams().period;
                period = period.toUpperCase();

                if (!data.isRetrieveLiveData) {
                    if (data.message != '') {
                        $('#live-alert').html('* ' + (Utility.isRtl() ? Utility.GetNumber(data.message) : data.message));
                        $('#live-alert').show();
                        var selectionFrag = $('.fragmentation-title');
                        var selectionMarket = $('.market-share-heading');
                        $('.selected-period', selectionFrag).html(Labels.lastupdate + ': ' + Utility.GetNumber($.format.date(startTime, Settings.dateFormat)));
                        $('.selected-period', selectionMarket).html('(' + Utility.GetNumber($.format.date(startTime, Settings.dateFormat)) + ')')
                    }

                    DataCentre.cancelIntervalRequest();
                    var requestParams = DataCentre.getParams();


                    if (period != 'LIVE') {
                        var endTime = new Date(),
                        startTime = new Date();

                        if (period == '1M') {
                            startTime.setMonth(startTime.getMonth() - 1);
                        }
                        else if (period == '3M') {
                            startTime.setMonth(startTime.getMonth() - 3);
                        }
                        else if (period == '6M') {
                            startTime.setMonth(startTime.getMonth() - 6);
                        }
                        else if (period == '1Y') {
                            startTime.setFullYear(startTime.getFullYear() - 1);
                        }
                        else if (period == 'CUSTOM') {
                            //startTime = startCal.selection.getDates()[0];
                            //endTime = endCal.selection.getDates()[0];
                            startTime = Date.parseIsoDate(requestParams.startDate);
                            endTime = Date.parseIsoDate(requestParams.endDate);
                        }

                        $('.selected-period').html('(' + Utility.GetNumber($.format.date(startTime, Settings.dateFormat) + ' - ' + $.format.date(endTime, Settings.dateFormat)) + ')')
                    }
                    else {
                        var selectionFrag = $('.fragmentation-title');
                        var selectionMarket = $('.market-share-heading');
                        $('.selected-period', selectionFrag).html(Labels.selectedperiod + ': ' + Utility.GetNumber($.format.date(startTime, Settings.dateFormat)));
                        $('.selected-period', selectionMarket).html('(' + Utility.GetNumber($.format.date(startTime, Settings.dateFormat)) + ')');
                    }
                }
                else {
                    if (period == 'live') {
                        var selectionFrag = $('.fragmentation-title');
                        var selectionMarket = $('.market-share-heading');
                        $('.selected-period', selectionFrag).html(Labels.selectedperiod + ': ' + Utility.GetNumber($.format.date(startTime, Settings.dateFormat)));
                        $('.selected-period', selectionMarket).html('(' + Utility.GetNumber($.format.date(startTime, Settings.dateFormat)) + ')');
                    }
                }
            }

            // legend
            var typeIndex = $('.share-item:checked').attr('typeIndex'),
                chartLegendClass = 'legend-group legend-group-' + typeIndex;
            $('.legend-group').attr('class', chartLegendClass);
        },
        marketShareDataReady: function (data) {
            if (data) {
                var typeIndex = $('.share-item:checked').attr('typeIndex'),
                pieChartClass = 'pie-chart-' + typeIndex;
                $('#pie-chart').attr('class', pieChartClass);

                MarketShare.PieChart.destroy();
                MarketShare.PieChart.containerId = 'pie-chart';
                MarketShare.PieChart.drawChart(data);

                MarketShare.createMarketInfo(data);
                var tableMarketShare = $('.table-market-share');
                MarketShare.initStyleForTableData(tableMarketShare);
            }
        },
        activityTrendDataReady: function (data) {
            if (Settings.enableActivityTrend) {
                $('.activity-trend').show();
                var colChart = new ColumnChart();
                colChart.initializeChart(data);
            }

        },
        latestTradingDataReady: function (data) {
            var msg = '';
            if (data && Settings.enableInstrumentsTable) {
                TableOfInstrument.createShareInfo(data.data);
                var tableInstrumentContainer = $('.table-share-info');
                MarketShare.initStyleForTableData(tableInstrumentContainer);
                if (typeof (data.message) == 'string' && data.message.length > 0)
                    msg = '* ' + data.message;
            }

            $('.latest-trading-notice').html(msg);
        }
    };
    // set option
    DataCentre.setOptions(dataCentreOptions);
    // get data
    $('.fragmentation, .market-share-pie-chart, .activity-trend, .footer').show('1000');

    // refresh data each 60 seconds
    // TODO: need check Live data
    setTimeout(function () {
        if (Settings.SelectedPeriod.toLowerCase() == 'live') {
            DataCentre.startIntervalRequest();
        }
    }, 1000);

    // Auto update table of instrument every 60 seconds 
    var autoInterval = null;
    function autoUpdateTableOfInstrument() {
        autoInterval = setInterval(function () {
            var currentDate = Utility.toPageTimezone(new Date()),
                currentHours = currentDate.getHours(),
                currentMinutes = currentDate.getMinutes(),
                currentTotalMinutes = (currentHours * 60) + currentMinutes,
                openTime = Settings.OpenTime,
                closeTime = Settings.CloseTime;

            if (openTime && closeTime) {
                if (currentTotalMinutes >= openTime.TotalMinutes
                    && currentTotalMinutes <= closeTime.TotalMinutes) {
                    DataCentre.getLatestTradingData();
                }
            }

        }, 60000);
    }
    // start update table of instrument
    autoUpdateTableOfInstrument();

    // bind click event to change period
    $('.period-button')
    .off('click')
    .on('click', function () {
        // clear old selected
        $('.period-selected').removeClass('period-selected');
        // set new selected
        $(this).addClass('period-selected');

        // set new params to get data
        // KHANH: please check this
        //var content = $(this).html();
        //Settings.SelectedPeriod = content.replace(' ', '').toLowerCase();

        var period = $(this).attr('period');
        period = period.toLowerCase();

        var newParams = {
            period: period,
            OpenTime: Settings.OpenTime.Hours + ':' + Settings.OpenTime.Minutes,
            CloseTime: Settings.CloseTime.Hours + ':' + Settings.CloseTime.Minutes,
            lang: lang
        };
        // update params
        DataCentre.setParams(newParams);
        // get data
        DataCentre.startRequest();
        // clear auto interval update table of instrument
        autoInterval && clearInterval(autoInterval);
        if (period != 'live') {
            DataCentre.cancelIntervalRequest();
            autoUpdateTableOfInstrument();
        }
        else {
            var newParam = {
                firstTime: false,
                OpenTime: Settings.OpenTime.Hours + ':' + Settings.OpenTime.Minutes,
                CloseTime: Settings.CloseTime.Hours + ':' + Settings.CloseTime.Minutes,
                lang: lang
            };
            DataCentre.setParams(newParam);
            DataCentre.startIntervalRequest();
        }

        if (Settings.SelectedPeriod.toLowerCase() == 'live') {
            LiveChart.destroy();
            LiveChart.lineChartOptions.series = [];
        }
    });

    // Hide custom period box when click outer
    $(document).on('mouseup.custom-range-calendar', function (e) {
        // for firefox
        var check = true;

        if ($(e.target).closest('.datepicker').length
        || $(e.target).closest('.custom-range-title').length) {
            check = false;
        }

        if (!$(e.target).closest('.custom-range-calendar').length
        && !$(e.target).closest('.custom-range-calendar-header').length
        && $('#custom-range-calendar').is(':visible')
        && check) {
            $('#custom-range-calendar').hide();
        }
    });

    // bind event for jpg-icon 
    /*$('.jpg-icon').unbind('click').bind('click', function (e) {
        e.preventDefault();
        Exporting.generateDocument(Exporting.ExportType.JPG);
    });

    $('.pdf-icon').unbind('click').bind('click', function (e) {
        e.preventDefault();
        Exporting.generateDocument(Exporting.ExportType.PDF);
    });
    */

    $('.excel-icon').on('click', function (e) {
    	e.preventDefault();
    	var requestParams = DataCentre.getParams();
        var url = baseUri + 'Export/Excel',
        shareName = $.trim($('.radio-checked').closest('label').text());
    	instrumentIDs = requestParams.instrumentIDs,
        period = requestParams.period,
        startDate = requestParams.period == "custom" ? requestParams.startDate : null,
        endDate = requestParams.period == "custom" ? requestParams.endDate : null;

        if (/iPad/i.test(navigator.userAgent)) {
            var params = '&shareName=' + shareName + '&instrumentIDs=' + instrumentIDs + '&period=' + period + '&startDate=' + startDate + '&endDate=' + endDate;
            url += '?lang=' + lang + '&langShort=' + langShort + '&companycode=' + Settings.companyCode;
            window.open(url + params, '_blank');
        }
        else {
            var $formTemp = $('<form/>');
            $formTemp.attr({
                action: url,
                method: 'GET'
            }).appendTo(document.body);

            // [Sep 09, 2016]
            // Form does not accept querystring inside "action" attribute
            // Therefore, must append lang, companycode values to the hidden fields
            // of form
            $('<input type="hidden" name="lang"/>').val(lang).appendTo($formTemp);
            $('<input type="hidden" name="companycode"/>').val(Settings.companyCode).appendTo($formTemp);
            $('<input type="hidden" name="langShort"/>').val(langShort).appendTo($formTemp);

            $('<input type="hidden"/>')
            .attr('name', 'shareName')
            .val(shareName)
            .appendTo($formTemp);

            $('<input type="hidden"/>')
            .attr('name', 'instrumentIDs')
            .val(instrumentIDs)
            .appendTo($formTemp);

            $('<input type="hidden"/>')
            .attr('name', 'period')
            .val(period)
            .appendTo($formTemp);

            $('<input type="hidden"/>')
            .attr('name', 'startDate')
            .val((startCal != null) ? startCal.selection.getDates()[0].toJSON() : null)
            .appendTo($formTemp);

            $('<input type="hidden"/>')
            .attr('name', 'endDate')
            .val((endCal != null) ? endCal.selection.getDates()[0].toJSON() : null)
            .appendTo($formTemp);

            $formTemp.submit().remove();
        }
    });

    //Replace footer command image by css background-image attribute
    $('.command-button a').each(function () {
        var cssImage = $(this).css('background-image')
            .replace('url(', '').replace(')', '')
            .replace(/'/gi, "")
            .replace(/"/gi, "");
        if (cssImage && cssImage != 'none') {
            $('img', this).attr('src', cssImage);
            $(this).css('background-image', 'none');
        }
    });

    //Bind event for footer icon
    $('.command-button a').etip({
        followCursor: true,
        //showOn: 'hover',
        allowTipHover: true,
        alignX: 'left',
        alignY: 'top',
        alignTo: 'cursor',
        hideTimeout: 0,
        hideAniDuration: 0,
        opacity: .8,
        beforePosition: function () {
            var api = $.data(this, 'etip');
            api && api.$arrow && api.$arrow.hide();
        }
    }).children('img').hover(function () {
        var scale = .75,
            time = 200,
            framePauseTime = Math.ceil(1e3 / 24),
            scaleJump = Math.abs((1 - scale) / (time / framePauseTime)),
            width = 32, height = 32, margin = 0, sender = $(this), maxWidth = 43;

        var interval = setInterval(function () {
            if (width < maxWidth) {
                scale = scale + scaleJump;
                width = Math.floor(maxWidth * scale);
                height = Math.floor(maxWidth * scale);
                margin = Math.round((maxWidth - height) / 2)
            }
            else {
                width = maxWidth;
                height = maxWidth;
                clearInterval(interval);
            }
            $(sender).css({ width: width + 'px', height: height + 'px', margin: margin + 'px', opacity: scale })
        }, framePauseTime)
    }, function () {
        var scale = 1,
            time = 200,
            framePauseTime = Math.ceil(1e3 / 24),
            scaleJump = Math.abs((1 - .75) / (time / framePauseTime)),
            width = 43, height = 43, margin = 0, sender = $(this), minWidth = 32, maxWidth = 43;

        var interval = setInterval(function () {
            if (width > minWidth) {
                scale = scale - scaleJump;
                width = Math.floor(maxWidth * scale);
                height = Math.floor(maxWidth * scale);
                margin = Math.round((maxWidth - height) / 2)
            }
            else {
                width = minWidth, height = minWidth;
                clearInterval(interval);
            }
            $(sender).css({ width: width + 'px', height: height + 'px', margin: margin + 'px', opacity: scale })
        }, framePauseTime)
    });


    var hash = document.location.hash.replace(/^[#]\/?/, '');
    function toCamelCase(str) {
        return (str || '').replace(/(\w)/, function (str, capture) { return capture.toLowerCase(); });
    }
    function queryStringToObject(str) {
        return (str || document.location.search).replace(/(^\?)/, '').split("&").map(function (n) { return n = n.split("="), this[toCamelCase(n[0])] = n[1], this }.bind({}))[0];
    }

    // Set request parameters at the page load which are got from hash query-string
    // Used for export to JPEG and PDF function
    if (hash.length > 0 && $('#wrapper').hasClass('exporting')) {
        _IsExporting = true;
        var dataUrl = baseUri + hash;
        var fakeHyperlink = document.createElement('a');
        fakeHyperlink.href = dataUrl;

        var queryObj = queryStringToObject(fakeHyperlink.search.replace(/^\?/, ''));

        DataCentre.setParams(queryObj);
        // Try to update share selection and period manually
        //period-selection
        //share-type-selection
        $('.share-type-selection:first').find(':radio').each(function () {
            this.checked = false;
            $(this).parent().removeClass('radio-checked');
        })
        .filter('[instruments="' + queryObj.instrumentIDs + '"]')
        .attr('checked', 'checked')
        .parent().addClass('radio-checked');

        $('.period-selection')
        .find('a[period]').removeClass('period-selected')
        .filter('[period="' + (queryObj.period || '').toUpperCase() + '"]').addClass('period-selected');
    }

    DataCentre.startRequest();
});

var originalOnload = window.onload;
window.onload = function (e) {
    $.isFunction(originalOnload) && originalOnload.apply(this, arguments);
    if (typeof (EurolandToolAutoSizeObject) !== 'undefined') {
        EurolandToolAutoSizeObject.load('wrapper');
    }
};