/*#REGION common */
body
{
    color: #111111;
    font-family: <PERSON><PERSON>;
    font-size: 16px;
    padding: 0px;
    margin: 0px;
}

a img
{
    border: none;
}

.radio
{
    border: none;
    line-height: 18px;
    display: inline-block;
    background: transparent url('Images/18x18_38x38.png') 0px 0px;
}

.radio input, .checkbox input
{
    padding: 0;
    margin: 0;
    width: 18px;
    height: 18px;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
    -moz-opacity: 0;
    -khtml-opacity: 0;
    opacity: 0;
    vertical-align: middle;
}

.radio-checked
{
    background-position: -20px 0px;
}

.clear
{
    clear: both;
}

.display-none
{
    display: none;
}

.pisition-relative
{
    position: relative; /*height: auto !important; /* IE6 */ /*zoom: 1 !important; /* IE6 */
    _height: auto !important; /* IE7 */
    _zoom: 1 !important; /* IE7 */
}

.position-absolute
{
    position: absolute;
}


/*#REGION tool common */

.wrapper
{
   
}

.selected-instrument-group
{
    display: none;    
}

.table-header
{
    background-color: #fafafa;
    border: solid 1px #CCC;
    border-left: none;
    border-right: none;
    line-height: 30px;
    padding-left: 10px;
    padding-right: 10px;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.table-row
{
    line-height: 30px;
    padding: 0 10px;
}


/*#REGION share-selection section */
.share-type-selection .share-type-header
{
    text-align: left;
    border: solid 1px #CCC;
}

body.body-rtl .share-type-selection .share-type-header
{
    text-align: right;
}

.share-type-selection .share-type-item
{
    margin-top: 5px;
}

body.body-rtl .share-type-item
{
    text-align:right;
}

/*#REGION  table-of-instrument section */
.date-time-heading
{
    line-height: 30px;
    padding-bottom: 5px;
    padding-top: 15px;
    text-align: right;
}

body.body-rtl .date-time-heading
{
    text-align: left;
}

/* table-of-instruments header */
/* first column: exchange */
.td-exchange-header
{
    border-left: 1px solid #ccc;
}

body.body-rtl .td-exchange-header
{
    border-right: solid 1px #CCC;
    border-left: none;
    text-align: right;
}

/* nested in exchange column */
.share-currency
{
    display: none;
}

/* columns in midle */
.td-currency-header, .td-last-header, .td-change-header, .td-high-header, .td-low-header, .td-volume-header
{
    text-align: right;
}

body.body-rtl .td-currency-header, body.body-rtl .td-last-header, body.body-rtl .td-change-header, body.body-rtl .td-high-header, body.body-rtl .td-low-header, body.body-rtl .td-volume-header
{
    text-align: left;
}

body.body-rtl .td-market-name
{
    text-align: right;
}

/* last column: time */
.td-time-header
{
    border-right: 1px solid #ccc;
    text-align: right;
    /*padding-right:3px;*/
}

body.body-rtl .td-time-header
{
    border-left: solid 1px #CCC;
    text-align: left;
    border-right: none;
}

/* table-of-instruments row */
.td-currency, .td-last, .td-change, .td-high, .td-low, .td-volume
{
    text-align: right;
}

body.body-rtl .td-currency, body.body-rtl .td-last, body.body-rtl .td-change, body.body-rtl .td-high, body.body-rtl .td-low, body.body-rtl .td-volume
{
    text-align: left;
}

.td-time
{
    text-align: right;
}

body.body-rtl .td-time
{
    text-align: left;
}


/*#REGION market share section */
/* heading of section */
.market-share-heading
{
    padding-bottom: 10px;
}

body.body-rtl .market-share-heading
{
    text-align:right;
}

.market-share-heading .title
{
    font-size: 22px;
}

.td-message-no-data
{
    text-align:center;
}

/* table-of-market-share */
.market-share-container
{
    white-space: nowrap;
}
.market-share,
.pie-chart-container
{
    display: inline-block;
    *display: inline;
    *zoom: 1;
    height: 250px;
    *height: 250px;
}
.market-share
{
    margin-right: 5px;
    
}
.pie-chart-container
{
    position: relative;
    width: 400px;
    *width: 400px;    
}

.table-market-share tbody tr:hover .table-row
{
    background-color: #f5f5f5;
}

/* header */
.table-of-market-share-header
{
    padding: 0 10px;
}

.table-market-share .td-exchange-header
{
    text-align: left;
    padding-left: 10px;
}

body.body-rtl .table-market-share .td-exchange-header
{
    text-align: right;
    padding-right: 10px;
}

.table-market-share .td-volume-header
{
    text-align: right;
}

body.body-rtl .table-market-share .td-volume-header
{
    text-align: left;
}

.td-changepro-header
{
    border-right: 1px solid #ccc;
    text-align: right;
    padding-right: 10px;
}

body.body-rtl .table-market-share .td-changepro-header
{
    border-left: 1px solid #ccc;
    text-align: left;
    border-right: none;
    padding-left: 10px;
}

/* rows */
.market-share-row
{
    padding: 0 10px;
}

.table-market-share .td-exchange
{
    text-align: left;
    padding-left: 10px;
}

body.body-rtl .table-market-share .td-exchange
{
    text-align: right;
    padding-right: 10px;
}

.table-market-share .td-changepro
{
    text-align: right;
    padding-right: 10px;
}

body.body-rtl .table-market-share .td-changepro
{
    text-align: left;
    padding-left: 10px;
}

.table-market-share .td-volume
{
    text-align: right;
}

body.body-rtl .table-market-share .td-volume
{
    text-align: left;
}

/* pie chart */

#pie-chart
{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
    *top:-20px;
}

.main-market-chart
{
    float: left;
    padding-top: 30px;
}

body.body-rtl .main-market-chart
{
    float: right;
}

.orther-market-chart
{
    float: left;
}

body.body-rtl .orther-market-chart
{
    float: right;
}


/*#REGION fragmentation */
.fragmentation-title-text
{
}

.fragmentation-period
{
}

#chart-container
{
    height: 200px;
}

#legend-info
{
    margin: 0 45px;
}

body.body-rtl #legend-info
{
    margin: 0 10px 0 0;
}

#legend-info li
{
    float: left;
    /*min-width: 140px;*/
}

.legend-group
{
    width: 100%;
    text-align: left;
}

.legend-color
{
    width: 12px;
    height: 12px;
}

.legend-name
{
    margin: 0 50px 0 5px;
    white-space: nowrap;
    overflow: hidden;
}

body.body-rtl .legend-name
{
    margin: 0 5px 0 50px;
}

.fragmentation
{
    margin-top: 40px;
}

.fragmentation-heading
{
    clear: both;
    width:100%;
}

.fragmentation .title
{
    font-size: 22px;
}

.selected-period
{
    white-space: nowrap;
}

.fragmentation .period-title
{
    line-height: 30px;
    text-align: right;
}

#customperiod
{
    color:inherit;
}

.fragmentation .period-title a
{
    text-decoration: none;
}

.custom-range-calendar
{
    display: none;
    position: absolute;
    border: solid 2px #172854;
    z-index: 1000;
    background-color: #FFF;
    min-width: 420px;
    padding: 3px;
}

.custom-range-calendar-header
{
    position: relative;
    border-bottom: solid 2px gray;
    line-height: 23px;
    height: 23px;
}

.custom-range-title
{
    float: left;
    padding: 3px;
    line-height: 15px;
}

.close-icon
{
    background-color: #CCCCCC;
    cursor: pointer;
    float: right;
    font-weight: bold;
    height: 14px;
    line-height: 14px;
    margin: 3px 3px 0;
    text-align: center;
    vertical-align: bottom;
    width: 14px;
    z-index: 999999;
}

.fragmentation-title
{
    float: left;
}

body.body-rtl .fragmentation-title
{
    float: right;
    text-align:right;
}

.period-selection
{
    float: right;
}

body.body-rtl .period-selection
{
    float: left;
}

body.body-rtl .fragmentation .period-title
{
    text-align: left;
}

.fragmentation ul
{
    list-style-type: none;
    padding: 0px;
    margin: 0px;
    text-align: right;
    float: right;
}

body.body-rtl .fragmentation ul
{
    text-align: left;
    float: left;
    padding: 0px;
    margin: 0px;
}

.period-container
{
    float: left;
    width: 100%;
}

.fragmentation li
{
    float: left;
}

.fragmentation .period-button
{
    line-height: 28px;
    display: block;
    background-color: #fafafa;
    border: solid 1px #CCC;
    -moz-border-radius: 3px;
    border-radius: 3px;
    min-width: 35px;
    padding: 0px 10px;
    text-align: center;
    color: inherit;
    text-decoration: none;
    margin-left: 10px;
}

.show-data:hover
{
    color: #cc0000;
    cursor: pointer;
}

.show-data
{
    background-color: #FFFFFF;
    border: 1px solid #FFFFFF;
    color: #172854;
    font-size: 12px;
    line-height: 15px;
    padding: 0 20px;
}

body.body-rtl .fragmentation .period-button
{
    margin-left: 0px;
    margin-right: 10px;
}

.live-alert
{
    font-size: 90%;
    margin: 0 45px 10px;
}

.latest-trading-notice
{
    font-size: 90%;
}

.market-share-pie-chart
{
    margin-top:40px;
}

/*#REGION activity trend */
.activity-trend
{
    display: none;
}

.activity-trend-heading
{
    padding-bottom: 10px;
}

body.body-rtl .activity-trend-heading
{
    text-align:right;    
}

.activity-trend-heading .title
{
    font-size: 22px;
}

.activity-trend-heading .text
{
    color: #787878;
}

#columnChartArea
{
    width: 768px;
}

.main-activity-chart
{
    float: left;
    height: 250px;
    margin-right: 5%;
    margin-top: 70px;
    width: 35%;
}

body.body-rtl .main-activity-chart
{
    float: right;
    margin-right: 0px;
}

.other-activity-chart-container
{
    float: left;
    width: 60%;
    padding:0;
}

body.body-rtl .other-activity-chart-container
{
    float: left;
}

.other-activity-chart
{
    float: left;
    height: 200px;
    margin: 5%;
    width: 40%;
}

a img
{
    border: none;
}

.increase-value
{
    color: #44AA43;
}

.decrease-value
{
    color: #E3433E;
}

.loading
{
    background-image: url('images/loading.gif');
    background-position: center;
    background-repeat: no-repeat;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 30px;
}

.label_first
{
    text-align: right;
    width: 60px;
}

.label_last
{
    text-align: left;
    width: 60px;
}

.period-selected
{
    background-color: #CCC !important;
    border-color: #AAA !important;
}

.main-heading
{
    margin-bottom: 22px;
    font-weight: bold;
}

body.body-rtl .main-heading
{
    text-align: right;    
}

.table-share-info tbody tr:hover .table-row, .table-share-info tbody tr:hover > th
{
    background-color: #f5f5f5;
}


/*#REGION: footer info */
.footer-info
{
    text-align: right;
}

body.body-rtl .footer-info
{
    text-align: left;
}

.command-button
{
    line-height: 35px;
    text-align: right;
}

body.body-rtl .command-button
{
    text-align: left;
}

.command-button a
{
    background-position: center center;
    background-repeat: no-repeat;
    display: inline-block;
    height: 44px;
    width: 44px;
    text-decoration:none;
    margin-left:0px;
}

body.body-rtl .command-button a
{
    margin-right: 0px;
}
.print-icon
{
    background-image: url("images/print.png");
}

.zh-cn .print-icon
{
    background-image: url('images/ChinesePrint.png');
}

.zh-tw .print-icon
{
    background-image: url('images/PrintTWS.png');
}

.excel-icon
{
    background-image: url('images/xls.png');
}

.zh-tw .excel-icon, .zh-cn .excel-icon
{
    background-image: url('images/ChinesXLSs.png');
}

.pdf-icon
{
    background-image: url('images/pdf.png');
}

.zh-tw .pdf-icon, .zh-cn .pdf-icon
{
    background-image: url('images/Chinesepdf.png');
}

.jpg-icon
{
    background-image: url('images/jpg.png');
}

/*#REGION: reduce calendar spacing */
.td-calendar-spearator
{
    border-left: solid 1px gray;
}

.EUCalendar, .EUCalendar table
{
    font-size: 10px;
}

.EUCalendar-dayNames
{
    padding: 0;
}

.EUCalendar-body
{
    padding: 0;
}

.EUCalendar-animBody-back
{
    top: 0;
}

.EUCalendar-animBody-fwd
{
    top: 0;
}

.EUCalendar-animBody-now
{
    top: 0;
}

.EUCalendar-first-col
{
    padding-left: 0;
}

.EUCalendar-last-col
{
    padding-right: 0;
}

.EUCalendar-weekNumber
{
    margin-right: 0;
    padding-right: 4px !important;
}

.EUCalendar-dayNames div, .EUCalendar-day, .EUCalendar-weekNumber
{
    padding: 1px 2px;
}

.EUCalendar-menu-year
{
    font-size: 12px;
}

.EUCalendar-hover-date
{
    padding: 0px 1px;
}

.EUCalendar-day-selected
{
    padding: 0px 1px;
}

.EUCalendar-menu table td div
{
    padding: 2px 4px;
}

.EUCalendar-menu table td div.EUCalendar-hover-navBtn
{
    padding: 1px 3px;
}

.EUCalendar
{
    border: 1px solid #FFF;
    box-shadow: 0 1px 5px #FFF;
    ms-box-shadow: 0 1px 5px #FFF;
    -moz-box-shadow: 0 1px 5px #FFF;
    -webkit-box-shadow: 0 1px 5px #FFF;
    -moz-user-select: none;
    -webkit-user-select: none;
}

.EUCalendar-topBar
{
    background: #FFF;
}

.EUCalendar-bottomBar
{
    background: #FFF;
}

.EUCalendar-day-selected
{
    background-color: #172854 !important;
    color: White !important;
}

.EUCalendar-bottomBar-today
{
    text-transform: lowercase;
}

.EUCalendar-bottomBar-today:first-letter
{
    text-transform: capitalize;
}


/*#REGION viewport */

/*#viewport pc browser: 768*/
@media only screen and (max-width: 768px)
{
    .wrapper
    {
        width: 100% !important;
    }
    
    /* table of instrument*/
    .td-high-header, .td-low-header, .td-high, .td-low
    {
        display: none;
    }
    
    .market-share-container
    {
        width: 100%;
        padding: 0;
        margin: 0;    
    }
        
    #columnChartArea
    {
        width: 100% !important;
    }
}

@media only screen and (max-width: 640px)
{
     /* market share */
    .market-share
    {
        width: 100% !important;
        display: block !important;
    }
    
    .pie-chart-container
    {
        width: 100% !important;
        padding-left:0px;
        display: block !important;
    }
    
    #pie-chart
    {
        width:100%;
    }
}

/*#ipad - orientation: portrait*/
@media only screen and (device-width: 768px) and (device-height: 1024px) and (orientation:portrait)
{
    .wrapper
    {
        width: 100%;
    }
    
    /* table of instrument*/
    .td-high-header, .td-low-header, .td-high, .td-low
    {
        display: none;
    }

    /* activity trend */
    body.body-rtl .main-activity-chart
    {
        float: right;
        margin-left: 0px;
    }
}

@media only screen and (max-width: 480px)
{
    .wrapper
    {
        width: 100%;
    }
    
    /* table of instrument */
    .td-change-header, .td-high-header, .td-low-header, .td-change, .td-high, .td-low, .td-currency, .td-currency-header
    {
        display: none;
    }
    .share-currency
    {
        display: inline;
    }
    /* begin table market share */
    .market-share
    {
        width: 100%;
        display: block;
    }
    .table-market-share
    {
        width:100%;
    }
    .fragmentation-title-text
    {
    }

    .fragmentation-period
    {
    }

    /* begin column chart */
    .pie-chart-container
    {
        width: 100%;
        padding-left:0px;
        display: block;
    }
    .main-activity-chart
    {
        width: 70%;
        margin: 5% 15%;
    }
    body.body-rtl .main-activity-chart
    {
        margin-right: 80px;
    }
    .other-activity-chart-container
    {
        width: 90%;
        padding: 5%;
    }
    
    .other-activity-chart
    {
        width: 40%;
    }

    .fragmentation .period-button
    {
        margin: 0px;
        display: inline-block;
        padding: 4px 6px;
        margin-bottom: 0;
        font-size: 14px;
        line-height: 20px;
        color: #333333;
        text-align: center;
        text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
        vertical-align: middle;
        cursor: pointer;
        background-color: #f5f5f5;
        background-image: -moz-linear-gradient(top, #ffffff, #e6e6e6);
        background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e6e6e6));
        background-image: -webkit-linear-gradient(top, #ffffff, #e6e6e6);
        background-image: -o-linear-gradient(top, #ffffff, #e6e6e6);
        background-image: linear-gradient(to bottom, #ffffff, #e6e6e6);
        background-repeat: repeat-x;
        border: 1px solid #cccccc;
        border-color: #e6e6e6 #e6e6e6 #bfbfbf;
        border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
        border-bottom-color: #b3b3b3;
        -webkit-border-radius: 4px;
        -moz-border-radius: 4px;
        border-radius: 4px;
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#ffe6e6e6', GradientType=0);
        filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
        -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
        -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .fragmentation .period-button
    {
        margin-left: -1px;
        border-radius: 0px;
    }

    .fragmentation ul :first-child .period-button
    {
        margin-left: 0;
        -webkit-border-bottom-left-radius: 4px;
        border-bottom-left-radius: 4px;
        -webkit-border-top-left-radius: 4px;
        border-top-left-radius: 4px;
        -moz-border-radius-bottomleft: 4px;
        -moz-border-radius-topleft: 4px;
    }

    .fragmentation ul :last-child .period-button
    {
        -webkit-border-bottom-right-radius: 4px;
        border-bottom-right-radius: 4px;
        -webkit-border-top-right-radius: 4px;
        border-top-right-radius: 4px;
        -moz-border-radius-bottomright: 4px;
        -moz-border-radius-topright: 4px;
    }

    .selected-period
    {
        margin-bottom: 10px;
    }
    
    legend-group li
    {
        float: none;
        padding: 3px 5px;
    }

    .fragmentation .period-title
    {
        display: none;
    }
}

@media only screen and (max-width: 320px)
{
    .wrapper
    {
        width: 100%;
    }
    
    /* table of instrument*/
    .time-label
    {
        display: block;
    }
    .date-time-heading
    {
        line-height: normal !important;
    }
    .td-currency-header, .td-change-header, .td-high-header, .td-low-header, .td-currency, .td-change, .td-high, .td-low
    {
        display: none;
    }
    .table-header
    {
        padding: 0 5px;
    }
    .share-currency
    {
        display: inline;
    }
    .table-row
    {
        white-space: normal;
        line-height: 15px;
        padding: 5px;
    }

    /* begin table market share */
    .market-share
    {
        width: 100%;
        display: block;
    }
    
    .table-market-share
    {
        width:100%;
    }
    /* begin column chart */

    .pie-chart-container
    {
        width: 100%;
        height: 200px;
        display: block;
    }
    .activity-trend-heading
    {
        padding-top: 40px;
    }
    .main-activity-chart {
        width: 80%;
        margin: 10%;
    }
    .other-activity-chart-container
    {
        width: 100%;
        padding: 0;
    }

    body.body-rtl .other-activity-chart-container
    {
        float: left;
    }
    
    .other-activity-chart
    {
        width: 60%;
        margin: 5% 20%;    
    }
    body.body-rtl .main-activity-chart
    {
        float: left;
        margin-left: 15px;
    }

    .fragmentation a.period-button
	{
        margin-left: -1px;
        border-radius: 0px;
		min-width: 10px;
		padding: 0 2px;
    }

    .fragmentation ul :first-child .period-button
    {
        margin-left: 0;
        -webkit-border-bottom-left-radius: 4px;
        border-bottom-left-radius: 4px;
        -webkit-border-top-left-radius: 4px;
        border-top-left-radius: 4px;
        -moz-border-radius-bottomleft: 4px;
        -moz-border-radius-topleft: 4px;
    }

    .fragmentation ul :last-child .period-button
    {
        -webkit-border-bottom-right-radius: 4px;
        border-bottom-right-radius: 4px;
        -webkit-border-top-right-radius: 4px;
        border-top-right-radius: 4px;
        -moz-border-radius-bottomright: 4px;
        -moz-border-radius-topright: 4px;
    }

    .selected-period
    {
        margin-bottom: 10px;
    }
    
    legend-group li
    {
        float: none;
        padding: 3px 5px;
    }

    .fragmentation .period-title
    {
        display: none;
    }
}
 
/*#REGION exporting */
div.exporting
{
    margin: 9px !important;
}

div.exporting .other-activity-chart-container
{
    width: 360px !important;
}

div.exporting .main-activity-chart
{
    float: left;
    height: 200px;
    margin-right: 10%;
    margin-top: 70px;
    width: 30%;
}

body.body-rtl div.exporting .main-activity-chart
{
    margin-left: 10%;    
    float: right;
}

div.exporting .other-activity-chart
{
    float: left;
    height: 200px;
    margin: 5%;
    width: 40%;
}

div.exporting .market-share
{
    width: auto !important;
}

div.exporting #pie-chart
{
    width: 100% !important;
}

div.exporting .main-heading
{
    margin-bottom: 0px;    
}
    
div.exporting .date-time-heading
{
    margin-bottom:1px;
    padding-top:0px;
}

div.exporting .legend-group li
{
    float: left;
    padding: 3px 5px;
    /*line-height:45px;*/
}

div.exporting .fragmentation
{
    margin-top:40px;
}

div.exporting .selected-period
{
    margin-top: 4px;
    margin-left: 3px;
}

div.exporting #chart-container
{
    padding:2px;
}

div.exporting .legend-group
{
    margin-top:5px;
}

div.exporting .market-share-pie-chart
{
    margin-top:40px;
    width:100%;
}

div.exporting div.command-button
{
    display: none;
}

div.exporting div.period-selection
{
    display: none;
}

div.exporting .radio
{
    line-height: 17px;
}

div.exporting .pie-chart-container
{
    height: 250px;
    width: 450px;
    *height: 250px;
    *width: 400px;
    position: relative;    
}

div.tooltip_box {
	background: #ffffff;
	background: rgba(255,255,255,1);
	border: 1px solid #333333;
	border-radius: 5px;
	padding: 10px;
	color: #333333;
	box-shadow: 2px 2px 5px 0px #9d9d9d;
}

@media print 
{
    #columnChartContainer3, #columnChartContainer4
    {
        margin-top:150px;
    }
    
    .wrapper{
		width: 100% !important;
	}
    .command-button {
        display: none !important;
    }
	.etooltip {
		display: none;
	}
}