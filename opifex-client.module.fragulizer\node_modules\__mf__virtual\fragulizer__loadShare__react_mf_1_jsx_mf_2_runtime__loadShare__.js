
    
    ;() => import("__mf__virtual/fragulizer__prebuild__react_mf_1_jsx_mf_2_runtime__prebuild__.js").catch(() => {});
    // dev uses dynamic import to separate chunks
    ;() => import("react/jsx-runtime").catch(() => {});
    const {loadShare} = require("@module-federation/runtime")
    const {initPromise} = require("__mf__virtual/fragulizer__mf_v__runtimeInit__mf_v__.js")
    const res = initPromise.then(_ => loadShare("react/jsx-runtime", {
    customShareInfo: {shareConfig:{
      singleton: true,
      strictVersion: false,
      requiredVersion: "^19.1.0"
    }}}))
    const exportModule = /*mf top-level-await placeholder replacement mf*/res.then(factory => factory())
    module.exports = exportModule
  