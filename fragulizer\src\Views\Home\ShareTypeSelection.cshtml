﻿@using Fragulizer.SettingService
@using ToolsFramework
@using Fragulizer.Common
@{
    InstrumentGroupToolCompanySetting setting = Services.Get<IInstrumentGroupSettingService>().GetSettings(RequestHelper.CompanyCode);            
}
@if (setting.InstrumentGroups.Count > 0)
{
    <div class="share-type-selection" style="@(setting.InstrumentGroups.Count == 1 ? "display:none" : "")">
        <div class="selected-instrument-group">@setting.InstrumentGroups[0].Name</div>
        <table class="border-outline" width="100%" cellpadding="0" cellspacing="0">
            <thead>
                <tr>
                    <td class="table-header share-type-header ">
                        @Translations.SELECT_SHARE
                    </td>
                </tr>
            </thead>
            <tbody>
                @for (int i = 0; i < setting.InstrumentGroups.Count; i++)
                {
                    if (i == 0)
                    {
                    <tr>
                        <td class="table-row share-type-item border-inline table-share-row-even">
                            <label>
                                    <input type="radio" name="share" class="share-item" typeIndex="@(i + 1)" instruments="@setting.InstrumentGroups[i].InstrumentIds" checked="checked"/>
                                @setting.InstrumentGroups[i].Name</label>
                        </td>
                    </tr>
                    }
                    else
                    {
                    <tr>
                        <td class="table-row share-type-item @(i % 2 == 0 ? "border-inline table-share-row-even" : "border-inline table-share-row-odd")">
                            <label>
                                    <input type="radio" name="share" class="share-item" typeIndex="@(i + 1)" instruments="@setting.InstrumentGroups[i].InstrumentIds"/>
                                @setting.InstrumentGroups[i].Name</label>
                        </td>
                    </tr>
                    }
                }
            </tbody>
        </table>
    </div>
}
