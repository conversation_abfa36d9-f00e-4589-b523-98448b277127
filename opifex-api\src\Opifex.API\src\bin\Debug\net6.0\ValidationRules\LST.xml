<?xml version="1.0" encoding="utf-8"?>
<!--
    Defines list of XML Paths that be enabled to modify combines with
    the validation rules for validating input value must be satisfied. 
-->
<schema>
	<pattern>
		<rule context="/">
			<assert test="exists(Template)">Must have Template element</assert>
			<assert test="contains(createArray('Adaptive','Compact','Perspective','Quadrata','Victor'), Template)">
				Template must be one of following values: 'Adaptive', 'Compact', 'Perspective', 'Quadrata', 'Victor'
			</assert>
			<assert test="isBoolean(EnabledOrderDepth)">EnabledOrderDepth value must be type of boolean</assert>
			<assert test="and(isInteger(RefreshTimeOut) , greaterOrEquals(RefreshTimeOut, 30), lessOrEquals(RefreshTimeOut, 60))">'RefreshTimeOut' must be a number and greater than or equal 60</assert>
		</rule>
		<rule context="Instruments">
			<assert test="count(Instrument) > 0">'Instruments' must has at least one 'Instrument'</assert>
			<assert test="!contains(foreach(Instrument, i, isInteger(i.Id)), false)">'InstrumentID' must be a number</assert>
		</rule>
	</pattern>
</schema>