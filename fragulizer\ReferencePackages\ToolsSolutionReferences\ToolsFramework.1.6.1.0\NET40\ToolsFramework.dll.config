﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="languageMap" type="ToolsFramework.Configuration.LanguageSection,ToolsFramework" />
  </configSections>
  <!--
    "customMapUrl": custom address to the file LanguageMap.xml. 
            The value can be Internet address (http://tools.euroland.com/tools/LanguageMap.xml) 
            or Local physical path ("C:\\Interpub\wwwroot\tools\LanguageMap.xml"). 
            NOTE: Preferably, Internet address is recommend
  -->
  <appSettings>
    <add key="customLanguageMapUrl" value="http://eurolandasia.homeftp.net/tools/LanguageMap.xml" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
  </appSettings>
  <languageMap reloadInterval="10" versionDate="2012-06-06">
    <languages>
      <lang value="EN" name="english" defaultCultureCode="en-GB">
        <resolveFor>
          <culture cultureCode="en-US" languageID="32" />
          <culture cultureCode="en-GB" languageID="33" />
          <culture cultureCode="en-AU" languageID="34" />
        </resolveFor>
      </lang>
    </languages>
  </languageMap>
  <connectionStrings>
    <add name="LanguageConnectionString" connectionString="Data Source=buffalo;Initial Catalog=Language;User=test;Password=Test" providerName="System.Data.SqlClient" />
    <add name="ToolsFramework.Properties.Settings.LanguageConnectionString" connectionString="Data Source=buffalo;Initial Catalog=Language;User=test;Password=Test" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0" />
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.WindowsAzure.StorageClient" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.7.0.0" newVersion="1.7.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Configuration" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.WindowsAzure.ServiceRuntime" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Data.Services.Client" publicKeyToken="b77a5c561934e089" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
</configuration>