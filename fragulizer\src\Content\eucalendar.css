/*! EUCalendar version 2.0 - a calendar plugin heavily based on JSCalendar at http://www.dynarch.com/jscal | fixed bugs and make support touch <NAME_EMAIL> */

.EUCalendar 
{
  border: 2px solid #CCC;
  background: white;
  box-shadow: 0 2px 10px rgba(0,0,0,.2);
  ms-box-shadow: 0 2px 10px rgba(0,0,0,.2);
  -moz-box-shadow: 0 2px 10px rgba(0,0,0,.2);
  -webkit-box-shadow: 0 2px 10px rgba(0,0,0,.2);
	-moz-user-select: none;
	-webkit-user-select: none;
	user-select: none;
  /*font: 11px "lucida grande",tahoma,verdana,sans-serif;*/
  font-size: 110%;
  text-shadow: 1px 1px 1px #777;
  line-height: 14px;
  position: relative;
  cursor: default;
  z-index: 10000;
}

.EUCalendar:focus {
    outline: none;
}
/*.EUCalendar div:focus, 
.EUCalendar input:focus, */
.EUCalendar *:focus {
  -webkit-transition: outline 100s ease-out;
    -moz-transition: outline 100ms ease-out;
    transition: outline 100ms ease-out;
    outline: 3px solid #a8c1e4;
}

.EUCalendar table {
  border-collapse: collapse;
  /*font: 11px "lucida grande",tahoma,verdana,sans-serif;*/
  line-height: 14px;
  border-spacing: 0;
  border-collapse: collapse;
}

.EUCalendar-topBar {
  border-bottom: 1px solid #aaa;
  /*background: #E7F4FF;*/
  padding: 5px 0 0 0;
}

.EUCalendar-table-topControl {
  width: 100%;
}

table.EUCalendar-titleCont {
  font-size: 120%; 
  font-weight: bold;
  color: #444;
  text-align: center;
  position: relative;
}

.EUCalendar-menu-year-table-cont {
  height: 50%;
}
.EUCalendar-menu-yearLabel div,
.EUCalendar-title div {
  padding: 10px 20px;
  text-shadow: 1px 1px 1px #777;
  border: 1px solid transparent;
}
.EUCalendar-menu-hover-yearLabel div,
.EUCalendar-hover-title div {
  background-color: #fff;
  border: 1px solid #777777;
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-transition: border-color .218s;
  -moz-transition: border .218s;
  -o-transition: border-color .218s;
  transition: border-color .218s;
}
.EUCalendar-menu-pressed-yearLabel div,
.EUCalendar-pressed-title div {
  border: 1px solid #777777;
  background-color: #99CCFF;
  color: #fff;
}

.EUCalendar-menu-yearLabel,
.EUCalendar-menu-decade-title {
  font-size: 120%;
  font-weight: bold;
  display: inline-block;
  *display: inline;
  *zoom: 1;
}

.EUCalendar-bodyTable,
.EUCalendar-dayNames table {
	width: 100%;
}

.EUCalendar-bottomBar {
  border-top: 1px solid #aaa;
/*  background: #E7F4FF;*/
  padding: 2px;
  position: relative;
  text-align: center;
}

.EUCalendar-menu-today,
.EUCalendar-bottomBar-today {
  padding: 2px 15px;
  background-color: transparent;
  border: solid 0px #dcdcdc;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
  -webkit-transition: border-color .218s;
    -moz-transition: border .218s;
    -o-transition: border-color .218s;
    transition: border-color .218s;
}

.EUCalendar-menu-today-hover,
.EUCalendar-bottomBar-today-hover {
  border: 1px solid #777777;
  background-color: #fff;
  padding: 1px 14px;
}
.EUCalendar-menu-today-pressed,
.EUCalendar-bottomBar-today-pressed {
  border: 1px solid #777777;
  background-color: #99CCFF;
  color: #fff;
  padding: 1px 14px;
}

.EUCalendar-body {
  position: relative;
  overflow: hidden;
  padding-top: 5px;
  padding-bottom: 5px;
  text-shadow: .5px 0px 0px #ccc;
  /*outline: none;*/
}

.EUCalendar-first-col { padding-left: 5px; }
.EUCalendar-last-col { padding-right: 5px; }

.EUCalendar-animBody-backYear {
  position: absolute;
  top: -100%;
  left: 0;
}
.EUCalendar-animBody-back {
  position: absolute;
  top: 5px;
  left: -100%;
}
.EUCalendar-animBody-fwd {
  position: absolute;
  top: 5px;
  left: 100%;
}
.EUCalendar-animBody-now {
  position: absolute;
  top: 5px;
  left: 0;
}
.EUCalendar-animBody-fwdYear {
  position: absolute;
  top: 100%;
  left: 0;
}

.EUCalendar-dayNames {
  padding-left: 5px;
  padding-right: 5px;
  clear: both;
}

.EUCalendar-dayNames div { font-weight: bold; color: #444; }

.EUCalendar-navBtn {
  /*position: absolute;
  top: 5px;*/
}
.EUCalendar-menu-prevDecade button,
.EUCalendar-menu-nextDecade button,
.EUCalendar-navBtn button {
  padding: 1px;
  text-align: center;
  height: 25px;
  width: 25px;
  line-height: 25px;
  border: none;
  font-weight: bold;
  font-size: 110%;
  background-color: transparent;
  border: solid 0px #dcdcdc;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
  -webkit-transition: border-color .218s;
    -moz-transition: border .218s;
    -o-transition: border-color .218s;
    transition: border-color .218s;
}
.EUCalendar-hover-navBtn button {
  border: 1px solid #777777;
  padding: 0;
  background-color: #fff;
}
.EUCalendar-menuDisabled,
.EUCalendar-navDisabled {
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.EUCalendar-pressed-navBtn button {
  border: 1px solid #777777;
  padding: 0;
  background-color: #99CCFF;
  color: #fff;
}

.EUCalendar-prevMonth {
  
}

.EUCalendar-nextMonth {
  
}

.EUCalendar-prevYear {
  
}

.EUCalendar-nextYear {
  
}

.EUCalendar-menu,
.EUCalendar-menuYear {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #ddd;
  overflow: hidden;
}

.EUCalendar-menu-cont {
  position: relative;
  height: 100%;
}

.EUCalendar-menu table td .EUCalendar-menu-month,
.EUCalendar-menuYear .EUCalendar-menu-table td .EUCalendar-menu-month {
  width: 100%;
  height: 2em;
  line-height: 2em;
  text-align: center;
  border: solid 1px;
  border-color: transparent;
}
.EUCalendar-menu table td .EUCalendar-hover-navBtn,
.EUCalendar-menuYear  .EUCalendar-menu-table td .EUCalendar-hover-navBtn {
  border-color: #777777;
  background-color: #fff;
  color: #000;
  border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    transition: border-color 0.30s ease-in-out;
    -webkit-transition: border-color 0.30s ease-in-out;
    -moz-transition: border-color 0.30s ease-in-out;
    -ms-transition: border-color 0.30s ease-in-out;
    -o-transition: border-color 0.30s ease-in-out;
}
.EUCalendar-menu table td .EUCalendar-pressed-navBtn,
.EUCalendar-menuYear .EUCalendar-menu-table td .EUCalendar-pressed-navBtn {
  border-color: #777777;
  background-color: #777;
  color: #fff !important;
}
.EUCalendar-menu-close {
  position: absolute;
  top: 2px;
  right: 2px;
  border: 1px solid transparent;
  background-color: transparent;
  font-size: 80%;
}

.EUCalendar-menu-close-hover {
  border: 1px solid #777777;
  background-color: #fff;
}

.EUCalendar-menu-year {
  text-align: center;
  height: 40px !important;
  width: 50% !important;
  line-height: 40px !important;
  font-weight: bold;
  font-size: 35px;
  font-family: Arial, Helvetica, sans-serif;
  padding: 1px;
}

.EUCalendar-menu-decade-table-cont {
    position: relative;
    overflow: hidden;
    padding-top: 5px;
    padding-bottom: 5px;
}

.EUCalendar-menu-sep {
  height: 1px; font-size: 1px; line-height: 1px;
  overflow: hidden;
  border-top: 1px solid #888;
  background: #fff;
  margin-top: 4px; margin-bottom: 3px;
}

.EUCalendar-time td { font-weight: bold; font-size: 120%; }
.EUCalendar-time-hour, .EUCalendar-time-minute { padding: 1px 3px; }
.EUCalendar-time-down { background: url("Images/Calendar/img/time-down.png") no-repeat 50% 50%; width: 11px; height: 8px; opacity: 0.5; }
.EUCalendar-time-up { background: url("Images/Calendar/img/time-up.png") no-repeat 50% 50%; width: 11px; height: 8px; opacity: 0.5; }
.EUCalendar-time-sep { padding: 0 2px; }
.EUCalendar-hover-time { background-color: #444; color: #fff; opacity: 1; }
.EUCalendar-pressed-time { background-color: #777; color: #fff; opacity: 1; }
.EUCalendar-time-am { padding: 1px; width: 2.5em; text-align: center; }

/* body */

.EUCalendar-hover-week { background-color: #DDDDDD; }

.EUCalendar-dayNames div, .EUCalendar-day, .EUCalendar-weekNumber {
  width: 1.9em;
  height: 1.7em;
  line-height: 1.7em;
  padding: 3px 4px;
  text-align: center;
}
.EUCalendar-dayNames div {
  height: 1em;
  line-height: 1em;
  font-size: 90%;
}
.EUCalendar-weekNumber {
  border-right: 1px solid #aaa;
  margin-right: 4px;
  width: 2em !important;
  padding-right: 8px !important;
}

.EUCalendar-day {
  text-align: center; 
  color: #222;
  font-size: 95%;
  border: solid 0px #dcdcdc;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
  -webkit-transition: border-color .218s;
    -moz-transition: border .218s;
    -o-transition: border-color .218s;
    transition: border-color .218s;
}
.EUCalendar-day-othermonth { color: #888; }
.EUCalendar-weekend { color: #c22; }
.EUCalendar-day-today { color: #00f; font-weight: bold; }

.EUCalendar-menu-month-disabled,
.EUCalendar-day-disabled {
  opacity: 0.5;
  text-shadow: 2px 1px 1px #fff;
}

.EUCalendar-hover-date {
  padding: 2px 3px;
  background-color: #eeeeff;
  border: 1px solid #88c;
  margin: 0 !important;
  color: #000000;
}

.EUCalendar-menu-month-selected {
  /*background-color: #FFF;*/
  color: red !important;
  font-weight: bold;
}

.EUCalendar-day-othermonth.EUCalendar-hover-date { border-color: #aaa; color: #888; }

.EUCalendar-dayNames .EUCalendar-weekend { color: #c22; }
.EUCalendar-day-othermonth.EUCalendar-weekend { color: #d88; }

.EUCalendar-day-selected {
  padding: 2px 3px;
  margin: 1px;
  background-color: #172854;
  color: #FFFFFF !important;
}
.EUCalendar-day-today.EUCalendar-day-selected { background-color: #999; }

/*
.EUCalendar-focused {
  border-color: #000;
}

.EUCalendar-focused .EUCalendar-topBar, .EUCalendar-focused .EUCalendar-bottomBar {
  background-color: #ccc;
  border-color: #336;
}

.EUCalendar-focused .EUCalendar-hover-week {
  background-color: #ccc;
}*/

.EUCalendar-tooltip {
  position: absolute;
  top: 100%;
  width: 100%;
}

.EUCalendar-tooltipCont {
  margin: 0 5px 0 5px;
  border: 1px solid #aaa;
  border-top: 0;
  padding: 3px 6px;
  background: #ddd;
}
/*
.EUCalendar-focused .EUCalendar-tooltipCont {
  background: #ccc;
  border-color: #000;
}
*/

@media print {
  .EUCalendar-day-selected {
    padding: 2px 3px;
    border: 1px solid #000;
    margin: 0 !important;
  }
}
