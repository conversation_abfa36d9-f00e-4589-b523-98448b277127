﻿<!--
***********************************************************************************************
Microsoft.Web.Publishing.AllFilesInTheProject.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your web deploy projects from the command-line or the IDE.

This file defines the steps in the standard package/publish process for collecting only project files .

Copyright (C) Microsoft Corporation. All rights reserved.
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--ImportBefore Extension-->
  <PropertyGroup>
    <ImportByWildcardBeforeMicrosoftWebPublishingAllFilesInTheProjectTargets Condition="'$(ImportByWildcardBeforeMicrosoftWebPublishingAllFilesInTheProjectTargets)'==''">true</ImportByWildcardBeforeMicrosoftWebPublishingAllFilesInTheProjectTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportBefore\*" Condition="'$(ImportByWildcardBeforeMicrosoftWebPublishingAllFilesInTheProjectTargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportBefore')"/>

  <!--The following property need to set before import of Microsoft.Web.Publishing.OnlyFilesToRunTheApp.targets or it will set the property first-->
  <PropertyGroup>
    <!--Overwrite the default behavior.  Don't remove the web.debug.config -->
    <ExcludeTransformAssistFilesFromPublish Condition="'$(ExcludeTransformAssistFilesFromPublish)'==''">False</ExcludeTransformAssistFilesFromPublish>
  </PropertyGroup>

  <!--We need all of the item in the minimum run web needed-->
  <Import Project="Microsoft.Web.Publishing.OnlyFilesToRunTheApp.targets"/>

  <PropertyGroup>
    <PublishPipelineCollectFilesCore>
      $(PublishPipelineCollectFilesCore);
      CollectFilesFromCompile;
      CollectFilesFromEmbeddedResource;
      CollectFilesFromNone;
      CollectFilesFromBaseApplicationManifest;
      CollectFilesFromProjectFile;
    </PublishPipelineCollectFilesCore>
  </PropertyGroup>

  <!--MSBuild target does not have a good way to identify all item in the project yet. 
  MSBuild team is working on this.  At the same time, we are working backward from only known common item collection from 
  The following is base on http://msdn.microsoft.com/en-us/library/bb629388.aspx for common project item.
  Note that Content Collection is already in the Microsoft.Web.Publishing.OnlyFilesToRunTheApp.targets-->
  
  <!--********************************************************************-->
  <!-- Task CollectFilesFromCompile -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFromCompileDependsOn>
      $(OnBeforeCollectFilesFromCompile);
      $(CollectFilesFromCompileDependsOn);
    </CollectFilesFromCompileDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFromCompile"
          DependsOnTargets="$(CollectFilesFromCompileDependsOn)"
          Condition="'@(Compile)'!=''">


    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificItemsFromProject"
      ArgumentCount="1"
      Arguments="Compile"
      LogType="Message" />
    <Message Text="@(Compile)" />
    
    <ItemGroup>
      <FilesForPackagingFromProject Include="@(Compile)" Condition="'%(Compile.Link)'==''">
        <DestinationRelativePath>%(Compile.Identity)</DestinationRelativePath>
        <FromTarget>CollectFilesFromCompile</FromTarget>
        <Category>ProjectNotRunRequired</Category>
      </FilesForPackagingFromProject>
      <FilesForPackagingFromProject Include="@(Compile)" Condition="'%(Compile.Link)'!='' And $(EnableCollectLinkFilesInProject)">
        <DestinationRelativePath>%(Compile.Link)</DestinationRelativePath>
        <FromTarget>CollectFilesFromCompile</FromTarget>
        <Category>ProjectNotRunRequired</Category>
        <Exclude>$(ExcludeLinkFilesInProject)</Exclude>
        <ProjectFileType>Link</ProjectFileType>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFromCompile)" RunEachTargetSeparately="false" />
  </Target>

  <!--********************************************************************-->
  <!-- Task CollectFilesFromEmbeddedResource -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFromEmbeddedResourceDependsOn>
      $(OnBeforeCollectFilesFromEmbeddedResource);
      $(CollectFilesFromEmbeddedResourceDependsOn);
    </CollectFilesFromEmbeddedResourceDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFromEmbeddedResource"
          DependsOnTargets="$(CollectFilesFromEmbeddedResourceDependsOn)"
          Condition="'@(EmbeddedResource)'!=''">

    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificItemsFromProject"
      ArgumentCount="1"
      Arguments="EmbeddedResource"
      LogType="Message" />
    <Message Text="@(EmbeddedResource)" />

    <ItemGroup>
      <FilesForPackagingFromProject Include="@(EmbeddedResource)" Condition="'%(EmbeddedResource.Link)'==''">
        <DestinationRelativePath>%(EmbeddedResource.Identity)</DestinationRelativePath>
        <FromTarget>CollectFilesFromEmbeddedResource</FromTarget>
        <Category>ProjectNotRunRequired</Category>
      </FilesForPackagingFromProject>
      <FilesForPackagingFromProject Include="@(EmbeddedResource)" Condition="'%(EmbeddedResource.Link)'!='' And $(EnableCollectLinkFilesInProject)">
        <DestinationRelativePath>%(EmbeddedResource.Link)</DestinationRelativePath>
        <FromTarget>CollectFilesFromEmbeddedResource</FromTarget>
        <Category>ProjectNotRunRequired</Category>
        <Exclude>$(ExcludeLinkFilesInProject)</Exclude>
        <ProjectFileType>Link</ProjectFileType>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFromEmbeddedResource)" RunEachTargetSeparately="false" />
  </Target>

  <!--********************************************************************-->
  <!-- Task CollectFilesFromNone -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFromNoneDependsOn>
      $(OnBeforeCollectFilesFromNone);
      $(CollectFilesFromNoneDependsOn);
    </CollectFilesFromNoneDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFromNone"
          DependsOnTargets="$(CollectFilesFromNoneDependsOn)"
          Condition="'@(None)'!=''">

    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificItemsFromProject"
      ArgumentCount="1"
      Arguments="None"
      LogType="Message" />
    <Message Text="@(None)" />
    
    <ItemGroup>
      <FilesForPackagingFromProject Include="@(None)" Condition="'%(None.Link)'==''">
        <DestinationRelativePath>%(None.Identity)</DestinationRelativePath>
        <FromTarget>CollectFilesFromNone</FromTarget>
        <Category>ProjectNotRunRequired</Category>
      </FilesForPackagingFromProject>
      <FilesForPackagingFromProject Include="@(None)" Condition="'%(None.Link)'!='' And $(EnableCollectLinkFilesInProject)" >
        <DestinationRelativePath>%(None.Link)</DestinationRelativePath>
        <FromTarget>CollectFilesFromNone</FromTarget>
        <Category>ProjectNotRunRequired</Category>
        <Exclude>$(ExcludeLinkFilesInProject)</Exclude>
        <ProjectFileType>Link</ProjectFileType>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFromNone)" RunEachTargetSeparately="false" />
  </Target>


  <!--********************************************************************-->
  <!-- Task CollectFilesFromBaseApplicationManifest -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFromBaseApplicationManifestDependsOn>
      $(OnBeforeCollectFilesFromBaseApplicationManifest);
      $(CollectFilesFromBaseApplicationManifestDependsOn);
    </CollectFilesFromBaseApplicationManifestDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFromBaseApplicationManifest"
          DependsOnTargets="$(CollectFilesFromBaseApplicationManifestDependsOn)"
          Condition="'@(BaseApplicationManifest)'!=''">


    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificItemsFromProject"
      ArgumentCount="1"
      Arguments="BaseApplicationManifest"
      LogType="Message" />
    <Message Text="@(BaseApplicationManifest)" />

    <ItemGroup>
      <FilesForPackagingFromProject Include="@(BaseApplicationManifest)" Condition="'%(BaseApplicationManifest.Link)'==''">
        <DestinationRelativePath>%(BaseApplicationManifest.Identity)</DestinationRelativePath>
        <FromTarget>CollectFilesFromBaseApplicationManifest</FromTarget>
        <Category>ProjectNotRunRequired</Category>
      </FilesForPackagingFromProject>
      <FilesForPackagingFromProject Include="@(BaseApplicationManifest)" Condition="'%(BaseApplicationManifest.Link)'!='' And $(EnableCollectLinkFilesInProject)">
        <DestinationRelativePath>%(BaseApplicationManifest.Link)</DestinationRelativePath>
        <FromTarget>CollectFilesFromBaseApplicationManifest</FromTarget>
        <Category>ProjectNotRunRequired</Category>
        <Exclude>$(ExcludeLinkFilesInProject)</Exclude>
        <ProjectFileType>Link</ProjectFileType>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFromBaseApplicationManifest)" RunEachTargetSeparately="false" />
  </Target>


  <!--********************************************************************-->
  <!-- Task CollectFilesFromProjectFile -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFromProjectFileDependsOn>
      $(OnBeforeCollectFilesFromProjectFile);
      $(CollectFilesFromProjectFileDependsOn);
    </CollectFilesFromProjectFileDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFromProjectFile"
          DependsOnTargets="$(CollectFilesFromProjectFileDependsOn)">

    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificItemsFromProject"
      ArgumentCount="1"
      Arguments="ProjectFiles"
      LogType="Message" />
    <Message Text="$(MSBuildProjectFile);$(MSBuildProjectFile).user" />

    <ItemGroup>
      <FilesForPackagingFromProject Include="$(MSBuildProjectFile)">
        <DestinationRelativePath>$(MSBuildProjectFile)</DestinationRelativePath>
        <FromTarget>CollectFilesFromProjectFile</FromTarget>
        <Category>ProjectNotRunRequired</Category>
      </FilesForPackagingFromProject>
      <FilesForPackagingFromProject Include="$(MSBuildProjectFile).user" Condition="Exists('$(MSBuildProjectFile).user')">
        <DestinationRelativePath>$(MSBuildProjectFile).user</DestinationRelativePath>
        <FromTarget>CollectFilesFromProjectFile</FromTarget>
        <Category>ProjectNotRunRequired</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFromProjectFile)" RunEachTargetSeparately="false" />
  </Target>

  <!--ImportAfter Extension-->
  <PropertyGroup>
    <ImportByWildcardAfterMicrosoftWebPublishingAllFilesInTheProjectTargets Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingAllFilesInTheProjectTargets)'==''">true</ImportByWildcardAfterMicrosoftWebPublishingAllFilesInTheProjectTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter\*" Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingAllFilesInTheProjectTargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter')"/>

</Project>
