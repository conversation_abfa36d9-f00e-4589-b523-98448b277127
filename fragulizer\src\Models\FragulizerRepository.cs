﻿using System;
using System.Collections.Generic;
using System.Data.Linq;
using System.Linq;
using Fragulizer.Common;
using Fragulizer.Models.Partials;
using Fragulizer.SettingService;
using ToolsFramework;
using ToolsFramework.Cache;
using ToolsFramework.Data;

namespace Fragulizer.Models
{
    public interface IFragulizerRepository
    {
        LiveData GetLiveData(string instrumentIds, DateTime startDate, DateTime endDate, TimeSpan openTimeSpan);

        List<InstrumentInfo> GetLatestTrading(string instrumentIds);

        List<List<ActivityTrendData>> GetActivityTrendData(string instrumentIds, int numberOfYear);

        List<MarketInfo> GetMarketInfo(string instrumentIds);
    }

    public class FragulizerRepository : RepositoryBase, IFragulizerRepository
    {
        public FragulizerRepository(IDatabase database, ICache cache)
            : base(database, cache)
        {
        }

        public List<InstrumentInfo> GetLatestTrading(string instrumentIds)
        {
            List<InstrumentInfo> lstLatestTrading = new List<InstrumentInfo>();

            List<List<object>> param = (GetInstrumentId(instrumentIds).Distinct().Select(s => new List<object>() { s })).ToList();

            lstLatestTrading = DB.GetManyRecordsWithPrams<InstrumentInfo>("spFragInstrumentSelectByID", param).ToList();
            lstLatestTrading.ForEach(c => c.Time = Utility.CETToAny(c.Time, Utility.GetPageTimeZoneID()));

            //get translation for marketname
            LanguageService.GetTranslationOnTime<InstrumentInfo>(
                       lstLatestTrading,
                       (c) => c.MarketTranslationID,
                       (c, tran) => c.MarketName = string.IsNullOrEmpty(tran) ? c.MarketName : tran);

            return lstLatestTrading;
        }

        private List<int> GetInstrumentId(string instrumentIds)
        {
            List<string> lstInstrumentId = instrumentIds.Replace(';', ',').Split(',').ToList();
            List<int> ids = new List<int>();
            foreach (string inId in lstInstrumentId)
            {
                int instrumentId = 0;
                int.TryParse(inId, out instrumentId);
                if (instrumentId > 0)
                {
                    ids.Add(instrumentId);
                }
            }
            return ids;
        }

        public LiveData GetLiveData(string instrumentIds, DateTime startDate, DateTime endDate, TimeSpan openTime)
        {
#if DEBUG
            return SpeedWatch.Me.HowLong<LiveData>(
                () => { },
                (elapsedTime) =>
                {
                    System.Diagnostics.Debug.WriteLine("GetFragmentationData() in (s): " + elapsedTime.Seconds);
                },
                () =>
                {
#endif
                    // data
                    LiveData liveData = new LiveData();

                    // database timezone
                    TimeZoneInfo cestTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Central Europe Standard Time");

                    // convert utc time to db time
                    DateTime cestStartDate = TimeZoneInfo.ConvertTime(startDate, TimeZoneInfo.Utc, cestTimeZone);
                    DateTime cestEndDate = TimeZoneInfo.ConvertTime(endDate, TimeZoneInfo.Utc, cestTimeZone);
                    DateTime cestCurrentTime = TimeZoneInfo.ConvertTime(DateTime.UtcNow, TimeZoneInfo.Utc, cestTimeZone);

                    bool isLiveData = cestStartDate.Date == cestCurrentTime.Date & cestEndDate.Date == cestCurrentTime.Date;
                    DateTime startTime = cestStartDate.Date;
                    DateTime endTime = isLiveData ? cestEndDate : cestEndDate.Date;

                    IMultipleResults multipleResults = DB.GetManyTables("spFragDailyHistorySelectHistoryData", instrumentIds, startTime, endTime);
                    if (multipleResults != null)
                    {
                        List<Fragmentation> lstList = new List<Fragmentation>();
                        // get data for fragmentation chart	
                        List<FragmentationData> lstReturnData = multipleResults.GetResult<FragmentationData>().ToList();
                        if (lstReturnData.Count > 0)
                        {
                            LanguageService.GetTranslationOnTime<FragmentationData>(
                                        lstReturnData,
                                        (c) => c.MarketTranslationID,
                                        (c, tran) => c.MarketName = string.IsNullOrEmpty(tran) ? c.MarketName : tran);


                            List<int> marketIDs = lstReturnData.Select(s => s.MarketID).Distinct().ToList<int>();

                            if (isLiveData)
                            {
                                lstList = GetLiveData(marketIDs, lstReturnData, openTime);
                            }
                            else
                            {
                                lstList = GetHistoricalData(lstReturnData, marketIDs);
                            }

                            liveData.FragmentationData = lstList;
                        }
                        // get data for market share chart
                        List<MarketShareData> lstMarketShareDatas;
                        try
                        {
                            lstMarketShareDatas = multipleResults.GetResult<MarketShareData>().ToList();
                            if (lstMarketShareDatas.Count > 0)
                            {
                                LanguageService.GetTranslationOnTime<MarketShareData>(
                                lstMarketShareDatas,
                                (c) => c.MarketTranslationID,
                                (c, tran) => c.MarketName = string.IsNullOrEmpty(tran) ? c.MarketName : tran);

                                List<MarketShareData> lstPieChart = new List<MarketShareData>();
                                foreach (MarketShareData pie in lstMarketShareDatas)
                                {
                                    pie.MarketName = !string.IsNullOrEmpty(Utility.GetCustomMarketName(pie.MarketId))
                                                                    ? Utility.GetCustomMarketName(pie.MarketId)
                                                                    : pie.MarketName;

                                    lstPieChart.Add(pie);
                                }
                                liveData.MarketShareData = lstPieChart;
                            }
                        }
                        catch
                        {
                            lstMarketShareDatas = null;
                        }
                    }

                    return liveData;

#if DEBUG
                });
#endif
        }

        private List<Fragmentation> GetHistoricalData(List<FragmentationData> lstReturnData, List<int> marketIDs)
        {
            List<DateTime> lstDates = (from fd in lstReturnData
                                       where lstReturnData.Any(fd1 => fd1.Time == fd.Time && fd1.Volume > 0)
                                       select fd.Time).Distinct().OrderBy(x => x).ToList();

            List<Fragmentation> lstList = new List<Fragmentation>();
            Dictionary<int, List<FragmentationData>> separateData = new Dictionary<int, List<FragmentationData>>();
            foreach (int marketId in marketIDs)
            {
                separateData.Add(marketId, new List<FragmentationData>());
            }

            foreach (FragmentationData fragmentationData in lstReturnData)
            {
                fragmentationData.MarketName = string.IsNullOrEmpty(Utility.GetCustomMarketName(fragmentationData.MarketID)) ? fragmentationData.MarketName : Utility.GetCustomMarketName(fragmentationData.MarketID);
                separateData[fragmentationData.MarketID].Add(fragmentationData);
            }

            foreach (int marketId in marketIDs)
            {
                List<FragmentationData> lstLiveData = separateData[marketId];

                Fragmentation livedata = new Fragmentation();
                livedata.MarketID = marketId;
                livedata.MarketTranslationID = lstLiveData[0].MarketTranslationID;
                livedata.MarketName = lstLiveData[0].MarketName;
                livedata.Data = new List<DataItem>();

                foreach (DateTime dateIndex in lstDates)
                {
                    FragmentationData fragmentationData = lstLiveData.Find(fd => fd.Time == dateIndex);
                    if (fragmentationData == null)
                    {
                        livedata.Data.Add(new DataItem()
                        {
                            Time = dateIndex,
                            Volume = 0
                        });
                    }
                    else
                    {
                        livedata.Data.Add(new DataItem()
                        {
                            Time = fragmentationData.Time,
                            Volume = fragmentationData.Volume
                        });
                    }
                }

                lstList.Add(livedata);
            }

            return lstList;
        }

        private List<Fragmentation> GetLiveData(List<int> marketIDs, List<FragmentationData> lstReturnData, TimeSpan openTimeSpan)
        {
            DateTime maxTime = Utility.CETToAny(lstReturnData.Max(item => item.Time), Utility.GetPageTimeZoneID());
            //DateTime minTime = maxTime.Date.AddHours(Utility.OpenTime.Hours).AddMinutes(Utility.OpenTime.Minutes);

            Dictionary<int, List<FragmentationData>> separateData = new Dictionary<int, List<FragmentationData>>();
            foreach (int marketID in marketIDs)
            {
                separateData.Add(marketID, new List<FragmentationData>());
            }
            foreach (FragmentationData liveData in lstReturnData)
            {
                liveData.MarketName = string.IsNullOrEmpty(Utility.GetCustomMarketName(liveData.MarketID)) ? liveData.MarketName : Utility.GetCustomMarketName(liveData.MarketID);
                separateData[liveData.MarketID].Add(liveData);
            }

            List<Fragmentation> lstList = new List<Fragmentation>();
            foreach (int marketId in marketIDs)
            {
                List<FragmentationData> lstLiveData = separateData[marketId];
                DateTime openTime = maxTime.Date.Add(openTimeSpan);

                // in case: no data for this market
                if (lstLiveData.Count == 0) continue;

				// in case: this market just has data for prvious date, not live data
				DateTime firstPointTime = Utility.CETToAny(lstLiveData[0].Time, Utility.GetPageTimeZoneID());

				if (firstPointTime < openTime && firstPointTime.Date < openTime.Date) continue;

                // when request time at minute 00, 15, 30, 45: store returns 2 last points with same Time value
                // one holds total volume of a certain minute. Ex: from 9:30:00 to 9:30:59
                // one holds total volume of last 15 minures. ex: from 9:01:00 to 9:30:59
                // so we need remove one that has volume smaller
                if (lstLiveData.Count > 2)
                {
                    int length = lstLiveData.Count;
                    if (lstLiveData[length - 1].Time == lstLiveData[length - 2].Time)
                    {
                        if (lstLiveData[length - 1].Volume > lstLiveData[length - 2].Volume)
                        {
                            lstLiveData.RemoveAt(length - 2);
                        }
                        else
                        {
                            lstLiveData.RemoveAt(length - 1);
                        }
                    }
                }

                // start processing data
                Fragmentation livedata = new Fragmentation();
                livedata.MarketID = marketId;
                livedata.MarketTranslationID = lstLiveData[0].MarketTranslationID;
                livedata.MarketName = lstLiveData[0].MarketName;
                livedata.Data = new List<DataItem>();

                bool firstPoint = true;

                // make sure every points (every 15 minutes) have data
                // in case there's no trading in a certain 15 minutes, set volume to zero
                while (openTime < maxTime.AddMinutes(15))
                {
                    // ignore first point at opening time due to there's no trading in fact
                    if (firstPoint)
                    {
                        openTime = openTime.AddMinutes(15);
                        firstPoint = false;
                        continue;
                    }

                    // prevent adding point with Time in future
                    if (openTime >= maxTime)
                        openTime = maxTime;

                    // prevent adding two points with the same Time
                    int dataLength = livedata.Data.Count;
                    if (dataLength > 0
                        && Utility.CETToAny(livedata.Data[dataLength - 1].Time, Utility.GetPageTimeZoneID()) == openTime)
                    {
                        openTime = openTime.AddMinutes(15);
                        continue;
                    }

                    // case 1: when all points of this market has been added to liveData,
                    // sets volume of every next point to zero until Time reached maxTime
                    // so that all lines (markets) on chart will be full filled
                    if (lstLiveData.Count == 0)
                    {
                        livedata.Data.Add(new DataItem()
                        {
                            Time = openTime,
                            Volume = 0
                        });
                    }
                    // case 2: somehow, there're no trading from market opening to 15 minutes later,
                    // the first points will set to zero until a certain transaction is performed
                    else if (Utility.CETToAny(lstLiveData[0].Time, Utility.GetPageTimeZoneID()) > openTime)
                    {
                        livedata.Data.Add(new DataItem()
                        {
                            Time = openTime,
                            Volume = 0
                        });
                    }
                    // case 3[normal case]: each point in turn added to the list
                    else
                    {
                        livedata.Data.Add(new DataItem()
                        {
                            Time = Utility.CETToAny(lstLiveData[0].Time, Utility.GetPageTimeZoneID()),
                            Volume = lstLiveData[0].Volume
                        });

                        lstLiveData.RemoveAt(0);
                    }

                    openTime = openTime.AddMinutes(15);
                }

                lstList.Add(livedata);
            }
            return lstList;
        }

        public List<List<ActivityTrendData>> GetActivityTrendData(string instrumentIds, int numberOfYear)
        {
            IMultipleResults multipleResults = DB.GetManyTables("spFragInstrumentHistorySelectHistoryDataForColumnChart", instrumentIds, numberOfYear);
            int primaryMarket = multipleResults.GetResult<int>().FirstOrDefault();

            List<ActivityTrendData> lstData =
                multipleResults.GetResult<ActivityTrendData>().OrderBy(d=>d.Year).ToList();

            LanguageService.GetTranslationOnTime<ActivityTrendData>(
                        lstData,
                        (c) => c.MarketTranslationID,
                        (c, tran) => c.MarketName = string.IsNullOrEmpty(tran) ? c.MarketName : tran);

            List<ActivityTrendData> lstDataGroup = lstData.GroupBy(item => item.Year).Select(group => new ActivityTrendData() { Year = group.First().Year, TotalVolume = (group.Sum(p => p.Volume)) }).OrderBy(y=>y.Year).ToList();

            Dictionary<int, List<ActivityTrendData>> separateData = new Dictionary<int, List<ActivityTrendData>>();
            List<int> marketIds = lstData.GroupBy(q => q.MarketId).Select(p => p.First().MarketId).ToList();

            separateData.Add(primaryMarket, new List<ActivityTrendData>());

            foreach (int marketId in marketIds)
            {
                if (marketId != primaryMarket)
                    separateData.Add(marketId, new List<ActivityTrendData>());
            }

            foreach (ActivityTrendData chartData in lstData)
            {
                foreach (ActivityTrendData data in lstDataGroup)
                {
                    if (chartData.Year == data.Year)
                    {
                        chartData.TotalVolume = data.TotalVolume;
                    }
                }
                chartData.MarketName = !string.IsNullOrEmpty(Utility.GetCustomMarketName(chartData.MarketId))
                                                 ? Utility.GetCustomMarketName(chartData.MarketId)
                                                 : chartData.MarketName;
                separateData[chartData.MarketId].Add(chartData);
            }

            return separateData.Values.ToList();
        }

        public List<MarketInfo> GetMarketInfo(string instrumentIds)
        {
            List<MarketInfo> lstMarkets = DB.GetManyRecords<MarketInfo>("spFragMarketSelectListByInstrumentIDs", instrumentIds).ToList();

            LanguageService.GetTranslationOnTime<MarketInfo>(
                        lstMarkets,
                        (c) => c.TranslationId,
                        (c, tran) => c.MarketName = string.IsNullOrEmpty(tran) ? c.MarketName : tran);

            return lstMarkets;
        }
    }
}