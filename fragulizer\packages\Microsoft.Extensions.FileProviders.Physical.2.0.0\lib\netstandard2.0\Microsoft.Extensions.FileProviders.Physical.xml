<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.FileProviders.Physical</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo">
            <summary>
            Represents a directory on a physical filesystem
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.#ctor(System.IO.DirectoryInfo)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo"/> that wraps an instance of <see cref="T:System.IO.DirectoryInfo"/>
            </summary>
            <param name="info">The directory</param>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.Exists">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.Length">
            <summary>
            Always equals -1.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.PhysicalPath">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.Name">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.LastModified">
            <summary>
            The time when the directory was last written to.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.IsDirectory">
            <summary>
            Always true.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.CreateReadStream">
            <summary>
            Always throws an exception because read streams are not support on directories.
            </summary>
            <exception cref="T:System.InvalidOperationException">Always thrown</exception>
            <returns>Never returns</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo">
            <summary>
            Represents a file on a physical filesystem
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.#ctor(System.IO.FileInfo)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo"/> that wraps an instance of <see cref="T:System.IO.FileInfo"/>
            </summary>
            <param name="info">The <see cref="T:System.IO.FileInfo"/></param>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.Exists">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.Length">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.PhysicalPath">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.Name">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.LastModified">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.IsDirectory">
            <summary>
            Always false.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.CreateReadStream">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher">
            <summary>
                <para>
                A file watcher that watches a physical filesystem for changes.
                </para>
                <para>
                Triggers events on <see cref="T:Microsoft.Extensions.Primitives.IChangeToken" /> when files are created, change, renamed, or deleted.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher.#ctor(System.String,System.IO.FileSystemWatcher,System.Boolean)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher" /> that watches files in <paramref name="root" />.
            Wraps an instance of <see cref="T:System.IO.FileSystemWatcher" />
            </summary>
            <param name="root">Root directory for the watcher</param>
            <param name="fileSystemWatcher">The wrapped watcher that is watching <paramref name="root" /></param>
            <param name="pollForChanges">
            True when the watcher should use polling to trigger instances of
            <see cref="T:Microsoft.Extensions.Primitives.IChangeToken" /> created by <see cref="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher.CreateFileChangeToken(System.String)" />
            </param>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher.CreateFileChangeToken(System.String)">
            <summary>
                <para>
                Creates an instance of <see cref="T:Microsoft.Extensions.Primitives.IChangeToken" /> for all files and directories that match the
                <paramref name="filter" />
                </para>
                <para>
                Globbing patterns are relative to the root directory given in the constructor
                <seealso cref="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher.#ctor(System.String,System.IO.FileSystemWatcher,System.Boolean)" />. Globbing patterns
                are interpreted by <seealso cref="T:Microsoft.Extensions.FileSystemGlobbing.Matcher" />.
                </para>
            </summary>
            <param name="filter">A globbing pattern for files and directories to watch</param>
            <returns>A change token for all files that match the filter</returns>
            <exception cref="T:System.ArgumentNullException">When <paramref name="filter" /> is null</exception>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher.Dispose">
            <summary>
            Disposes the file watcher
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken">
            <summary>
                <para>
                A change token that polls for file system changes.
                </para>
                <para>
                This change token does not raise any change callbacks. Callers should watch for <see cref="P:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken.HasChanged" /> to turn
                from false to true
                and dispose the token after this happens.
                </para>
            </summary>
            <remarks>
            Polling occurs every 4 seconds.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken.#ctor(System.IO.FileInfo)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken" /> that polls the specified file for changes as
            determined by <see cref="P:System.IO.FileSystemInfo.LastWriteTimeUtc" />.
            </summary>
            <param name="fileInfo">The <see cref="T:System.IO.FileInfo"/> to poll</param>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken.ActiveChangeCallbacks">
            <summary>
            Always false.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken.HasChanged">
            <summary>
            True when the file has changed since the change token was created. Once the file changes, this value is always true
            </summary>
            <remarks>
            Once true, the value will always be true. Change tokens should not re-used once expired. The caller should discard this
            instance once it sees <see cref="P:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken.HasChanged" /> is true.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken.RegisterChangeCallback(System.Action{System.Object},System.Object)">
            <summary>
            Does not actually register callbacks.
            </summary>
            <param name="callback">This parameter is ignored</param>
            <param name="state">This parameter is ignored</param>
            <returns>A disposable object that noops when disposed</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.Physical.PollingWildCardChangeToken">
            <summary>
            A polling based <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> for wildcard patterns.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PollingWildCardChangeToken.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.Extensions.FileProviders.Physical.PollingWildCardChangeToken"/>.
            </summary>
            <param name="root">The root of the file system.</param>
            <param name="pattern">The pattern to watch.</param>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PollingWildCardChangeToken.ActiveChangeCallbacks">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PollingWildCardChangeToken.HasChanged">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PollingWildCardChangeToken.GetLastWriteUtc(System.String)">
            <summary>
            Gets the last write time of the file at the specified <paramref name="path"/>.
            </summary>
            <param name="path">The root relative path.</param>
            <returns>The <see cref="T:System.DateTime"/> that the file was last modified.</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.Internal.PhysicalDirectoryContents">
            <summary>
            Represents the contents of a physical file directory
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Internal.PhysicalDirectoryContents.#ctor(System.String)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileProviders.Internal.PhysicalDirectoryContents"/>
            </summary>
            <param name="directory">The directory</param>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Internal.PhysicalDirectoryContents.Exists">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Internal.PhysicalDirectoryContents.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.PhysicalFileProvider">
            <summary>
            Looks up files using the on-disk file system
            </summary>
            <remarks>
            When the environment variable "DOTNET_USE_POLLING_FILE_WATCHER" is set to "1" or "true", calls to
            <see cref="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.Watch(System.String)" /> will use <see cref="T:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken" />.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.#ctor(System.String)">
            <summary>
            Initializes a new instance of a PhysicalFileProvider at the given root directory.
            </summary>
            <param name="root">The root directory. This should be an absolute path.</param>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.Dispose">
            <summary>
            Disposes the provider. Change tokens may not trigger after the provider is disposed.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.PhysicalFileProvider.Root">
            <summary>
            The root directory for this instance.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.GetFileInfo(System.String)">
            <summary>
            Locate a file at the given path by directly mapping path segments to physical directories.
            </summary>
            <param name="subpath">A path under the root directory</param>
            <returns>The file information. Caller must check Exists property. </returns>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.GetDirectoryContents(System.String)">
            <summary>
            Enumerate a directory at the given path, if any.
            </summary>
            <param name="subpath">A path under the root directory. Leading slashes are ignored.</param>
            <returns>
            Contents of the directory. Caller must check Exists property. <see cref="T:Microsoft.Extensions.FileProviders.NotFoundDirectoryContents" /> if
            <paramref name="subpath" /> is absolute, if the directory does not exist, or <paramref name="subpath" /> has invalid
            characters.
            </returns>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.Watch(System.String)">
            <summary>
                <para>Creates a <see cref="T:Microsoft.Extensions.Primitives.IChangeToken" /> for the specified <paramref name="filter" />.</para>
                <para>Globbing patterns are interpreted by <seealso cref="T:Microsoft.Extensions.FileSystemGlobbing.Matcher" />.</para>
            </summary>
            <param name="filter">
            Filter string used to determine what files or folders to monitor. Example: **/*.cs, *.*,
            subFolder/**/*.cshtml.
            </param>
            <returns>
            An <see cref="T:Microsoft.Extensions.Primitives.IChangeToken" /> that is notified when a file matching <paramref name="filter" /> is added,
            modified or deleted. Returns a <see cref="T:Microsoft.Extensions.FileProviders.NullChangeToken" /> if <paramref name="filter" /> has invalid filter
            characters or if <paramref name="filter" /> is an absolute path or outside the root directory specified in the
            constructor <seealso cref="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.#ctor(System.String)" />.
            </returns>
        </member>
    </members>
</doc>
