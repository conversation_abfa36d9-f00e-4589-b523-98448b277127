import { convertDayjsFormatToDotnetFormat } from "@euroland/opifix-utils";
import { findWindows } from 'windows-iana';

export function getLSTXmlSettings(basicSettings, state) {
  const { currencyCodes, InstrumentConfigs, ChartColors, InstrumentGroups, ...props } = state;
  console.log('basicSettings',basicSettings)
  const extractMultiLangField = (data, fieldName) => {
    const result = {};
    for (const [id, item] of Object.entries(data)) {
      if (item[fieldName] && typeof item[fieldName] === "object") {
        result[`_${id}`] = item[fieldName];
      }
    }
    return result;
  };

  const enabledIds = InstrumentConfigs
    ? Object?.entries(InstrumentConfigs)
        .filter(([_, value]) => value.EnabledDividendOption)
        .map(([key]) => key)
    : undefined;

  const extractConfigField = (configs, field) => {
    if (!configs) return undefined;

    return Object.entries(configs)
      .filter(([_, value]) => value[field])
      .reduce((acc, [key, value]) => {
        acc[`_${key}`] = value[field];
        return acc;
      }, {});
  };

  const updatedFormats =
    basicSettings?.Format &&
    Object.fromEntries(
      Object.entries(basicSettings.Format).map(([key, value]) => [
        key,
        {
          PercentDigits: value?.PercentDigits,
          DecimalSeparator: value?.DecimalSeparator,
          ThousandsSeparator: value?.ThousandsSeparator,
          DecimalDigits: value?.DecimalDigits,
          NegativeNumberFormat: value?.NegativeNumberFormat,
          LongDate: convertDayjsFormatToDotnetFormat(value?.LongDate),
          ShortDate: convertDayjsFormatToDotnetFormat(value?.ShortDate),
        },
      ])
    );

  // Process InstrumentGroups with multi-language support - ensure InstrumentGroups is array
  const processedInstrumentGroups = Array.isArray(InstrumentGroups) && InstrumentGroups.length > 0
    ? {
        InstrumentGroup: InstrumentGroups.map((group, index) => {
          // Get the group ID from InstrumentGroupKeys
          const groupKeys = state.InstrumentGroupKeys || [];
          const groupId = groupKeys[index];

          // Create Name object with multi-language support
          const nameObject = {};

          // Extract multi-language names from EditCustomPhrases using the actual group ID
          if (state.EditCustomPhrases && groupId && state.EditCustomPhrases[groupId]) {
            Object.entries(state.EditCustomPhrases[groupId]).forEach(([lang, name]) => {
              if (name && name.trim()) {
                nameObject[lang] = name;
              }
            });
          }

          // If no names found in EditCustomPhrases, use fallback
          if (Object.keys(nameObject).length === 0) {
            // Try to get name from group object itself
            if (group.Name) {
              nameObject['en-GB'] = group.Name;
            } else {
              // Final fallback
              nameObject['en-GB'] = `Group ${index + 1}`;
            }
          }

          return {
            Name: nameObject,
            InstrumentIDs: Array.isArray(group.InstrumentIDs)
              ? group.InstrumentIDs.join(";")
              : (group.InstrumentIDs || "")
          };
        })
      }
    : undefined;

  // Extract all instrumentIds from InstrumentGroups - ensure InstrumentGroups is array
  const allInstrumentIds = Array.isArray(InstrumentGroups) && InstrumentGroups.length > 0
    ? [...new Set(InstrumentGroups.flatMap(group =>
        Array.isArray(group.InstrumentIDs) ? group.InstrumentIDs : []
      ))]
    : basicSettings?.instrumentIds || [];
    
  const xml = {
    ...props,
    Format: updatedFormats,
    TimeZone: findWindows(basicSettings.timezone ?? "")?.[0],
    EnabledFieldTradeHistories: state?.EnabledFieldTradeHistories ?? "",
    RefreshTimeOut: basicSettings?.dataRefreshRate ?? 60,
    InstrumentIds: allInstrumentIds.join(","),
    DefaultCulture: basicSettings?.availableLanguages?.join(","),
    Currencies: currencyCodes?.join(","),
    ChartColors: Object.values(basicSettings?.instrument || {}).map(ins => ins.color).join(","),
    EnabledDividendOption:
      enabledIds?.join(",") ?? allInstrumentIds.join(","),
    LimitInvestmentStartDate: extractConfigField(
      InstrumentConfigs,
      "LimitInvestmentStartDate"
    ),
    LimitStartingData: extractConfigField(
      InstrumentConfigs,
      "LimitStartingData"
    ),
    ...(processedInstrumentGroups && { InstrumentGroups: processedInstrumentGroups }),
  };
  if (basicSettings?.instrument) {
    xml["CustomInstrumentName"] = extractMultiLangField(
      basicSettings.instrument,
      "shareName"
    );
    xml["CustomMarketName"] = extractMultiLangField(
      basicSettings.instrument,
      "marketName"
    );
  }
  if (state?.EditCustomPhrases) {
    xml["CustomPhrases"] = structuredClone(state.EditCustomPhrases);
  }

  if (state?.StyleURI) {
    xml["StyleURI"] = state.StyleURI;
  }

  console.log("xmlss", basicSettings, xml)
  delete xml.InstrumentConfigs;
  delete xml.AllowedParentDomains;
  delete xml.Template;
  delete xml.Instruments;
  delete xml.RefreshTimeOut;
  delete xml.HideChart;
  delete xml.GoogleAnalyticsEnabled;
  delete xml.EnabledFieldTradeHistories;
  delete xml.EnabledColorBlindMode;
  delete xml.EnableExcelDownload;
  return xml;
}
