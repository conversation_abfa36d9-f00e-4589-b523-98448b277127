﻿using System.Globalization;
using System.Web;
using ToolsFramework;
using ToolsFramework.Cache;
using ToolsFramework.Mvc;
using ToolsFramework.Settings;

namespace Fragulizer.Common
{
    public class RequestHelper : RequestHelperBase
    {
        public static string lang
        {
            get
            {
                //NOTE: [17-January-2012][binh.nguyen]: Get default culture from setting file if culture is not 
                // specific in query string
                // [01-June-2012][binh.nguyen]: Compatible with old tool with selected language query string named "selectlanguage"

                // Prioritize old tool first
                string lang = HttpContext.Current.Request["selectlanguage"];
                // If not, back to compatible with new tool with selected language query string named "lang"
                if (string.IsNullOrEmpty(lang))
                {
                    lang = HttpContext.Current.Request["lang"];
                }

                // There is no language query, try to get
                // the default language "<defaultlanguage/>" in configuration file
                if (string.IsNullOrEmpty(lang))
                {
                    lang = Tool.Settings.defaultculture;
                }
                // Still don't have lang? get "en-GB" as default
                if (string.IsNullOrEmpty(lang))
                    lang = Lang;
                return lang;
            }
        }

        public static string CompanyCode
        {
            get
            {
                if (string.IsNullOrEmpty(ToolsFramework.Mvc.RequestHelperBase.CompanyCode))
                    return "Default";
                else
                    return ToolsFramework.Mvc.RequestHelperBase.CompanyCode;
            }
        }
    }

    public static class Tool
    {
        public static ToolCompanySettings Settings
        {
            get
            {
                return Services.Get<ISettingsService>().GetSettings(RequestHelper.CompanyCode);
            }
        }

        public static CultureInfo Culture
        {
            get
            {
                return AspectF.Define
                    .Cache<CultureInfo>(Services.Get<ICache>(), RequestHelper.CompanyCode + RequestHelper.lang + "_culture")
                    .Return(() => Format.GetCulture(Services.Get<ILanguageService>().GetCurrentCultureCode(), Settings.format.Setting));
            }
        }
    }
}