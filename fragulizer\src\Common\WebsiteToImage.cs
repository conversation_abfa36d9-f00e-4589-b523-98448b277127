﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Threading;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Windows.Forms;

namespace Fragulizer.Common
{
    public class WebsiteToImage
    {
        private Bitmap m_Bitmap;
        private string m_Html;
        private int image_width;
        private string m_FileName = string.Empty;

        public WebsiteToImage(string html)
        {
            // Without file 
            m_Html = html;
        }

        public WebsiteToImage(string html, string fileName, int imageWidth)
        {
            // With file 
            m_Html = html;
            m_FileName = fileName;
            image_width = imageWidth;
        }

        public Bitmap Generate()
        {
            // Thread 
            var m_thread = new Thread(_Generate);
            m_thread.SetApartmentState(ApartmentState.STA);
            m_thread.Start();
            m_thread.Join();
            return m_Bitmap;
        }

        private void _Generate()
        {
            var browser = new WebBrowser { ScrollBarsEnabled = false };
            browser.DocumentText = m_Html;

            browser.DocumentCompleted += WebBrowser_DocumentCompleted;

            while (browser.ReadyState != WebBrowserReadyState.Complete)
            {
                Application.DoEvents();
            }

            browser.Dispose();
        }

        private void WebBrowser_DocumentCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
        {
            // Capture 
            var browser = (WebBrowser)sender;
            browser.Width = image_width;
            browser.ClientSize = new Size(image_width + 20, browser.Document.Body.ScrollRectangle.Bottom);
            browser.ScrollBarsEnabled = false;
            m_Bitmap = new Bitmap(image_width + 20, browser.Document.Body.ScrollRectangle.Bottom);
            browser.BringToFront();
            browser.DrawToBitmap(m_Bitmap, browser.Bounds);
        }
    }
}