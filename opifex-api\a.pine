//@version=6
strategy("Advanced High TP Strategy v6.2", overlay=true, 
         initial_capital=10000, default_qty_type=strategy.percent_of_equity, default_qty_value=10,
         commission_type=strategy.commission.percent, commission_value=0.1, slippage=2,
         margin_long=100, margin_short=100, pyramiding=1, calc_on_every_tick=false)

// ═══════════════════════════════════════════════════════════════════════════════════
// 📊 STRATEGY SETTINGS
// ═══════════════════════════════════════════════════════════════════════════════════
g_strategy = "🎯 Strategy Settings"
tpPercent = input.float(4.0, "Take Profit (%)", minval=1.0, maxval=20.0, step=0.1, group=g_strategy)
slPercent = input.float(2.0, "Stop Loss (%)", minval=0.5, maxval=10.0, step=0.1, group=g_strategy)
riskPerTrade = input.float(2.0, "Risk per Trade (%)", minval=0.5, maxval=10.0, step=0.1, group=g_strategy)
useTrailing = input.bool(true, "Enable Trailing Stop", group=g_strategy)
trailingPercent = input.float(1.5, "Trailing Stop (%)", minval=0.5, maxval=5.0, step=0.1, group=g_strategy)

g_filters = "🔍 Entry Filters"
useVolumeFilter = input.bool(true, "Volume Filter", group=g_filters)
volumeMultiplier = input.float(1.5, "Volume Multiplier", minval=1.0, maxval=3.0, step=0.1, group=g_filters)
useTrendFilter = input.bool(true, "Trend Filter", group=g_filters)
useRsiFilter = input.bool(true, "RSI Filter", group=g_filters)
rsiOversold = input.int(35, "RSI Oversold", minval=20, maxval=40, group=g_filters)
rsiOverbought = input.int(65, "RSI Overbought", minval=60, maxval=80, group=g_filters)

g_visual = "🎨 Visual Settings"
showSignals = input.bool(true, "Show Entry Signals", group=g_visual)
showLevels = input.bool(true, "Show TP/SL Levels", group=g_visual)
showTable = input.bool(true, "Show Performance Table", group=g_visual)

// ═══════════════════════════════════════════════════════════════════════════════════
// 📈 TECHNICAL INDICATORS - RESTORED
// ═══════════════════════════════════════════════════════════════════════════════════
// 1. Moving Averages (FIXED)
ema9 = ta.ema(close, 9)
ema21 = ta.ema(close, 21)
ema50 = ta.ema(close, 50)

// 2. RSI (FIXED)
rsi = ta.rsi(close, 14)

// 3. Bollinger Bands (FIXED)
bbLength = input.int(20, "BB Length", group="Technical Indicators")
bbStdDev = input.float(2.0, "BB StdDev", step=0.1, group="Technical Indicators")
bbBasis = ta.sma(close, bbLength)
dev = ta.stdev(close, bbLength)
bbUpper = bbBasis + bbStdDev * dev
bbLower = bbBasis - bbStdDev * dev
bbSqueeze = (bbUpper - bbLower) / bbBasis < 0.10

// 4. Volume Analysis (FIXED)
volSma = ta.sma(volume, 20)
volStdDev = ta.stdev(volume, 20)
highVolume = useVolumeFilter ? volume > volSma + volStdDev * (volumeMultiplier - 1) : true

// 5. ATR for volatility
atr = ta.atr(14)
atrPercent = atr / close * 100

// ═══════════════════════════════════════════════════════════════════════════════════
// 🔄 TREND & MOMENTUM ANALYSIS - FIXED
// ═══════════════════════════════════════════════════════════════════════════════════
// Trend Direction
emaTrendUp = ema9 > ema21 and ema21 > ema50
emaTrendDown = ema9 < ema21 and ema21 < ema50

// Price Action
bullishMomentum = close > open and close > close[1] and close > ema9
bearishMomentum = close < open and close < close[1] and close < ema9

// ═══════════════════════════════════════════════════════════════════════════════════
// 🎯 ENTRY CONDITIONS - FIXED CONFLICTS
// ═══════════════════════════════════════════════════════════════════════════════════
// Long Setup Components
longRsiCondition = useRsiFilter ? (rsi < rsiOversold and rsi > 20) : true
longPriceCondition = close <= bbLower * 1.005 or (close < ema21 and close > bbLower)
longCondition = longRsiCondition and longPriceCondition and 
               (useTrendFilter ? emaTrendUp : true) and 
               bullishMomentum and 
               highVolume and 
               strategy.position_size == 0 and 
               not bbSqueeze

// Short Setup Components  
shortRsiCondition = useRsiFilter ? (rsi > rsiOverbought and rsi < 80) : true
shortPriceCondition = close >= bbUpper * 0.995 or (close > ema21 and close < bbUpper)
shortCondition = shortRsiCondition and shortPriceCondition and (useTrendFilter ? emaTrendDown : true) and bearishMomentum and highVolume and strategy.position_size == 0 and not bbSqueeze

// ═══════════════════════════════════════════════════════════════════════════════════
// 💰 POSITION SIZING & RISK MANAGEMENT - FIXED VARIABLES
// ═══════════════════════════════════════════════════════════════════════════════════
// Calculate Position Size based on Risk
calculatePositionSize() =>
    accountRisk = strategy.equity * riskPerTrade / 100
    priceRisk = close * slPercent / 100
    posSize = accountRisk / priceRisk
    math.min(posSize, strategy.equity * 0.95 / close) // Max 95% of equity

positionSize = calculatePositionSize()

// TP/SL Levels
var float longEntryPrice = na
var float shortEntryPrice = na
var float longTpPrice = na
var float longSlPrice = na  
var float shortTpPrice = na
var float shortSlPrice = na

// ═══════════════════════════════════════════════════════════════════════════════════
// 🚀 STRATEGY EXECUTION - FIXED
// ═══════════════════════════════════════════════════════════════════════════════════
// Long Entry
if longCondition
    longEntryPrice := close
    longTpPrice := longEntryPrice * (1 + tpPercent / 100)
    longSlPrice := longEntryPrice * (1 - slPercent / 100)
    strategy.entry("Long", strategy.long, qty=positionSize, comment="🟢 LONG")

// Short Entry
if shortCondition  
    shortEntryPrice := close
    shortTpPrice := shortEntryPrice * (1 - tpPercent / 100)
    shortSlPrice := shortEntryPrice * (1 + slPercent / 100)
    strategy.entry("Short", strategy.short, qty=positionSize, comment="🔴 SHORT")

// ═══════════════════════════════════════════════════════════════════════════════════
// 🛡️ EXIT MANAGEMENT - FIXED TRAILING STOP
// ═══════════════════════════════════════════════════════════════════════════════════
// Long Exits
if strategy.position_size > 0
    if useTrailing
        // CORRECTED: Use trail_offset instead of trail_amount
        trailOffset = close * trailingPercent / 100
        strategy.exit("Exit Long", "Long", limit=longTpPrice, trail_offset=trailOffset, comment="✅ TP/Trail")
    else
        strategy.exit("Exit Long", "Long", limit=longTpPrice, stop=longSlPrice, comment="✅ TP/SL")

// Short Exits
if strategy.position_size < 0
    if useTrailing  
        // CORRECTED: Use trail_offset instead of trail_amount
        trailOffset = close * trailingPercent / 100
        strategy.exit("Exit Short", "Short", limit=shortTpPrice, trail_offset=trailOffset, comment="✅ TP/Trail")
    else
        strategy.exit("Exit Short", "Short", limit=shortTpPrice, stop=shortSlPrice, comment="✅ TP/SL")

// EMERGENCY EXITS
emergencyExitLong = strategy.position_size > 0 and rsi > 85
emergencyExitShort = strategy.position_size < 0 and rsi < 15

if emergencyExitLong
    strategy.close("Long", comment="🚨 RSI Exit")
if emergencyExitShort
    strategy.close("Short", comment="🚨 RSI Exit")

// ═══════════════════════════════════════════════════════════════════════════════════
// 🎨 VISUALIZATION - RESTORED
// ═══════════════════════════════════════════════════════════════════════════════════
// EMA Lines
plot(ema9, "EMA 9", color=color.new(#2962FF, 0), linewidth=2)
plot(ema21, "EMA 21", color=color.new(#FF6D00, 0), linewidth=2)
plot(ema50, "EMA 50", color=color.new(#9C27B0, 0), linewidth=1)

// Bollinger Bands
upperBand = plot(bbUpper, "BB Upper", color=color.new(color.gray, 60))
lowerBand = plot(bbLower, "BB Lower", color=color.new(color.gray, 60))
plot(bbBasis, "BB Basis", color=color.new(color.yellow, 70))
fill(upperBand, lowerBand, color=color.new(color.blue, 95), title="BB Background")

// Entry Signals
if showSignals
    plotshape(longCondition, "🟢 BUY", shape.triangleup, location.belowbar, color=color.new(color.lime, 0), size=size.normal)
    plotshape(shortCondition, "🔴 SELL", shape.triangledown, location.abovebar, color=color.new(color.red, 0), size=size.normal)

// TP/SL Levels
if showLevels
    plot(strategy.position_size > 0 ? longTpPrice : na, "Long TP", color=color.new(color.green, 0), style=plot.style_linebr, linewidth=2)
    plot(strategy.position_size > 0 ? longSlPrice : na, "Long SL",  color=color.new(color.red, 0), style=plot.style_linebr, linewidth=2)
    plot(strategy.position_size < 0 ? shortTpPrice : na, "Short TP", color=color.new(color.green, 0), style=plot.style_linebr, linewidth=2)
    plot(strategy.position_size < 0 ? shortSlPrice : na, "Short SL", color=color.new(color.red, 0), style=plot.style_linebr, linewidth=2)

// Background Colors
trendColor = emaTrendUp ? color.new(color.green, 97) : 
             emaTrendDown ? color.new(color.red, 97) : na
bgcolor(trendColor, title="Trend Background")

volumeColor = highVolume ? color.new(color.yellow, 98) : na
bgcolor(volumeColor, title="High Volume")

// ═══════════════════════════════════════════════════════════════════════════════════
// 📊 PERFORMANCE TABLE - RESTORED
// ═══════════════════════════════════════════════════════════════════════════════════
if showTable and barstate.islast
    var table infoTable = table.new(position.top_right, 3, 8, 
                                   bgcolor=color.new(color.white, 80), 
                                   border_width=1,
                                   border_color=color.new(color.gray, 0))
    
    // Calculate Performance Metrics
    totalTrades = strategy.closedtrades
    winRate = totalTrades > 0 ? strategy.wintrades / totalTrades * 100 : 0
    profitFactor = strategy.grossloss > 0 ? strategy.grossprofit / strategy.grossloss : 0
    avgWin = strategy.wintrades > 0 ? strategy.grossprofit / strategy.wintrades : 0
    avgLoss = (totalTrades - strategy.wintrades) > 0 ? strategy.grossloss / (totalTrades - strategy.wintrades) : 0
    maxDD = strategy.max_drawdown
    riskReward = tpPercent / slPercent
    
    // Table Headers
    table.cell(infoTable, 0, 0, "📊 METRIC", text_color=color.black, bgcolor=color.new(color.blue, 80), text_size=size.small)
    table.cell(infoTable, 1, 0, "VALUE", text_color=color.black, bgcolor=color.new(color.blue, 80), text_size=size.small)
    table.cell(infoTable, 2, 0, "STATUS", text_color=color.black, bgcolor=color.new(color.blue, 80), text_size=size.small)
    
    // Performance Data
    table.cell(infoTable, 0, 1, "Total Trades", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 1, str.tostring(totalTrades), text_color=color.black, text_size=size.small)
    table.cell(infoTable, 2, 1, totalTrades > 10 ? "✅" : "⚠️", text_size=size.small)
    
    table.cell(infoTable, 0, 2, "Win Rate", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 2, str.tostring(winRate, "#.#") + "%", 
               text_color=winRate >= 50 ? color.green : color.red, text_size=size.small)
    table.cell(infoTable, 2, 2, winRate >= 50 ? "✅" : "❌", text_size=size.small)
    
    table.cell(infoTable, 0, 3, "Profit Factor", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 3, str.tostring(profitFactor, "#.##"), 
               text_color=profitFactor >= 1.5 ? color.green : profitFactor >= 1.0 ? color.orange : color.red, text_size=size.small)
    table.cell(infoTable, 2, 3, profitFactor >= 1.5 ? "✅" : profitFactor >= 1.0 ? "⚠️" : "❌", text_size=size.small)
    
    table.cell(infoTable, 0, 4, "Net Profit", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 4, "$" + str.tostring(strategy.netprofit, "#"), 
               text_color=strategy.netprofit > 0 ? color.green : color.red, text_size=size.small)
    table.cell(infoTable, 2, 4, strategy.netprofit > 0 ? "✅" : "❌", text_size=size.small)
    
    table.cell(infoTable, 0, 5, "Max Drawdown", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 5, str.tostring(maxDD, "#.#") + "%", text_color=color.red, text_size=size.small)
    table.cell(infoTable, 2, 5, maxDD < 15 ? "✅" : maxDD < 25 ? "⚠️" : "❌", text_size=size.small)
    
    table.cell(infoTable, 0, 6, "Risk:Reward", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 6, "1:" + str.tostring(riskReward, "#.#"), text_color=color.blue, text_size=size.small)
    table.cell(infoTable, 2, 6, riskReward >= 2 ? "✅" : "⚠️", text_size=size.small)
    
    table.cell(infoTable, 0, 7, "Avg Win/Loss", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 7, "$" + str.tostring(avgWin, "#") + "/$" + str.tostring(avgLoss, "#"), 
               text_color=avgWin > avgLoss ? color.green : color.red, text_size=size.small)
    table.cell(infoTable, 2, 7, avgWin > avgLoss ? "✅" : "❌", text_size=size.small)

// ═══════════════════════════════════════════════════════════════════════════════════
// 🔔 ALERTS - RESTORED
// ═══════════════════════════════════════════════════════════════════════════════════
alertcondition(longCondition, title="🟢 Long Entry Signal", message="BUY SIGNAL 🟢\n" + "Symbol: {{ticker}}\n" + "Price: {{close}}\n" + "TP: " + str.tostring(tpPercent) + "%\n" + "SL: " + str.tostring(slPercent) + "%\n" + "Time: {{time}}")

alertcondition(shortCondition, title="🔴 Short Entry Signal", 
               message="SELL SIGNAL 🔴\n" +
                      "Symbol: {{ticker}}\n" + 
                      "Price: {{close}}\n" +
                      "TP: " + str.tostring(tpPercent) + "%\n" +
                      "SL: " + str.tostring(slPercent) + "%\n" +
                      "Time: {{time}}")

alertcondition(ta.change(strategy.position_size) != 0 and strategy.position_size == 0, 
               title="💰 Position Closed", 
               message="Position Closed 💰\n" +
                      "Symbol: {{ticker}}\n" + 
                      "Price: {{close}}\n" +
                      "Time: {{time}}")