body {
font-size: 16px;
color: #ff0000;
}
@font-face {
font-family: Aeonik;
font-style: normal;
font-weight: bold;
src: url(https://staticpacific.blob.core.windows.net/opifex3-dev//tools/fragulizer/config/company/s-volv/font/aeonik-bold.otf);
}
@font-face {
font-family: Aeonik;
font-style: normal;
font-weight: 400;
src: url(/tools/fragulizer/config/company/s-volv/font/aeonik-light.otf);
}
.main-heading {
color: #001eff;
font-size: 28px;
}
.title.second-heading {
color: #001eff;
font-size: 28px;
}
.share-type-selection table {
border-bottom-left-radius: unset;
border-bottom-right-radius: unset;
border-top-left-radius: unset;
border-top-right-radius: unset;
}
.table-share-info {
border-bottom-left-radius: unset;
border-bottom-right-radius: unset;
border-top-left-radius: unset;
border-top-right-radius: unset;
}
.table-share-info .table-of-instruments-header {
color: #ff0000;
}
.table-share-info .table-of-instrument-row {
color: #ff0000;
}
.table-market-share {
border-bottom-left-radius: unset;
border-bottom-right-radius: unset;
border-top-left-radius: unset;
border-top-right-radius: unset;
}
.table-market-share .table-of-market-share-header {
color: #ff0000;
}
.table-market-share .market-share-row {
color: #ff0000;
}
.fragmentation .period-button {
border: unset;
border-bottom-left-radius: 6px;
border-bottom-right-radius: 6px;
border-top-left-radius: 6px;
border-top-right-radius: 6px;
padding-top: 5px;
padding-bottom: 5px;
padding-right: 5px;
padding-left: 5px;
font-size: 10px;
vertical-align: center;
background-color: #ff0000;
color: #ffffff;
border-top-width: 1px;
border-top-color: #0073ff;
border-top-style: solid;
border-bottom-width: 1px;
border-bottom-color: #0073ff;
border-bottom-style: solid;
border-left-width: 1px;
border-left-color: #0073ff;
border-left-style: solid;
border-right-width: 1px;
border-right-color: #0073ff;
border-right-style: solid;
}
.fragmentation .period-button.period-selected {
background-color: #001dc2;
color: #ffffff;
border-top-width: 10px !important;
border-bottom-width: 10px !important;
border-left-width: 10px !important;
border-right-width: 10px !important;
border-top-color: #ff0000 !important;
border-bottom-color: #ff0000 !important;
border-left-color: #ff0000 !important;
border-right-color: #ff0000 !important;
border-top-style: solid !important;
border-bottom-style: solid !important;
border-left-style: solid !important;
border-right-style: solid !important;
}
.show-data {
border-bottom-left-radius: 4px;
border-bottom-right-radius: 4px;
border-top-left-radius: 4px;
border-top-right-radius: 4px;
border: unset;
}
a, .hyperlink {
color: #ff0000;
text-decoration: unset;
}
a:hover, .hyperlink:hover {
color: #ff0000;
}
.footer a {
color: #ff0000;
text-decoration: unset;
}
.footer a:hover {
color: #ff0000;
}
.footer {
font-size: 16px;
color: #ff0000;
}
.time-label, .time, .selected-period {
font-size: 16px;
color: #ff0000;
}
.EUCalendar .EUCalendar-hover-date {
border: unset;
}
.EUCalendar .EUCalendar-bottomBar-today, .EUCalendar .EUCalendar-menu-today {
border-bottom-left-radius: unset;
border-bottom-right-radius: unset;
border-top-left-radius: unset;
border-top-right-radius: unset;
border-top: unset;
border-bottom: unset;
border-left: unset;
border-right: unset;
}
.EUCalendar .EUCalendar-bottomBar-today-hover, .EUCalendar .EUCalendar-menu-today-hover {
border-bottom-left-radius: unset;
border-bottom-right-radius: unset;
border-top-left-radius: unset;
border-top-right-radius: unset;
border-top: unset;
border-bottom: unset;
border-left: unset;
border-right: unset;
}
