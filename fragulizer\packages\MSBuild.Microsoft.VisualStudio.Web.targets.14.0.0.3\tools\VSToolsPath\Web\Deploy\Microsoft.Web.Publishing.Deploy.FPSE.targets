﻿<!--
***********************************************************************************************
Microsoft.Web.Publishing.Deploy.FPSE.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your web deploy projects from the command-line or the IDE.

This file defines the steps in the standard package/publish process for Deploy 
Currently

Copyright (C) Microsoft Corporation. All rights reserved.
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Import task from our dll-->
  <UsingTask TaskName="GetPublishingLocalizedString" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>


  <!--Append WebFTPPublish to the supported list.-->
  <PropertyGroup>
    <_WPPWebPublishMethodSupports>$(_WPPWebPublishMethodSupports);WebFPSEPublish</_WPPWebPublishMethodSupports>
  </PropertyGroup>


  <!--********************************************************************-->
  <!--Target WebFPSEPublish -->
  <!--********************************************************************-->
  <PropertyGroup>
    <WebFPSEPublishDependsOn>
      $(OnBeforeWebFPSEPublish);
      $(WebFPSEPublishDependsOn);
    </WebFPSEPublishDependsOn>
  </PropertyGroup>

  <Target Name="WebFPSEPublish"
          DependsOnTargets="$(WebFPSEPublishDependsOn)"
          Condition="'$(WebFPSEPublish)' != 'False'">

    <!--This is not yet implemented through the command line for now error it out-->
    <GetPublishingLocalizedString
       ID="PublishLocalizedString_WebPublishMethodIsNotSupportedInCmdLine"
       ArgumentCount="1"
       Arguments="$(WebPublishMethod)"
       LogType="Error" />

    <Error  Text ="Target WebFPSEPublish Failed" />

    <CallTarget Targets="$(OnAfterWebFPSEPublish)" RunEachTargetSeparately="False" />
  </Target>


  <!--ImportAfter Extension-->
  <PropertyGroup>
    <ImportByWildcardAfterMicrosoftWebPublishingDeployFPSETargets Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingDeployFPSETargets)'==''">true</ImportByWildcardAfterMicrosoftWebPublishingDeployFPSETargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter\*" Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingDeployFPSETargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter')"/>

</Project>
