<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit http://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>Package</WebPublishMethod>
    <WebRoot>wwwroot</WebRoot>
    <DesktopBuildPackageLocation>$(OutDir)\$(Configuration)\MSDeployPackage\$(MSBuildProjectName).zip</DesktopBuildPackageLocation>
    <DeployIisAppPath>Default Web Site</DeployIisAppPath>
  </PropertyGroup>
</Project>