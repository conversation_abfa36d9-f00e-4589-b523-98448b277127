﻿/**
 * @author: <EMAIL>
 * @date: 17-March-2012
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Diagnostics;

namespace Fragulizer.Common
{
    public class SpeedWatch
    {
        public SpeedWatch()
        {
        }

        public static SpeedWatch Me
        {
            [DebuggerStepThrough]
            get
            {
                return new SpeedWatch();
            }
        }

        public SpeedWatch HowLong(Action onStart, Action<TimeSpan> onStop, Action yourWork)
        {
            Stopwatch stopWatch = Stopwatch.StartNew();
            onStart();
            yourWork();
            stopWatch.Stop();
            onStop(stopWatch.Elapsed);
            return this;
        }
        public TResult HowLong<TResult>(Action onStart, Action<TimeSpan> onStop, Func<TResult> yourWork)
            where TResult: class
        {
            Stopwatch stopWatch = Stopwatch.StartNew();
            onStart();
            TResult result = yourWork();
            stopWatch.Stop();
            onStop(stopWatch.Elapsed);
            return result;
        }
    }
}