﻿<!--
***********************************************************************************************
Microsoft.Web.Publishing.Deploy.Package.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your web deploy projects from the command-line or the IDE.

This file defines the steps in the standard package/publish process for Deploy 
Currently

Copyright (C) Microsoft Corporation. All rights reserved.
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Import task from our dll-->
  <UsingTask TaskName="GetPublishingLocalizedString" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="MSDeploy" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="VSMSDeploy" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="ImportParametersFile" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="ExportParametersFile" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="SortParametrsByPriority" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>


  <!--ImportBefore Extension-->
  <PropertyGroup>
    <ImportByWildcardBeforeMicrosoftWebPublishingDeployPackageTargets Condition="'$(ImportByWildcardBeforeMicrosoftWebPublishingDeployPackageTargets)'==''">true</ImportByWildcardBeforeMicrosoftWebPublishingDeployPackageTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportBefore\*" Condition="'$(ImportByWildcardBeforeMicrosoftWebPublishingDeployPackageTargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportBefore')"/>

  <PropertyGroup>
    <Microsoft_Web_Publishing_MSDeploy_Common_targets Condition="'$(Microsoft_Web_Publishing_MSDeploy_Common_targets)' == ''">Microsoft.Web.Publishing.MSDeploy.Common.targets</Microsoft_Web_Publishing_MSDeploy_Common_targets>
  </PropertyGroup>
  <Import Project="$(Microsoft_Web_Publishing_MSDeploy_Common_targets)" Condition="'$(Microsoft_Web_Publishing_MSDeploy_Common_targets_Imported)' != 'true' And Exists($(Microsoft_Web_Publishing_MSDeploy_Common_targets))"/>

  <PropertyGroup>
    <Microsoft_Web_Publishing_Deploy_Package_targets_Imported>True</Microsoft_Web_Publishing_Deploy_Package_targets_Imported>
    <PipelineDeployPhaseDependsOn>
      $(PipelineDeployPhaseDependsOn);
      PipelineMsdeploySpecificTransformPhase;
    </PipelineDeployPhaseDependsOn>
  </PropertyGroup>
  
  
  


  <!--ImportAfter Extension-->
  <PropertyGroup>
    <ImportByWildcardAfterMicrosoftWebPublishingDeployPackageTargets Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingDeployPackageTargets)'==''">true</ImportByWildcardAfterMicrosoftWebPublishingDeployPackageTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter\*" Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingDeployPackageTargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter')"/>

</Project>
