<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>AnyCPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://gamma.euroland.com/tools/fragulizer/?companycode=default</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>False</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <MSDeployServiceURL>https://ee-v-gamma1.euroland.com:8172/msdeploy.axd</MSDeployServiceURL>
    <DeployIisAppPath>Default Web Site/tools/fragulizer</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>False</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>True</EnableMSDeployBackup>
    <_SavePWD>True</_SavePWD>
    <AllowUntrustedCertificate>True</AllowUntrustedCertificate>
    <UserName>EE-V-GAMMA1\WDeployAdmin2</UserName>
    <EnvironmentName>Gamma</EnvironmentName>
  </PropertyGroup>
  <ItemGroup>
    <MsDeploySkipRules Include="CustomSkipFolder">
      <ObjectName>dirPath</ObjectName>
      <AbsolutePath>Config\\Company</AbsolutePath>
    </MsDeploySkipRules>
    <MsDeploySkipRules Include="CustomSkipFolder">
      <ObjectName>dirPath</ObjectName>
      <AbsolutePath>Styles</AbsolutePath>
    </MsDeploySkipRules>
  </ItemGroup>
</Project>
