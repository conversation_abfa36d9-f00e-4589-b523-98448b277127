﻿<!--
***********************************************************************************************
Microsoft.Web.Publishing.targets  

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your web deploy projects from the command-line or the IDE.

This file defines the steps in the standard build process to deploy web application projects.

Copyright (C) Microsoft Corporation. All rights reserved.
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <UsingTask TaskName="GetProjectProperties" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="TransformXml" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="ParameterizeTransformXml" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="MSDeploy" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="VSMSDeploy" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="CopyPipelineFiles"  AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="GetPipelineDestinationFileNameMatch"  AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="EscapeTextForRegularExpressions" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="RemoveTrailingSlash" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="ConcatFullServiceUrlWithSiteName" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="IsCleanMSDeployPackageNeeded" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="CheckPathAttributes" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="CheckItemsCount" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="CheckItemsForDuplication" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="CreateProviderList" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="CreateDacPac" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="GatherLinkExtensionsToBeExposedInVS" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="MapUriToIisWebServer" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="ImportParametersFile" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="ExportParametersFile" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="SortParametrsByPriority" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="RemoveEmptyDirectories" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="FilterByItems" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="ExportManifestFile" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="GetDeployManagedRuntimeVersion" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="NormalizeServiceUrl" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="SqlScriptPreprocessSqlVariables" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="GetPublishingLocalizedString" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="CollectFilesinFolder" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="GetLocalMachineRegistrySubKeyNames" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="EscapeXMLString" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="DetectRemoteProvider" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="PublishToolsVersionCheck" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="GetFileSkips" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="PublishNuGetPackage" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="CreateAppHostXDTFile" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="VerifyAzureNet46Support" AssemblyFile="Microsoft.Web.Publishing.Tasks.dll"/>

  <!-- The build process can be extended in a similar way as MS.Common.targets -->
  <PropertyGroup>
    <ImportByWildcardBeforeMicrosoftWebPublishingTargets Condition="'$(ImportByWildcardBeforeMicrosoftWebPublishingTargets)'==''">true</ImportByWildcardBeforeMicrosoftWebPublishingTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportBefore\*" Condition="'$(ImportByWildcardBeforeMicrosoftWebPublishingTargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportBefore')"/>

  <!--***************************************************************-->
  <!-- Web Publish Pipeline Version/Supports
       _WPPVersion: Version Stamp 
       _WPPSupports: Function support list-->
  <!--***************************************************************-->
  <PropertyGroup>
    <_WPPVersion>2.0</_WPPVersion>
    <_WPPSupports>IISExpress;AspNetCompileMerge</_WPPSupports>
    <_WPPWebPublishMethodSupports>WebMSDeployPublish;WebPackagePublish</_WPPWebPublishMethodSupports>
  </PropertyGroup>

  <ItemGroup>
    <_WPPSupports Include="$(_WPPSupports)" />
  </ItemGroup>


  <!--Generic mapping for the IISExpress Application Pools to IIS Application Pools common names-->
  <ItemGroup>
    <_IISApplicationPool Include="Clr4IntegratedAppPool">
      <DestinationIISApplicationPool>ASP.NET v4.0</DestinationIISApplicationPool>
    </_IISApplicationPool>
    <_IISApplicationPool Include="Clr4ClassicAppPool">
      <DestinationIISApplicationPool>ASP.NET v4.0 Classic</DestinationIISApplicationPool>
    </_IISApplicationPool>
    <_IISApplicationPool Include="Clr2IntegratedAppPool">
      <DestinationIISApplicationPool>DefaultAppPool</DestinationIISApplicationPool>
    </_IISApplicationPool>
    <_IISApplicationPool Include="Clr2ClassicAppPool">
      <DestinationIISApplicationPool>Classic .NET AppPool</DestinationIISApplicationPool>
    </_IISApplicationPool>
  </ItemGroup>
  
  
  
  <!--***************************************************************-->
  <!-- Setting up properties which MS.Common.targets would normally setup -->
  <!--***************************************************************-->
  <PropertyGroup Condition="'$(BaseIntermediateOutputPath)'=='' ">
    <BaseIntermediateOutputPath >obj\</BaseIntermediateOutputPath>
    <BaseIntermediateOutputPath Condition="!HasTrailingSlash('$(BaseIntermediateOutputPath)')">$(BaseIntermediateOutputPath)\</BaseIntermediateOutputPath>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(IntermediateOutputPath)' == '' ">
    <IntermediateOutputPath Condition=" '$(PlatformName)' == 'AnyCPU' Or '$(PlatformName)' == ''">$(BaseIntermediateOutputPath)$(Configuration)\</IntermediateOutputPath>
    <IntermediateOutputPath Condition=" '$(IntermediateOutputPath)' == '' ">$(BaseIntermediateOutputPath)$(PlatformName)\$(Configuration)\</IntermediateOutputPath>
    <IntermediateOutputPath Condition="!HasTrailingSlash('$(IntermediateOutputPath)')">$(IntermediateOutputPath)\</IntermediateOutputPath>
  </PropertyGroup>

  <!--***************************************************************-->
  <!-- Global settings for Web publish pipeline -->
  <!--***************************************************************-->
  <PropertyGroup>
    <WebPublishPipelineProjectDirectory Condition="'$(WebPublishPipelineProjectDirectory)'==''">$(MSBuildProjectDirectory)</WebPublishPipelineProjectDirectory>
    <WebPublishPipelineSourceRootDirectory Condition="'$(WebPublishPipelineSourceRootDirectory)'==''">$(WebPublishPipelineProjectDirectory)</WebPublishPipelineSourceRootDirectory>
    <WebPublishPipelineTransformRootDirectory Condition="'$(WebPublishPipelineTransformRootDirectory)'==''"></WebPublishPipelineTransformRootDirectory>
    <WebPublishPipelineProjectName Condition="'$(WebPublishPipelineProjectName)'==''">$(MSBuildProjectName)</WebPublishPipelineProjectName>
    <WebPublishPipelineProjectFile Condition="'$(WebPublishPipelineProjectFile)'==''">$(MSBuildProjectFile)</WebPublishPipelineProjectFile>
    <WebPublishPipeLineProjectFullPath Condition="'$(WebPublishPipelineProjectFullPath)'==''">$([System.IO.Path]::GetFullPath($([System.IO.Path]::Combine($(WebPublishPipelineProjectDirectory), $(WebPublishPipelineProjectFile)))))</WebPublishPipeLineProjectFullPath>
    <IsDesktopBuild Condition="'$(IsDesktopBuild)'=='' And '$(TeamFoundationServerUrl)' != ''">False</IsDesktopBuild>
    <WebPublishPipelineWAPProjectSettings Condition="'$(WebPublishPipelineWAPProjectSettings)' ==''">$(WebPublishPipeLineProjectFullPath)</WebPublishPipelineWAPProjectSettings>
    <_WPPDefaultIntermediateOutputPathPathRoot Condition="'$(WebPublishPipelineProjectDirectory)' != '$(MSBuildProjectDirectory)'">$(MSBuildProjectDirectory)\</_WPPDefaultIntermediateOutputPathPathRoot>
    <VerifyDatabaseSettingWithImport Condition="'$(VerifyDatabaseSettingWithImport)' == '' And '$(IgnoreDatabaseSettingOutOfSync)' == 'true'">False</VerifyDatabaseSettingWithImport>
    <VerifyDatabaseSettingWithImport Condition="'$(VerifyDatabaseSettingWithImport)' == '' ">True</VerifyDatabaseSettingWithImport>
    <_LocalDBVersionToUseForSqlExpress Condition="'$(_LocalDBVersionToUseForSqlExpress)' == ''">11.0</_LocalDBVersionToUseForSqlExpress>
    <_PublishProfileSet>False</_PublishProfileSet>
    <EnableWebPublishProfileFile>True</EnableWebPublishProfileFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(_WPPDefaultIntermediateOutputPath)' == ''">
    <_WPPDefaultIntermediateOutputPath>$(IntermediateOutputPath)</_WPPDefaultIntermediateOutputPath>
    <_WPPDefaultIntermediateOutputPath Condition="'$([System.IO.Path]::IsPathRooted($(IntermediateOutputPath)))' == 'False'">$(_WPPDefaultIntermediateOutputPathPathRoot)$(IntermediateOutputPath)</_WPPDefaultIntermediateOutputPath>
    <_WPPDefaultIntermediateOutputFullPath>$([System.IO.Path]::GetFullPath($(_WPPDefaultIntermediateOutputPath)))</_WPPDefaultIntermediateOutputFullPath>
  </PropertyGroup>

  <!--***************************************************************
  	If a publish profile is specified setup the properties to import the file
  ***************************************************************-->
  <PropertyGroup Condition="'$(PublishProfile)' != ''">
    <_PublishProfileExtension>$([System.IO.Path]::GetExtension($(PublishProfile)))</_PublishProfileExtension>
    <_PublishProfileDirectory>$([System.IO.Path]::GetDirectoryName($(PublishProfile)))</_PublishProfileDirectory>
    <_PublishProfileName>$([System.IO.Path]::GetFileNameWithoutExtension($(PublishProfile)))</_PublishProfileName>
    <_PublishProfileSet>True</_PublishProfileSet>
    <DeployDefaultTarget Condition="'$(DeployDefaultTarget)'==''">WebPublish</DeployDefaultTarget>
  </PropertyGroup>

  <PropertyGroup>
    <PublishProfileRootFolder Condition="'$(PublishProfileRootFolder)' == '' and '$(AppDesignerFolder)'!='' ">$(WebPublishPipelineProjectDirectory)\$(AppDesignerFolder)\PublishProfiles</PublishProfileRootFolder>
    <PublishProfileRootFolder Condition="'$(PublishProfileRootFolder)' == '' and '$(MSBuildProjectExtension)' =='.vbproj' ">$(WebPublishPipelineProjectDirectory)\My Project\PublishProfiles</PublishProfileRootFolder>
    <PublishProfileRootFolder Condition="'$(PublishProfileRootFolder)' == '' and '$(MSBuildProjectExtension)' =='.csproj' ">$(WebPublishPipelineProjectDirectory)\Properties\PublishProfiles</PublishProfileRootFolder>
    <PublishProfileRootFolder Condition="'$(PublishProfileRootFolder)' == ''">$(WebPublishPipelineProjectDirectory)\__profiles</PublishProfileRootFolder>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(_PublishProfileExtension)' == '' And '$(_PublishProfileDirectory)' == ''">
    <PublishProfileName Condition="'$(PublishProfileName)' == '' ">$(_PublishProfileName)</PublishProfileName>
  </PropertyGroup>

  <PropertyGroup Condition="'$(PublishProfileName)' !='' And '$(WebPublishProfileFile)'==''">
    <WebPublishProfileFile>$(PublishProfileRootFolder)\$(PublishProfileName).pubxml</WebPublishProfileFile>
    <WebPublishProfileFile Condition="'$(_WebProjectType)' == 'WebSite'">$(WebPublishPipelineProjectDirectory)\App_Data\PublishProfiles\$(PublishProfileName).pubxml</WebPublishProfileFile>
  </PropertyGroup>

  <PropertyGroup Condition="$(_PublishProfileSet) And '$(PublishProfileName)' =='' And '$(WebPublishProfileFile)'==''">
    <WebPublishProfileFile Condition="'$([System.IO.Path]::IsPathRooted($(PublishProfile)))' == 'True'">$(PublishProfile)</WebPublishProfileFile>
    <WebPublishProfileFile Condition="'$([System.IO.Path]::IsPathRooted($(PublishProfile)))' == 'False' And '$([System.IO.File]::Exists($(WebPublishPipelineProjectDirectory)\$(PublishProfile)))'">$(WebPublishPipelineProjectDirectory)\$(PublishProfile)</WebPublishProfileFile>
    <WebPublishProfileFile Condition="'$(WebPublishProfileFile)'==''">$(PublishProfileRootFolder)\$(PublishProfile)</WebPublishProfileFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(PublishProfileName)' == '' And '$(WebPublishProfileFile)' !=''">
    <PublishProfileName>$([System.IO.Path]::GetFileNameWithoutExtension($(WebPublishProfileFile)))</PublishProfileName>
    <_PublishProfileName>$(PublishProfileName)</_PublishProfileName>
  </PropertyGroup>

  <PropertyGroup>
    <_WebPublishProfileFileWillBeImported>false</_WebPublishProfileFileWillBeImported>
    <_WebPublishProfileFileWillBeImported Condition="'$(EnableWebPublishProfileFile)'=='true' And '$(WebPublishProfileFile)' != '' And Exists($(WebPublishProfileFile))">true</_WebPublishProfileFileWillBeImported>
  </PropertyGroup>

  <Import Project="$(WebPublishProfileFile)" Condition="'$(_WebPublishProfileFileWillBeImported)'=='true'"/>

  <PropertyGroup Condition="'$(_WebPublishProfileFileWillBeImported)'=='true'">
    <WebPublishProfileCustomizeTargetFile Condition="'$(WebPublishProfileCustomizeTargetFile)'==''">$([System.IO.Path]::ChangeExtension($(WebPublishProfileFile), '.wpp.targets'))</WebPublishProfileCustomizeTargetFile>
    <WebPublishProfileParametersXMLFile Condition="'$(WebPublishProfileParametersXMLFile)'==''">$([System.IO.Path]::ChangeExtension($(WebPublishProfileFile), '.parameters.xml'))</WebPublishProfileParametersXMLFile>
  </PropertyGroup>

  <Import Project="$(WebPublishProfileCustomizeTargetFile)" Condition="'$(WebPublishProfileCustomizeTargetFile)' != '' And Exists($(WebPublishProfileCustomizeTargetFile)) " />

  <!--***************************************************************-->
  <!-- If there is a file named $(WebPublishPipelineProjectName).wpp.targets it will automatically be imported. -->
  <!-- This allows users to extend the build/publish process for the project -->
  <!--***************************************************************-->
  <PropertyGroup>
    <WebPublishPipelineCustomizeTargetFile Condition="'$(WebPublishPipelineCustomizeTargetFile)'==''">$(WebPublishPipelineProjectDirectory)\*.wpp.targets</WebPublishPipelineCustomizeTargetFile>
    <WebPublishPipelineSolutionTargetFile Condition="'$(WebPublishPipelineSolutionTargetFile)'==''">$(WebPublishPipelineProjectDirectory)\..\wpp.deploysettings.targets</WebPublishPipelineSolutionTargetFile>
  </PropertyGroup>
  <Import Project="$(WebPublishPipelineCustomizeTargetFile)" Condition="'$(WebPublishPipelineCustomizeTargetFile)' != ''"/>
  <Import Project="$(WebPublishPipelineSolutionTargetFile)" Condition="'$(WebPublishPipelineSolutionTargetFile)' != '' And Exists($(WebPublishPipelineSolutionTargetFile))"/>

  <!--***************************************************************-->
  <!--Globals settings for the Clean target -->
  <!--***************************************************************-->
  <PropertyGroup>
    <_WPPCleanTargets>
      $(_WPPCleanTargets);
      CleanWebsitesPackage;
      CleanWebsitesWPPAllFilesInSingleFolder;
      CleanWebPublishPipelineIntermediateOutput;
    </_WPPCleanTargets>
    <CleanDependsOn>
      $(CleanDependsOn);
      $(_WPPCleanTargets);
    </CleanDependsOn>
  </PropertyGroup>

  <!-- Global settings for file collection -->
  <PropertyGroup Condition="'$(WebPublishPipelineProjectDirectory)' == '$(MSBuildProjectDirectory)'">
    <FilesToIncludeForPublish Condition="'$(FilesToIncludeForPublish)'==''">OnlyFilesToRunTheApp</FilesToIncludeForPublish>
    <AlternativeProjectDirectory></AlternativeProjectDirectory>
  </PropertyGroup>

  <PropertyGroup Condition="'$(WebPublishPipelineProjectDirectory)' != '$(MSBuildProjectDirectory)'">
    <FilesToIncludeForPublish Condition="'$(FilesToIncludeForPublish)'==''">AllFilesInProjectFolder</FilesToIncludeForPublish>
    <AlternativeProjectDirectory>$(WebPublishPipelineProjectDirectory)</AlternativeProjectDirectory>
    <AlternativeProjectDirectory Condition="!HasTrailingSlash('$(AlternativeProjectDirectory)')">$(AlternativeProjectDirectory)</AlternativeProjectDirectory>
  </PropertyGroup>

  <PropertyGroup>
    <!-- For website project we will always exclude debug symbols from publishing. This can be overridden in the .pubxml file. -->
    <ExcludeGeneratedDebugSymbol Condition="'$(ExcludeGeneratedDebugSymbol)'=='' And '$(_WebProjectType)' == 'WebSite'">True</ExcludeGeneratedDebugSymbol>
    
    <ExcludeGeneratedDebugSymbol Condition="'$(ExcludeGeneratedDebugSymbol)'=='' And '$(Configuration)' == 'Release'">True</ExcludeGeneratedDebugSymbol>
    <ExcludeGeneratedDebugSymbol Condition="'$(ExcludeGeneratedDebugSymbol)'==''">False</ExcludeGeneratedDebugSymbol>

    <!-- This will control whether XML files from ResolveAssemblyReference is published, default is true (to exclude) -->
    <ExcludeXmlAssemblyFiles Condition=" '$(ExcludeXmlAssemblyFiles)'=='' ">true</ExcludeXmlAssemblyFiles>
    
    <ExcludeApp_Data Condition="'$(ExcludeApp_Data)'==''">False</ExcludeApp_Data>
    <SkipApp_DataFolder Condition="'$(SkipApp_DataFolder)'==''">$(ExcludeApp_Data)</SkipApp_DataFolder>
    <UseMsdeployExe Condition="'$(UseMsdeployExe)'==''">False</UseMsdeployExe>

    <!-- Determines if Linked files should be collected during the PipelineCollectFilesPhase -->
    <EnableCollectLinkFilesInProject Condition="'$(EnableCollectLinkFilesInProject)'==''">True</EnableCollectLinkFilesInProject>
    <ExcludeLinkFilesInProject Condition="'$(ExcludeLinkFilesInProject)'==''">False</ExcludeLinkFilesInProject>

    <!-- If $(CleanWPPAllFilesInSingleFolder) is explicit set, we should set $(_CleanWPPIfNeedTo) accordingly -->
    <_CleanWPPIfNeedTo Condition="'$(_CleanWPPIfNeedTo)' == '' And '$(CleanWPPAllFilesInSingleFolder)'!=''">$(CleanWPPAllFilesInSingleFolder)</_CleanWPPIfNeedTo>
    <CleanWPPAllFilesInSingleFolder Condition="'$(CleanWPPAllFilesInSingleFolder)'==''">False</CleanWPPAllFilesInSingleFolder>
    
    <IncludeIisSettings Condition="'$(IncludeIisSettings)'==''">False</IncludeIisSettings>
    <!-- Determines if the files in @(ExcludeFilesFromPackage) will be excluded -->
    <ExcludeFilesFromPackage Condition="'$(ExcludeFilesFromPackage)'==''">True</ExcludeFilesFromPackage>
    <!-- Determines if the parameters.xml file will be merged into @(ParametersXMLFiles) -->
    <ImportParametersFiles  Condition="'$(ImportParametersFiles)'==''">True</ImportParametersFiles>
    <ImportParametersFile_DisableEscapeMSBuildVariable Condition="'$(ImportParametersFile_DisableEscapeMSBuildVariable)' == ''" >False</ImportParametersFile_DisableEscapeMSBuildVariable>
    <ImportProjectParametersFiles Condition="'$(ImportProjectParametersFiles)'==''">True</ImportProjectParametersFiles>
    <ImportProfileParametersFiles Condition="'$(ImportProfileParametersFiles)'==''">True</ImportProfileParametersFiles>
    <ImportPublishingParameterValues  Condition="'$(ImportPublishingParameterValues)'==''">True</ImportPublishingParameterValues>
    <EnableOptimisticParameterDefaultValue Condition="'$(EnableOptimisticParameterDefaultValue)'==''">True</EnableOptimisticParameterDefaultValue>
    <ParameterApplicationPool Condition="'$(ParameterApplicationPool)'==''">True</ParameterApplicationPool>
    <IIS6ParameterApplicationPool Condition="'$(IIS6ParameterApplicationPool)'==''">False</IIS6ParameterApplicationPool>
    <!-- This allows disabling the generation of the MSDeploy parameters -->
    <DisableAllVSGeneratedMSDeployParameter Condition="'$(DisableAllVSGeneratedMSDeployParameter)'==''">False</DisableAllVSGeneratedMSDeployParameter>
    <!-- Setting this to false will disable the $(name) tokenization of connection strings in web.config when a package is created -->
    <AutoParameterizationWebConfigConnectionStrings  Condition="'$(AutoParameterizationWebConfigConnectionStrings)'==''">True</AutoParameterizationWebConfigConnectionStrings>
    <AutoParameterizationWebConfigCSNoDefaultValue  Condition="'$(AutoParameterizationWebConfigCSNoDefaultValue)'==''">False</AutoParameterizationWebConfigCSNoDefaultValue>
    <AutoParameterizationWebConfigConnectionStringsIntermediateOutput Condition="'$(AutoParameterizationWebConfigConnectionStringsIntermediateOutput)'==''">CSAutoParameterize</AutoParameterizationWebConfigConnectionStringsIntermediateOutput>
    <AutoParameterizationWebConfigConnectionStringsLocation Condition="'$(AutoParameterizationWebConfigConnectionStringsLocation)'==''">$(_WPPDefaultIntermediateOutputPath)$(AutoParameterizationWebConfigConnectionStringsIntermediateOutput)</AutoParameterizationWebConfigConnectionStringsLocation>

    <_WPPLastBuildInfoIntermediateOutput Condition="'$(_WPPLastBuildInfoIntermediateOutput)'==''">_WPPLastBuildInfo.txt</_WPPLastBuildInfoIntermediateOutput>
    <_WPPLastBuildInfoLocation Condition="'$(_WPPLastBuildInfoLocation)'==''">$(_WPPDefaultIntermediateOutputPath)$(_WPPLastBuildInfoIntermediateOutput)</_WPPLastBuildInfoLocation>

    <InsertAdditionalWebCofigConnectionStrings Condition="'$(InsertAdditionalWebCofigConnectionStrings)' == ''">True</InsertAdditionalWebCofigConnectionStrings>
    <InsertAdditionalWebCofigConnectionStringsIntermediateOutput Condition="'$(InsertAdditionalWebCofigConnectionStringsIntermediateOutput)'==''">InsertAdditionalCS</InsertAdditionalWebCofigConnectionStringsIntermediateOutput>
    <InsertAdditionalWebCofigConnectionStringsLocation Condition="'$(InsertAdditionalWebCofigConnectionStringsLocation)'==''">$(_WPPDefaultIntermediateOutputPath)$(InsertAdditionalWebCofigConnectionStringsIntermediateOutput)</InsertAdditionalWebCofigConnectionStringsLocation>
    <InsertAdditionalWebConfigConnectionStringProviderName Condition="'$(InsertAdditionalWebConfigConnectionStringProviderName)'==''">System.Data.SqlClient</InsertAdditionalWebConfigConnectionStringProviderName>
    <InsertAdditionalWebConfigConnectionStringOnlyInRoot Condition="'$(InsertAdditionalWebConfigConnectionStringOnlyInRoot)'==''">True</InsertAdditionalWebConfigConnectionStringOnlyInRoot>

    <!-- For added .sql files this property determines if the script will be contained in a transaction. -->
    <SqlScriptSourceTransacted Condition="'$(SqlScriptSourceTransacted)'==''"></SqlScriptSourceTransacted>
    <!-- For the automatically generated SQL script for legacy SQL publishing this determines if the generated script will be
    transactional or not. -->
    <AutoSqlScriptSourceTransacted Condition="'$(AutoSqlScriptSourceTransacted)'==''"></AutoSqlScriptSourceTransacted>

    <DatabaseDeployIntermediateRelativePath Condition="'$(DatabaseDeployIntermediateRelativePath)'==''">Database</DatabaseDeployIntermediateRelativePath>
    <DatabaseDeployIntermediateOutputPath Condition="'$(DatabaseDeployIntermediateOutputPath)'==''">$(_WPPDefaultIntermediateOutputPath)$(DatabaseDeployIntermediateRelativePath)</DatabaseDeployIntermediateOutputPath>
    <_WebConfigsToAutoParmeterizeCsTransformOutputParametersFile>$(AutoParameterizationWebConfigConnectionStringsLocation).parameters.xml</_WebConfigsToAutoParmeterizeCsTransformOutputParametersFile>
    
    <!-- This is for backw compat that /p:DeployOnBuild=true without publish profile, it will fall back to package -->
    <DeployDefaultTarget Condition="'$(DeployDefaultTarget)'==''">Package</DeployDefaultTarget>
    <DeployTarget Condition="'$(DeployTarget)'==''">$(DeployDefaultTarget)</DeployTarget>
    <RetryAttemptsForDeployment Condition="'$(RetryAttemptsForDeployment)'==''">2</RetryAttemptsForDeployment>
    <DeployOnBuildDefault>False</DeployOnBuildDefault>
    <DeployOnBuild Condition="'$(DeployOnBuild)'==''">$(DeployOnBuildDefault)</DeployOnBuild>
    <EnableMSDeployBackup Condition=" '$(EnableMSDeployBackup)'=='' ">false</EnableMSDeployBackup>
    <EnableMSDeployAppOffline Condition="'$(EnableMSDeployAppOffline)'==''">false</EnableMSDeployAppOffline>
    <MSDeployUseChecksum Condition=" '$(MSDeployUseChecksum)'=='' ">false</MSDeployUseChecksum>

    <!-- Setting this true will enable encryption of the web.config file with using the default encryption provider. An optional custom 
         encryption provider can be specified in $(MSDeployWebConfigEncryptProvider) 
    -->
    <MSDeployEnableWebConfigEncryptRule Condition=" '$(MSDeployEnableWebConfigEncryptRule)'=='' ">False</MSDeployEnableWebConfigEncryptRule>
    <MSDeployWebConfigEncryptProvider Condition=" '$(MSDeployWebConfigEncryptProvider)'=='' "></MSDeployWebConfigEncryptProvider>
  </PropertyGroup>

  <!-- UserAgent string sent to msdeploy -->
  <PropertyGroup>
    <_MSDeployUserAgentSource Condition=" '$(BuildingInsideVisualStudio)' != 'true'">$(VisualStudioVersion):CmdLine</_MSDeployUserAgentSource>
    <_MSDeployUserAgentSource Condition=" '$(BuildingInsideVisualStudio)' == 'true'">$(VisualStudioVersion):PublishDialog</_MSDeployUserAgentSource>
    <_MSDeployUserAgent>VS$(_MSDeployUserAgentSource)</_MSDeployUserAgent>
  </PropertyGroup>

  <!-- Special flag for WAP to enable _CopyWebApplication to use Web Publishing Pipeline -->
  <PropertyGroup>
    <UseWPP_CopyWebApplication Condition="'$(UseWPP_CopyWebApplication)'==''">False</UseWPP_CopyWebApplication>
  </PropertyGroup>
  
  <PropertyGroup>
    <!-- Determines if the publish process will take a dependency on Build -->
    <PipelineDependsOnBuild Condition="'$(PipelineDependsOnBuild)'=='' AND '$(BuildingInsideVisualStudio)' != 'true'">True</PipelineDependsOnBuild>
    <PipelineDependsOnBuild Condition="'$(PipelineDependsOnBuild)'==''">False</PipelineDependsOnBuild>

    <_DeployOnBuild>$(DeployOnBuild)</_DeployOnBuild>
    <!-- When building in VS the publish process is handled directly in VS-->
    <_DeployOnBuild Condition="'$(BuildingInsideVisualStudio)' == 'true'">False</_DeployOnBuild>

    <!-- If _DeployOnBuild is true we should ensure that it executes after PrepareForRun -->
     <PipelineDeployPhaseAfterTargets Condition="'$(PipelineDeployPhaseAfterTargets)' == '' And $(_DeployOnBuild) ">
      PrepareForRun;
    </PipelineDeployPhaseAfterTargets>    

    <!-- We use the following property to detect BuildCircularDependency that we might introduce in the msbuild due to 
    $(UseWPP_CopyWebApplication) and $(PipelineDependsOnBuild) both set to true.
    Instead of letting msbuild failed error early with no message, we can capture it and provide a better error. -->
    <WPPCopyWebApplicaitonPipelineCircularDependencyError>False</WPPCopyWebApplicaitonPipelineCircularDependencyError>
    <WPPCopyWebApplicaitonPipelineCircularDependencyError Condition="$(UseWPP_CopyWebApplication) And !$(Disable_CopyWebApplication)  And $(PipelineDependsOnBuild) And !$(_DeployOnBuild)">True</WPPCopyWebApplicaitonPipelineCircularDependencyError>
    <PipelineDependsOnBuild Condition="$(WPPCopyWebApplicaitonPipelineCircularDependencyError)">False</PipelineDependsOnBuild>

    <!-- Setup the minimum set of targets required -->
    <PipelineDependsOn Condition="!$(PipelineDependsOnBuild)">
      $(PipelineDependsOn);
      BeforeBuild;
      BuildOnlySettings;
      ResolveReferences;
      PrepareResourceNames;
      ComputeIntermediateSatelliteAssemblies;
      GetCopyToOutputDirectoryItems;
      _SGenCheckForOutputs;
    </PipelineDependsOn>

    <PipelineDependsOn Condition="$(PipelineDependsOnBuild) And !$(_DeployOnBuild)">
      $(PipelineDependsOn);
      Build;
    </PipelineDependsOn>
  
    <PipelineDependsOn Condition="!$(PipelineDependsOnBuild) And !$(_DeployOnBuild)" >
      $(PipelineDependsOn);
      AfterBuild;
    </PipelineDependsOn>
  </PropertyGroup>

  <!--***************************************************************-->
  <!-- Settings relating to the web.config transform -->
  <!--***************************************************************-->
  <PropertyGroup>
    <ProjectConfigFileName Condition="'$(ProjectConfigFileName)'==''">Web.config</ProjectConfigFileName>
    <_ProjectConfigFileExtension>$([System.IO.Path]::GetExtension($(ProjectConfigFileName)))</_ProjectConfigFileExtension>
    <_ProjectConfigFilePrefix>$([System.IO.Path]::GetFileNameWithoutExtension($(ProjectConfigFileName)))</_ProjectConfigFilePrefix>
  </PropertyGroup>
  <PropertyGroup>
    <ProjectConfigTransformFileName Condition="'$(ProjectConfigTransformFileName)'==''">$(_ProjectConfigFilePrefix).$(Configuration)$(_ProjectConfigFileExtension)</ProjectConfigTransformFileName>
    <TransformWebConfigEnabled Condition="'$(TransformWebConfigEnabled)'==''">true</TransformWebConfigEnabled>
    <TransformWebConfigStackTraceEnabled Condition="'$(TransformWebConfigStackTraceEnabled)'==''">False</TransformWebConfigStackTraceEnabled>
    <UseParameterizeToTransformWebConfig Condition="'$(UseParameterizeToTransformWebConfig)'==''">False</UseParameterizeToTransformWebConfig>
    <TransformWebConfigIntermediateOutput Condition="'$(TransformWebConfigIntermediateOutput)'==''">TransformWebConfig</TransformWebConfigIntermediateOutput>
    <TransformWebConfigIntermediateLocation Condition="'$(TransformWebConfigIntermediateLocation)'==''">$(_WPPDefaultIntermediateOutputPath)$(TransformWebConfigIntermediateOutput)</TransformWebConfigIntermediateLocation>
    <_WebConfigTransformOutputParametersFile>$(TransformWebConfigIntermediateLocation).parameters.xml</_WebConfigTransformOutputParametersFile>

    <ProjectProfileTransformFileName Condition="'$(ProjectProfileTransformFileName)'=='' And '$(PublishProfileName)' != '' ">$(_ProjectConfigFilePrefix).$(PublishProfileName)$(_ProjectConfigFileExtension)</ProjectProfileTransformFileName>

    <!--if $(TransformWebConfigEnabled) is also enabled and the ConfigTransform and ProfileTransform happen to have same filename, we default $(ProfilefileTransformWebCofnigEnabled) to false so it doesn't do double transform-->
    <ProfileTransformWebConfigEnabled Condition="'$(ProfileTransformWebConfigEnabled)'=='' And '$(TransformWebConfigEnabled)' == 'true' And ('$(ProjectProfileTransformFileName)' == '$(ProjectConfigTransformFileName)')">False</ProfileTransformWebConfigEnabled>
    <ProfileTransformWebConfigEnabled Condition="'$(ProfileTransformWebConfigEnabled)'=='' And '$(TransformWebConfigEnabled)' == 'false'">False</ProfileTransformWebConfigEnabled>
    <ProfileTransformWebConfigEnabled Condition="'$(ProfileTransformWebConfigEnabled)'==''">true</ProfileTransformWebConfigEnabled>
    <ProfileTransformWebConfigStackTraceEnabled Condition="'$(ProfileTransformWebConfigStackTraceEnabled)'==''">False</ProfileTransformWebConfigStackTraceEnabled>
    <UseParameterizeToProfileTransformWebConfig Condition="'$(UseParameterizeToProfileTransformWebConfig)'==''">False</UseParameterizeToProfileTransformWebConfig>
    <ProfileTransformWebConfigIntermediateOutput Condition="'$(ProfileTransformWebConfigIntermediateOutput)'==''">ProfileTransformWebConfig</ProfileTransformWebConfigIntermediateOutput>
    <ProfileTransformWebConfigIntermediateLocation Condition="'$(ProfileTransformWebConfigIntermediateLocation)'==''">$(_WPPDefaultIntermediateOutputPath)$(ProfileTransformWebConfigIntermediateOutput)</ProfileTransformWebConfigIntermediateLocation>
    <_ProfileWebConfigTransformOutputParametersFile>$(ProfileTransformWebConfigIntermediateLocation).parameters.xml</_ProfileWebConfigTransformOutputParametersFile>
    
    <ParameterizeTransformXmlUseXPath Condition="'$(ParameterizeTransformXmlUseXPath)'==''">True</ParameterizeTransformXmlUseXPath>
  </PropertyGroup>

  <!--***************************************************************-->
  <!-- Overwrite WAP default for _CopyWebApplication -->
  <!--***************************************************************-->
  <PropertyGroup>
    <!-- Required in case the WAP project doesn't exist -->
    <WebProjectOutputDirInsideProjectDefault>True</WebProjectOutputDirInsideProjectDefault>
    <WebProjectOutputDirInsideProjectDefault  Condition="('$(OutDir)' != '$(OutputPath)') Or ('$(IsDesktopBuild)' == 'False')" >False</WebProjectOutputDirInsideProjectDefault>
    <WebProjectOutputDirInsideProject Condition="'$(WebProjectOutputDirInsideProject)' == ''">$(WebProjectOutputDirInsideProjectDefault)</WebProjectOutputDirInsideProject>
    <CleanWebProjectOutputDir>True</CleanWebProjectOutputDir>
    <CleanWebProjectOutputDir Condition="$(WebProjectOutputDirInsideProject)" >False</CleanWebProjectOutputDir>
  </PropertyGroup>

  <!--***************************************************************-->
  <!-- Global settings for where the web package will be created for Team Build -->
  <!--***************************************************************-->
  <PropertyGroup>
    <!--We need the following in case WAP project does not exist-->
    <DefaultPackageOutputDir Condition="'$(DefaultPackageOutputDir)'=='' And !$(WebProjectOutputDirInsideProject) ">$(OutDir)_PublishedWebsites\$(WebPublishPipelineProjectName)_Package</DefaultPackageOutputDir>
    <DefaultPackageOutputDir Condition="'$(DefaultPackageOutputDir)'==''">$(_WPPDefaultIntermediateOutputPath)Package</DefaultPackageOutputDir>
    <DefaultPackageFileName Condition="'$(DefaultPackageFileName)'==''">$(WebPublishPipelineProjectName).zip</DefaultPackageFileName>
    <DefaultMSDeployDestinationSite Condition="'$(DefaultMSDeployDestinationSite)'==''">Default Web Site</DefaultMSDeployDestinationSite>
    <DefaultMsDeployAltSuffix Condition="'$(DefaultMsDeployAltSuffix)'==''">_deploy</DefaultMsDeployAltSuffix>
    <DefaultMSDeployDestinationApplicationName Condition="'$(DefaultMSDeployDestinationApplicationName)'==''">$(WebPublishPipelineProjectName)</DefaultMSDeployDestinationApplicationName>
    <DefaultDeployIisAppPath Condition="'$(DefaultDeployIisAppPath)'==''">$(DefaultMSDeployDestinationSite)/$(DefaultMSDeployDestinationApplicationName)$(DefaultMsDeployAltSuffix)</DefaultDeployIisAppPath>
    <DefaultDeployIisRootAppPath Condition="'$(DefaultDeployIisRootAppPath)'==''">$(DefaultMSDeployDestinationApplicationName)$(DefaultMsDeployAltSuffix)</DefaultDeployIisRootAppPath>
  </PropertyGroup>

  <!--***************************************************************-->
  <!--Global setting for controling the package creation process -->
  <!--***************************************************************-->
  <PropertyGroup>
    <PackageAsSingleFile Condition="'$(PackageAsSingleFile)'==''">True</PackageAsSingleFile>
    <!-- DesktopBuildPackageLocation is been set by VS UI -->
    <PackageLocation Condition="'$(PackageLocation)'=='' and !(('$(OutDir)' != '$(OutputPath)') Or ('$(IsDesktopBuild)' == 'False')) ">$(DesktopBuildPackageLocation)</PackageLocation>
    <PackageFileName Condition="'$(PackageFileName)'=='' And $(PackageAsSingleFile) And '$(PackageLocation)' != ''">$(PackageLocation)</PackageFileName>
    <PackageFileName Condition="'$(PackageFileName)'!='' And $(PackageAsSingleFile) And '$([System.IO.Path]::GetExtension($(PackageFileName)))' == '' ">$(PackageFileName)\$(DefaultPackageFileName)</PackageFileName>  
    <PackageFileName Condition="'$(PackageFileName)'==''">$(DefaultPackageOutputDir)\$(DefaultPackageFileName)</PackageFileName>
    <PackageArchiveRootDir Condition="'$(PackageArchiveRootDir)'=='' And !$(PackageAsSingleFile) And '$(PackageLocation)' != ''">$(PackageLocation)</PackageArchiveRootDir>
    <PackageArchiveRootDir Condition="'$(PackageArchiveRootDir)'==''">$(DefaultPackageOutputDir)</PackageArchiveRootDir>
    <PackageTempRootDir Condition="'$(PackageTempRootDir)'==''">$(_WPPDefaultIntermediateOutputPath)Package</PackageTempRootDir>
    <!-- This will determine if the published content will be marked as an IIS application or simply a folder -->
    <DeployAsIisApp Condition="'$(DeployAsIisApp)'==''">true</DeployAsIisApp>
    <IncludeSetAclProviderOnDestination Condition="'$(IncludeSetAclProviderOnDestination)'==''">True</IncludeSetAclProviderOnDestination>
    <MarkApp_DataWritableOnDestination Condition="'$(MarkApp_DataWritableOnDestination)'==''">True</MarkApp_DataWritableOnDestination>
    <IgnoreDeployManagedRuntimeVersion Condition="'$(IgnoreDeployManagedRuntimeVersion)'==''">False</IgnoreDeployManagedRuntimeVersion>
    <DeployDefaultTargetFrameworkVersion Condition="'$(DeployDefaultTargetFrameworkVersion)'==''">$(TargetFrameworkVersion)</DeployDefaultTargetFrameworkVersion>
    <DeployManagedRuntimeVersion Condition="'$(DeployManagedRuntimeVersion)'==''"></DeployManagedRuntimeVersion>
    <DeployEnable32bitAppOnWin64 Condition="'$(DeployEnable32bitAppOnWin64)'==''"></DeployEnable32bitAppOnWin64>
    <DeployManagedPipelineMode Condition="'$(DeployManagedPipelineMode)'==''"></DeployManagedPipelineMode>    
    <!-- Determines if the deploy.cmd file will be written out -->
    <GenerateSampleDeployScript Condition="'$(GenerateSampleDeployScript)'==''">True</GenerateSampleDeployScript>
    <PackageLogDir Condition="'$(PackageLogDir)'==''">$(DefaultPackageOutputDir)\Log</PackageLogDir>
    <EnablePackageProcessLoggingAndAssert Condition="'$(EnablePackageProcessLoggingAndAssert)'==''">False</EnablePackageProcessLoggingAndAssert>
    <!-- PackageTraceLevel 3 different levels:  Error, Warning, Info (From System.Diagnostic.TraceLevel, Default to Info) -->
    <PackageTraceLevel Condition="'$(PackageTraceLevel)'==''">Info</PackageTraceLevel>
    <_CreatePackage Condition="'$(_CreatePackage)'==''" >True</_CreatePackage>
    <_UseDefaultLinkExtensionValue>False</_UseDefaultLinkExtensionValue>
    <_UseDefaultLinkExtensionValue Condition="'$(PackageEnableLinks)'=='' and '$(PackageDisableLinks)'==''">True</_UseDefaultLinkExtensionValue>
    <PackageEnableLinks Condition="$(_UseDefaultLinkExtensionValue)"></PackageEnableLinks>
    <PackageDisableLinks Condition="$(_UseDefaultLinkExtensionValue)">AppPoolExtension;ContentExtension;CertificateExtension</PackageDisableLinks>
    <PackageEnableLinks Condition="'$(IncludeAppPool)'=='true' and $(_UseDefaultLinkExtensionValue)">AppPoolExtension</PackageEnableLinks>
    <PackageDisableLinks Condition="'$(IncludeAppPool)'=='true' and $(_UseDefaultLinkExtensionValue)">ContentExtension;CertificateExtension</PackageDisableLinks>
    <ProjectParametersXMLFile Condition="'$(ProjectParametersXMLFile)'=='' And Exists('$(MSBuildProjectDirectory)\Parameters.xml')">$(MSBuildProjectDirectory)\Parameters.xml</ProjectParametersXMLFile>
    <ProjectParametersXMLFile Condition="'$(ProjectParametersXMLFile)'==''">$(WebPublishPipelineProjectDirectory)\Parameters.xml</ProjectParametersXMLFile>
    <EnableProjectDeployParameterPrefix Condition="'$(EnableProjectDeployParameterPrefix)'==''">False</EnableProjectDeployParameterPrefix>
    <DeployParameterPrefix Condition="$(EnableProjectDeployParameterPrefix) and '$(DeployParameterPrefix)'==''">$(WebPublishPipelineProjectName) </DeployParameterPrefix>
    <DeployParameterIISAppName Condition="'$(DeployParameterIISAppName)'==''">$(DeployParameterPrefix)IIS Web Application Name</DeployParameterIISAppName>
    <DeployParameterIISAppPoolName Condition="'$(DeployParameterIISAppPoolName)'==''">$(DeployParameterPrefix)IIS Web Application Pool Name</DeployParameterIISAppPoolName>
    <DeployParameterApp_DataWritePermission Condition="'$(DeployParameterApp_DataWritePermission)'==''">$(DeployParameterPrefix)Add write permission to App_Data Folder</DeployParameterApp_DataWritePermission>
    <DeployParameterIISAppPhysicalPath Condition="'$(DeployParameterIISAppPhysicalPath)'==''">$(DeployParameterPrefix)Web Application Physical Path</DeployParameterIISAppPhysicalPath>
    
    <DeployParameterAutoDescriptionbyTags Condition="'$(DeployParameterAutoDescriptionbyTags)'==''">True</DeployParameterAutoDescriptionbyTags>
    <DeployParameterIISAppNameDescription Condition="'$(DeployParameterIISAppNameDescription)'==''"></DeployParameterIISAppNameDescription>
    <DeployParameterIISAppPoolNameDescription Condition="'$(DeployParameterIISAppPoolNameDescription)'==''"></DeployParameterIISAppPoolNameDescription>
    <DeployParameterIISAppPhysicalPathDescription Condition="'$(DeployParameterIISAppPhysicalPathDescription)'==''"></DeployParameterIISAppPhysicalPathDescription>
    <DeployParameterIISAppConnectionStringDescription Condition="'$(DeployParameterIISAppConnectionStringDescription)'==''"></DeployParameterIISAppConnectionStringDescription>
    <DeployParameterSqlScriptVariablesDescription Condition="'$(DeployParameterSqlScriptVariablesDescription)'==''"></DeployParameterSqlScriptVariablesDescription>
    
    <MsDeployDatabaseTag Condition="'$(MsDeployDatabaseTag)'==''">SqlConnectionString</MsDeployDatabaseTag>
    <MsDeploySqlCommandVariableKind Condition="'$(MsDeploySqlCommandVariableKind)'==''">SqlCommandVariable</MsDeploySqlCommandVariableKind>
    
    <!-- Properties relating to checking SQL scripts for commands which MSDeploy does not support -->
    <MsdeploySqlUnsupportedCommand Condition="'$(MsdeploySqlUnsupportedCommand)'==''">:Connect;:!!;:Exit;:Help;:r;:Quit;</MsdeploySqlUnsupportedCommand>
    <CheckSqlScriptForUnsupportedCommands Condition="'$(CheckSqlScriptForUnsupportedCommands)'==''">True</CheckSqlScriptForUnsupportedCommands>

    <!-- The following attributes will be preserved from connection string in web.config to the dest connection string on publish -->
    <UpdateFromConnectionStringAttributes Condition="'$(UpdateFromConnectionStringAttributes)' == ''">MultipleActiveResultSets;App;</UpdateFromConnectionStringAttributes>
   
    <!-- Determines if unsupported SQL commands should be treated an an error or warning -->
    <TreadSqlScriptUnsupportedCommandsAsWarning Condition="'$(TreadSqlScriptUnsupportedCommandsAsWarning)'==''">False</TreadSqlScriptUnsupportedCommandsAsWarning>
    <_PackageTempDir Condition ="'$(_PackageTempDir)' == '' ">$(PackageTempRootDir)\PackageTmp</_PackageTempDir>
    <WPPAllFilesInSingleFolder Condition="'$(WPPAllFilesInSingleFolder)' == ''">$(_PackageTempDir)</WPPAllFilesInSingleFolder>
    <IsWPPAllFilesInSingleFolder>False</IsWPPAllFilesInSingleFolder>
  </PropertyGroup>

  <PropertyGroup>
    <EnableSqlScriptVariableParameterize Condition="'$(EnableSqlScriptVariableParameterize)'==''">True</EnableSqlScriptVariableParameterize>
    <!-- The default BatchDelimiter is "Go", "" is the same for ManagedBatchParser.Parser -->
    <SqlScriptPreProcessBatchDelimiter Condition="'$(SqlScriptPreProcessBatchDelimiter)'==''"></SqlScriptPreProcessBatchDelimiter>
    <SqlScriptPreProcessResolveIncludes Condition="'$(SqlScriptPreProcessResolveIncludes)'==''">False</SqlScriptPreProcessResolveIncludes>
  </PropertyGroup>

  <ItemGroup>
    <ProjectParametersXMLFiles Condition="Exists($(ProjectParametersXMLFile))" Include="$(ProjectParametersXMLFile)" />
    <WebPublishProfileParametersXMLFiles Condition="'$(WebPublishProfileParametersXMLFile)' != '' And Exists($(WebPublishProfileParametersXMLFile))" Include="$(WebPublishProfileParametersXMLFile)" />
    <ParametersXMLFiles Include="@(ProjectParametersXMLFiles)" />
    <ProfileParametersXMLFiles  Include="@(WebPublishProfileParametersXMLFiles)" />
  </ItemGroup>

  <!--***************************************************************-->
  <!-- Properties relating to the publish destination -->
  <!--***************************************************************-->
  <PropertyGroup>
    <DeployIisAppPath Condition="'$(DeployIisAppPath)'==''"></DeployIisAppPath>
    <RemoteSitePhysicalPath Condition="'$(RemoteSitePhysicalPath)'==''"></RemoteSitePhysicalPath>
    <DefaultDeployIisAppPhysicalPath Condition="'$(DefaultDeployIisAppPhysicalPath)'==''">$(WebPublishPipelineProjectDirectory)$(DefaultMsDeployAltSuffix)</DefaultDeployIisAppPhysicalPath>
      <DeployIisAppPhysicalPath Condition="'$(DeployIisAppPhysicalPath)'==''"></DeployIisAppPhysicalPath>
    <_DestinationIisAppPhysicalPath>$(DeployIisAppPhysicalPath)</_DestinationIisAppPhysicalPath>
    <_DestinationIisAppPhysicalPath Condition="'$(_DestinationIisAppPhysicalPath)'==''">$(DefaultDeployIisAppPhysicalPath)</_DestinationIisAppPhysicalPath>
    <!-- If it is not set the default value will be taken from $(LocalIisVersion)-->
    <DestinationIisVersion Condition="'$(DestinationIisVersion)'==''"></DestinationIisVersion>
    <DestinationUseIis Condition="'$(DestinationUseIis)' ==''"></DestinationUseIis>
    <PublishEnableLinks Condition="'$(PublishEnableLinks)'==''">$(PackageEnableLinks)</PublishEnableLinks>
    <PublishDisableLinks Condition="'$(PublishDisableLinks)'==''">$(PackageDisableLinks)</PublishDisableLinks>
    <DeployEncryptKey Condition="'$(DeployEncryptKey)'==''"></DeployEncryptKey>
    <SkipExtraFilesOnServer Condition="'$(SkipExtraFilesOnServer)'==''">False</SkipExtraFilesOnServer>
    <MsDeployServiceUrl Condition="$(MsDeployServiceUrl)==''"></MsDeployServiceUrl>
  </PropertyGroup>

  <!-- Determines the order in which the related MSDeploy parametes will show up in the
       IIS manager UI when the package is imported. The lower the numberered items are shown first.
       Default is 0.Any integer number is allowed.
       These values are placed in the MSDeployParameterPriority item list -->
  <PropertyGroup>
    <VsIisAppParametersPriority Condition="'$(VsIisAppParametersPriority)'==''">-100</VsIisAppParametersPriority>
    <VsContentPathParametersPriority Condition="'$(VsContentPathParametersPriority)'==''">-80</VsContentPathParametersPriority>
    <VsDestinationVDirParametersPriority Condition="'$(VsDestinationVDirParametersPriority)'==''">-70</VsDestinationVDirParametersPriority>
    <VsSetAclPriority Condition="'$(VsSetAclPriority)'==''">-60</VsSetAclPriority>
    <UserProfileParametersFileParametersPriority Condition="'$(UserProfileParametersFileParametersPriority)' == ''" >-55</UserProfileParametersFileParametersPriority>
    <UserParametersFileParametersPriority Condition="'$(UserParametersFileParametersPriority)'==''">-50</UserParametersFileParametersPriority>
    <UserWebConfigParametersPriority Condition="'$(UserWebConfigParametersPriority)'==''">-40</UserWebConfigParametersPriority>
    <VsSQLDatabaseScriptParametersPriority Condition="'$(VsSQLDatabaseScriptParametersPriority)'==''">60</VsSQLDatabaseScriptParametersPriority>
    <VsWebConfigAutoCsParametersPriority Condition="'$(VsWebConfigAutoCsParametersPriority)'==''">100</VsWebConfigAutoCsParametersPriority>
  </PropertyGroup>

  <!--***************************************************************-->
  <!-- Properties relating to where the source manifest and related files will be created. -->
  <!--***************************************************************-->
  <ItemGroup>
    <_MSDeployPackageFile Include="$(PackageFileName)" />
    <_MSDeployArchiveDir Include="$(PackageArchiveRootDir)\Archive" />
    <_MSDeployPackageLocation Condition="$(PackageAsSingleFile)" Include="$(PackageFileName)" />
    <_MSDeployPackageLocation Condition="!$(PackageAsSingleFile)" Include="@(_MSDeployArchiveDir)" />
  </ItemGroup>

  <PropertyGroup>
    <GenerateSampleDeployScriptLocation Condition="$(GenerateSampleDeployScript) And '$(GenerateSampleDeployScriptLocation)'==''">@(_MSDeployPackageLocation->'%(RootDir)%(Directory)%(FileName).deploy.cmd')</GenerateSampleDeployScriptLocation>

    <PackageSourceManifest Condition="'$(PackageSourceManifest)'==''">@(_MSDeployPackageLocation->'%(RootDir)%(Directory)%(FileName).SourceManifest.xml')</PackageSourceManifest>
    <UseDeclareParametersXMLInMsDeploy Condition="'$(UseDeclareParametersXMLInMsDeploy)'==''">$(UseMsdeployExe)</UseDeclareParametersXMLInMsDeploy>
    <PackageParametersFile Condition="'$(PackageParametersFile)'==''">@(_MSDeployPackageLocation->'%(RootDir)%(Directory)%(FileName).Package.Parameters.xml')</PackageParametersFile>
    <PublishParametersFile Condition="'$(PublishParametersFile)'==''">@(_MSDeployPackageLocation->'%(RootDir)%(Directory)%(FileName).Publish.Parameters.xml')</PublishParametersFile>
    <GenerateSampleDeployScriptLocation Condition="$(GenerateSampleDeployScript) And '$(GenerateSampleDeployScriptLocation)'==''">@(_MSDeployPackageLocation->'%(RootDir)%(Directory)%(FileName).deploy.cmd')</GenerateSampleDeployScriptLocation>
    <GenerateSampleDeployScriptReadMeFileName Condition="$(GenerateSampleDeployScript) And '$(GenerateSampleDeployScriptReadMeLocation)'==''">@(_MSDeployPackageLocation->'%(FileName).deploy-readme.txt')</GenerateSampleDeployScriptReadMeFileName>
    <GenerateSampleDeployScriptReadMeLocation Condition="$(GenerateSampleDeployScript) And '$(GenerateSampleDeployScriptReadMeLocation)'==''">@(_MSDeployPackageLocation->'%(RootDir)%(Directory)%(FileName).deploy-readme.txt')</GenerateSampleDeployScriptReadMeLocation>
    <GenerateSampleParametersValueLocationDefault Condition="'$(GenerateSampleParametersValueLocationDefault)'==''">@(_MSDeployPackageLocation->'%(RootDir)%(Directory)%(FileName).SetParameters.xml')</GenerateSampleParametersValueLocationDefault>
    <GenerateSampleParametersValueLocation Condition="$(GenerateSampleDeployScript) And '$(GenerateSampleParametersValueLocation)'==''">$(GenerateSampleParametersValueLocationDefault)</GenerateSampleParametersValueLocation>
    <GenerateSampleParametersValueFileName Condition="$(GenerateSampleDeployScript)">@(_MSDeployPackageLocation->'%(FileName).SetParameters.xml')</GenerateSampleParametersValueFileName>
  </PropertyGroup>
  
  <!-- This will ensure that all values in the ExcludeFromPackageFiles have the
       FromTarget metadata.
       
       To exclude files from publish in the .pubxml you can include something like the following
       <ExcludeFromPackageFiles Include="Default.aspx">
         <FromTarget>Project</FromTarget>
       </ExcludeFromPackageFiles>
      
       You can exclude a file in the following way.
       <ExcludeFromPackageFolders Include="SampleExclude">
         <FromTarget>Project</FromTarget>
       </ExcludeFromPackageFolders>
  -->
  <ItemDefinitionGroup>
    <ExcludeFromPackageFiles>
      <FromTarget>Unknown</FromTarget>
    </ExcludeFromPackageFiles>
    <ExcludeFromPackageFolders>
      <FromTarget>Unknown</FromTarget>
    </ExcludeFromPackageFolders>
  </ItemDefinitionGroup>

  <!--********************************************************************-->
  <!-- This will ensure that all values have the required metadata -->
  <!--********************************************************************-->
  <ItemDefinitionGroup>
    <FilesForPackagingFromProject>
      <DestinationRelativePath></DestinationRelativePath>
      <Exclude>False</Exclude>
      <FromTarget>Unknown</FromTarget>
      <Category>Run</Category>
      <ProjectFileType>Default</ProjectFileType>
    </FilesForPackagingFromProject>
  </ItemDefinitionGroup>


  <ItemDefinitionGroup>
    <FilesForFileCopy>
      <DestinationRelativePath></DestinationRelativePath>
      <Exclude>False</Exclude>
      <FromTarget>Unknown</FromTarget>
      <Category>Run</Category>
      <ProjectFileType>Default</ProjectFileType>
    </FilesForFileCopy>
  </ItemDefinitionGroup>

  <!-- ReplaceRule(string RuleName, 
  string objectName, 
  string scopeAttributeName, 
  string scopeAttributeValue, 
  string targetAttributeName, 
  string match, 
  string replace);-->
  <ItemDefinitionGroup>
    <MsDeployReplaceRules>
      <ObjectName></ObjectName>
      <ScopeAttributeName></ScopeAttributeName>
      <ScopeAttributeValue></ScopeAttributeValue>
      <TargetAttributeName></TargetAttributeName>
      <Match></Match>
      <Replace></Replace>
    </MsDeployReplaceRules>
  </ItemDefinitionGroup>

  <!--
  DeploymentSkipRule(string skipAction, string objectName, string absolutePath, string XPath);-->
  <ItemDefinitionGroup>
    <MsDeploySkipRules>
      <SkipAction></SkipAction>
      <ObjectName></ObjectName>
      <AbsolutePath></AbsolutePath>
      <XPath></XPath>
      <KeyAttribute></KeyAttribute>
      <!--Source, Destination, Both(the default)-->
      <Apply></Apply>
    </MsDeploySkipRules>
  </ItemDefinitionGroup>

  <!--
  DeploymentParameter(string name, string type, string scope, string match, string description, string defaultValue); -->
  <ItemDefinitionGroup>
    <MsDeployDeclareParameters>
      <Kind></Kind>
      <Scope></Scope>
      <Match></Match>
      <Description></Description>
      <DefaultValue></DefaultValue>
      <Tags></Tags>
      <ExcludeFromSetParameter></ExcludeFromSetParameter>
    </MsDeployDeclareParameters>
  </ItemDefinitionGroup>

  <!--
  DeploymentParameter(string name, string value);  -->
  <ItemDefinitionGroup>
    <MsDeploySimpleSetParameters>
      <Value></Value>
    </MsDeploySimpleSetParameters>
  </ItemDefinitionGroup>

  <!--
  DeploymentParameter(string type, string scope, string match, string value);  -->
  <ItemDefinitionGroup>
    <MsDeploySetParameters>
      <Kind></Kind>
      <Scope></Scope>
      <Match></Match>
      <Value></Value>
      <Description></Description>
      <DefaultValue></DefaultValue>
      <Tags></Tags>
    </MsDeploySetParameters>
  </ItemDefinitionGroup>
  
  <!-- 
  DeploymentParameter(string type, string scope, string match, string value);  -->
  <ItemDefinitionGroup>
    <MsDeploySourceProviderSetting>
      <Path></Path>
      <ComputerName></ComputerName>
      <!--<Wmsvc></Wmsvc>  Not supported yet-->
      <UserName></UserName>
      <Password></Password>
      <EncryptPassword></EncryptPassword>
      <IncludeAcls></IncludeAcls>
      <authType></authType>
      <prefetchPayload></prefetchPayload>
    </MsDeploySourceProviderSetting>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup>
    <MsDeployDestinationProviderSetting>
      <Path></Path>
      <ComputerName></ComputerName>
      <!--<Wmsvc></Wmsvc>  Not supported yet-->
      <UserName></UserName>
      <Password></Password>
      <EncryptPassword></EncryptPassword>
      <IncludeAcls></IncludeAcls>
      <authType></authType>
      <prefetchPayload></prefetchPayload>
    </MsDeployDestinationProviderSetting>
  </ItemDefinitionGroup>

  <!-- AdditionalProviderSettings denote the additionProviderSetting need to be set in the manifest files-->
  <ItemDefinitionGroup>
    <MsDeploySourceManifest>
      <Path>Unknown</Path>
      <AdditionalProviderSettings></AdditionalProviderSettings>
    </MsDeploySourceManifest>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup>
    <SqlCommandVariableMetaData>
      <Value/>
      <IsDeclared/>
      <SourcePath/>
      <SourcePath_RegExExcaped/>
      <DestinationGroup/>
    </SqlCommandVariableMetaData>
  </ItemDefinitionGroup>

  <!-- Import the .targets file which defines how files are collected. -->
  <!--
  In $(MSBuildExtensionsPath)\Microsoft\VisualStudio\v11.0\Web\CollectFiles
  Microsoft.Web.Publishing.AllFilesInProjectFolder.targets
  Microsoft.Web.Publishing.AllFilesInTheProject.targets
  Microsoft.Web.Publishing.OnlyFilesToRunTheApp.targets
  MyCustomized.targets
  -->
  <!-- This property determines what .targets file will be imported for file collection -->
  <PropertyGroup Condition="'$(FilesToIncludeTargetFile)'==''">
    <FilesToIncludeTargetFile Condition="Exists('$(MSBuildThisFileDirectory)\CollectFiles\$(FilesToIncludeForPublish).targets')">$(MSBuildThisFileDirectory)\CollectFiles\$(FilesToIncludeForPublish).targets</FilesToIncludeTargetFile>
    <FilesToIncludeTargetFile Condition="'$(FilesToIncludeTargetFile)'==''">$(MSBuildThisFileDirectory)\CollectFiles\Microsoft.Web.Publishing.$(FilesToIncludeForPublish).targets</FilesToIncludeTargetFile>
  </PropertyGroup>
  <Import Condition="'$(FilesToIncludeTargetFile)' != '' And Exists($(FilesToIncludeTargetFile))"
    Project="$(FilesToIncludeTargetFile)" />

  <!-- Import the .targets file which defines the publish steps. This is based off $(WebPublishMethod) -->
  <!--
  In $(MSBuildExtensionsPath)\Microsoft\VisualStudio\v11.0\Web\Deploy
  Microsoft.Web.Publishing.FTP.targets
  Microsoft.Web.Publishing.FPSE.targets
  MyCustomized.targets
  -->
  <!-- This is the target file for the way to customize for collection files -->
  <PropertyGroup Condition="'$(DeployTargetFile)'=='' and '$(WebPublishMethod)' != ''">
    <DeployTargetFile Condition="Exists('$(MSBuildThisFileDirectory)\Deploy\$(WebPublishMethod).targets')">$(MSBuildThisFileDirectory)\Deploy\$(WebPublishMethod).targets</DeployTargetFile>
    <DeployTargetFile Condition="'$(DeployTargetFile)'==''">$(MSBuildThisFileDirectory)\Deploy\Microsoft.Web.Publishing.Deploy.$(WebPublishMethod).targets</DeployTargetFile>
  </PropertyGroup>
  <Import Condition="'$(DeployTargetFile)'!='' And Exists($(DeployTargetFile))"
           Project="$(DeployTargetFile)"/>

  <!-- Import AspNetCompile and Merge into the target -->
  <!-- Loop in AspnetCompilerMergeTarget if need to-->
  <PropertyGroup Condition="'$(AspNetCompileMerge)' == ''">
    <AspNetCompileMerge>False</AspNetCompileMerge>
    <AspnetCompileMerge Condition="'$(PrecompileBeforePublish)' == 'true'">True</AspnetCompileMerge>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Web_Publishing_AspNetCompileMerge_targets)' == ''">
    <Web_Publishing_AspNetCompileMerge_targets>Transform\Microsoft.Web.Publishing.AspNetCompileMerge.targets</Web_Publishing_AspNetCompileMerge_targets>
  </PropertyGroup>

  <Import Condition="'$(Microsoft_Web_Publishing_AspNetCompileMerge_targets_Imported)' != 'True' And '$(AspNetCompileMerge)' == 'true'"
          Project="$(Web_Publishing_AspNetCompileMerge_targets)"/>



  <!--***************************************************************-->
  <!-- Import Common MSDeploy target/tasks to support /t:Package -->
  <!--***************************************************************-->
  <PropertyGroup>
    <Microsoft_Web_Publishing_MSDeploy_Common_targets Condition="'$(Microsoft_Web_Publishing_MSDeploy_Common_targets)' == ''">Deploy\Microsoft.Web.Publishing.MSDeploy.Common.targets</Microsoft_Web_Publishing_MSDeploy_Common_targets>
  </PropertyGroup>
  <Import Project="$(Microsoft_Web_Publishing_MSDeploy_Common_targets)" Condition="'$(Microsoft_Web_Publishing_MSDeploy_Common_targets_Imported)' != 'true' And Exists($(Microsoft_Web_Publishing_MSDeploy_Common_targets))"/>


  <!--********************************************************************-->
  <!-- ExcludeApp_Data -->
  <!--********************************************************************-->
  <PropertyGroup>
    <ExcludeApp_DataDependsOn Condition="'$(ExcludeApp_DataDependsOn)'==''">
    </ExcludeApp_DataDependsOn>
  </PropertyGroup>

  <Target Name="ExcludeApp_Data"
          DependsOnTargets="$(ExcludeApp_DataDependsOn)"
          Condition="$(ExcludeApp_Data)">

    <GetPublishingLocalizedString
      ID="PublishLocalizedString_ExcludeAllFilesUnderFolder"
      ArgumentCount="1"
      Arguments="App_Data"
      LogType="Message" />

    <!-- This excludes the files in the App_Data folder -->
    <ItemGroup>
      <ExcludeFromPackageFolders Include="App_Data">
        <FromTarget>ExcludeApp_Data</FromTarget>
      </ExcludeFromPackageFolders>
    </ItemGroup>
  </Target>

  <!--********************************************************************-->
  <!-- ExcludeGeneratedDebugSymbol -->
  <!--********************************************************************-->
  <PropertyGroup>
    <ExcludeGeneratedDebugSymbolDependsOn Condition="'$(ExcludeGeneratedDebugSymbolDependsOn)'==''">
    </ExcludeGeneratedDebugSymbolDependsOn>
  </PropertyGroup>
  <Target Name="ExcludeGeneratedDebugSymbol"
          DependsOnTargets="$(ExcludeGeneratedDebugSymbolDependsOn)"
          Condition="$(ExcludeGeneratedDebugSymbol)">

    <GetPublishingLocalizedString
      ID="PublishLocalizedString_ExcludeAllDebugSymbols"
      LogType="Message" />

    <ItemGroup>
      <ExcludeFromPackageFiles Include="@(FilesForPackagingFromProject)" Condition="'%(FilesForPackagingFromProject.Extension)'=='.pdb'">
        <FromTarget>ExcludeGeneratedDebugSymbol</FromTarget>
      </ExcludeFromPackageFiles>
    </ItemGroup>
  </Target>

  <PropertyGroup>
    <ExcludeXmlAssemblyFilesDependsOn>
      @(ExcludeXmlAssemblyFilesDependsOn);
    </ExcludeXmlAssemblyFilesDependsOn>
  </PropertyGroup>
  <Target Name="ExcludeXmlAssemblyFiles" Condition=" '$(ExcludeXmlAssemblyFiles)'=='true' " DependsOnTargets="@(ExcludeXmlAssemblyFilesDependsOn)">    
    <ItemGroup>
      <ExcludeFromPackageFiles Include="%(_ReferenceRelatedPaths.FullPath)" Condition="'%(_ReferenceRelatedPaths.Extension)'=='.xml'">
        <FromTarget>ExcludeXmlAssemblyFiles</FromTarget>
      </ExcludeFromPackageFiles>
    </ItemGroup>
  </Target>

  <!--Deal with http://connect.microsoft.com/VisualStudio/feedback/details/779737/error-allowdefinition-machinetoapplication-beyond-application-level, 
  we will need to clean up our temp folder before MVC project starts the pre-compile-->
  <PropertyGroup>
    <_EnableCleanOnBuildForMvcViews Condition=" '$(_EnableCleanOnBuildForMvcViews)'=='' ">true</_EnableCleanOnBuildForMvcViews>
  </PropertyGroup>
  <Target Name="CleanupForBuildMvcViews" Condition=" '$(_EnableCleanOnBuildForMvcViews)'=='true' and '$(MVCBuildViews)'=='true' " BeforeTargets="MvcBuildViews">
    <ItemGroup>
      <_PublishTempFolderNamesToCleanup Include="Database;TransformWebConfig;CSAutoParameterize;InsertAdditionalCS;ProfileTransformWebConfig;Package" />
    </ItemGroup>
    <!--Force msbuild to expand all the wildcard characters so to get real file paths-->
    <CreateItem Include="@(_PublishTempFolderNamesToCleanup->'$(BaseIntermediateOutputPath)**\%(identity)\**\*')">
      <Output TaskParameter="Include" ItemName="_EvaluatedPublishTempFolderNamesToCleanup" />
    </CreateItem>
    <Delete Files="@(_EvaluatedPublishTempFolderNamesToCleanup)" />
  </Target>
  
  <!--********************************************************************
  ExcludeFilesByExtension
    If $(FilesToIncludeForPublish) is set to OnlyFilesToRunTheApp then you can exclude files by their extension.
    
    You can exlude all files with a given extension, by adding that extension to
    the WebPublishExtnsionsToExcludeItem ItemGroup before this target executes.
  
    You can disable this functionality by setting $(EnableExcludeFilesByExtension) to false.
  
    By default the extensions listed in $(WebPublishExtensionsToExclude) will be excluded. 
    You can override this property if you need to publish files with one of those extensions.
  ********************************************************************-->
  <PropertyGroup>
    <EnableExcludeFilesByExtension Condition=" '$(EnableExcludeFilesByExtension)'=='' and ( '$(_WebProjectType)'=='WebSite' or '$(FilesToIncludeForPublish)'=='OnlyFilesToRunTheApp' ) ">true</EnableExcludeFilesByExtension>
    <ExcludeFilesByExtensionDependsOn>$(ExcludeFilesByExtensionDependsOn);</ExcludeFilesByExtensionDependsOn>
    <WebPublishExtensionsToExclude Condition=" '$(WebPublishExtensionsToExclude)'=='' ">.tt;.settings</WebPublishExtensionsToExclude>
  </PropertyGroup>
  <ItemGroup>
    <WebPublishExtnsionsToExcludeItem Include="$(WebPublishExtensionsToExclude)"/>
  </ItemGroup>
  <Target Name="ExcludeFilesByExtension" DependsOnTargets="$(ExcludeFilesByExtensionDependsOn)"
          Outputs="%(WebPublishExtnsionsToExcludeItem.Identity)" Condition=" '$(EnableExcludeFilesByExtension)'=='true' ">
    <!-- this target will execute once per value in @(WebPublishExtnsionsToExcludeItem) -->
    <PropertyGroup>
      <_ExtToExclude>%(WebPublishExtnsionsToExcludeItem.Identity)</_ExtToExclude>
    </PropertyGroup>

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                      Encoding="utf-8"
                      File="$(PackageLogDir)\ExcludeFilesByExtension.txt"
                      Lines="Excluding files with extension '$(_ExtToExclude)' from the publish/package operation"
                      Overwrite="True" />
    
    <ItemGroup>
      <ExcludeFromPackageFiles Include="@(FilesForPackagingFromProject)"
                               Condition="'%(FilesForPackagingFromProject.Extension)'=='$(_ExtToExclude)'">
        <FromTarget>ExcludeFilesByExtension</FromTarget>
      </ExcludeFromPackageFiles>
    </ItemGroup>
  </Target>
  
  <!--**********************************************************************-->
  <!-- ExcludeFilesFromPackage-->
  <!--**********************************************************************-->
  <PropertyGroup>
    <ExcludeFilesFromPackageDependsOn>
      $(ExcludeFilesFromPackageDependsOn);
      ExcludeApp_Data;
      ExcludeGeneratedDebugSymbol;
      ExcludeXmlAssemblyFiles;
      ExcludeFilesByExtension;
    </ExcludeFilesFromPackageDependsOn>
  </PropertyGroup>
  <Target Name="ExcludeFilesFromPackage" DependsOnTargets="$(ExcludeFilesFromPackageDependsOn)"
          Condition="$(ExcludeFilesFromPackage)">

    <!--Add the exclude Folder into the list (Note that ItemGroup doesn't work in this scenario.
        Note that this is specify in the DestinationRelativePath in the item.Spec-->
    <CreateItem Include="@(ExcludeFromPackageFolders->'$(AlternativeProjectDirectory)%(Identity)\**')"
                AdditionalMetadata="FromTarget=%(FromTarget)">
      <Output TaskParameter="Include" ItemName="ExcludeFromPackageFiles"/>
    </CreateItem>

    <MakeDir Condition="$(EnablePackageProcessLoggingAndAssert) And !Exists($(PackageLogDir))"
             Directories="$(PackageLogDir)" />
    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                      Encoding="utf-8"
                      File="$(PackageLogDir)\ExcludeFromPackageFiles.txt"
                      Lines="@(ExcludeFromPackageFiles->'
                      Files:%(Identity) 
                      FromTarget:%(FromTarget)
                      DestinationRelativePath:%(DestinationRelativePath)')"
                      Overwrite="True" />

    <!-- This will exclude files if the DestinationRelativePath is found in @(ExcludeFromPackageFiles) -->
    <FilterByItems PipelineItems="@(FilesForPackagingFromProject)"
                   SourceMetadataName="DestinationRelativePath"
                   FilterRootFolder="$(AlternativeProjectDirectory)"
                   FilterBaseOnRelativePath="True"
                   Filter="@(ExcludeFromPackageFiles)">
      <Output TaskParameter="InFilter" ItemName="_NeedtoMarkasExclude_FilesForPackagingFromProject"/>
    </FilterByItems>


    <!--This exclude if the filter item's DestinationRelativePath is specified.  
        This is useful for case like Reference Dll and pdb where the source is not under the current project.-->
    <FilterByItems PipelineItems="@(FilesForPackagingFromProject)"
                       SourceMetadataName="DestinationRelativePath"
                       FilterRootFolder="$(AlternativeProjectDirectory)"
                       FilterBaseOnRelativePath="True"
                       FilterMetadataName="DestinationRelativePath"
                       Filter="@(ExcludeFromPackageFiles)">
      <Output TaskParameter="InFilter" ItemName="_NeedtoMarkasExclude_FilesForPackagingFromProject"/>
    </FilterByItems>

    <FilterByItems PipelineItems="@(FilesForPackagingFromProject)"
                       SourceMetadataName="FullPath"
                       FilterBaseOnRelativePath="False"
                       FilterMetadataName="FullPath"
                       Filter="@(ExcludeFromPackageFiles)">
      <Output TaskParameter="InFilter" ItemName="_NeedtoMarkasExclude_FilesForPackagingFromProject"/>
    </FilterByItems>

    <ItemGroup>
      <FilesForPackagingFromProject Remove="@(_NeedtoMarkasExclude_FilesForPackagingFromProject)" />
      <FilesForPackagingFromProject Include="@(_NeedtoMarkasExclude_FilesForPackagingFromProject)">
        <Exclude>True</Exclude>
      </FilesForPackagingFromProject>
    </ItemGroup>

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                      Encoding="utf-8"
                      File="$(PackageLogDir)\AfterExcludeFilesFilesList.txt"
                      Lines="@(FilesForPackagingFromProject->'
                      From:%(Identity) 
                      DestinationRelativePath:%(DestinationRelativePath) 
                      Exclude:%(Exclude) 
                      FromTarget:%(FromTarget) 
                      Category:%(Category)
                      ProjectFileType:%(ProjectFileType)')" Overwrite="True" />

    <CallTarget Targets="$(OnAfterExcludeFilesFromPackage)" RunEachTargetSeparately="False" />
  </Target>

  <!--Target ImportParametersFiles-->
  <Target Name="ImportParametersFiles" DependsOnTargets="$(ImportParametersFilesDependsOn)"
          Condition="$(ImportParametersFiles)">

    <ExportParametersFile
      Condition=" $(EnablePackageProcessLoggingAndAssert)"
      Parameters="@(MsDeployDeclareParameters)"
      DeclareParameterFile="$(PackageLogDir)\BeforeImportParametersFiles.parameters.xml"
      GenerateFileEvenIfEmpty="True"
      />
    <ImportParametersFile  Files="@(ParametersXMLFiles)" 
                           DisableEscapeMSBuildVariable="$(ImportParametersFile_DisableEscapeMSBuildVariable)">
      <Output TaskParameter="Result" ItemName="_ImportedMSDeployDeclareParameters"/>
    </ImportParametersFile>

    <ItemGroup>
      <!-- Use %(Identity) to force msbuild not to batch based on %(DefaultValue) -->
      <MsDeployDeclareParameters Include="@(_ImportedMSDeployDeclareParameters)"
                                 Condition="'$(ImportProjectParametersFiles)' == 'true' And '%(_ImportedMSDeployDeclareParameters.Identity)' !=''">
        <Value>%(_ImportedMSDeployDeclareParameters.DefaultValue)</Value>
        <Priority>$(UserParametersFileParametersPriority)</Priority>
      </MsDeployDeclareParameters>
    </ItemGroup>

    <ImportParametersFile  Files="@(ProfileParametersXMLFiles)" DisableEscapeMSBuildVariable="$(ImportParametersFile_DisableEscapeMSBuildVariable)">
      <Output TaskParameter="Result" ItemName="_ImportedProfileMSDeployDeclareParameters"/>
    </ImportParametersFile>

    <ItemGroup>
      <!-- Use %(Identity) to force msbuild not to batch based on %(DefaultValue)-->
      <MsDeployDeclareParameters Include="@(_ImportedProfileMSDeployDeclareParameters)"
                                 Condition="'$(ImportProfileParametersFiles)' == 'true' And '%(_ImportedProfileMSDeployDeclareParameters.Identity)' !=''">
        <Value>%(_ImportedProfileMSDeployDeclareParameters.DefaultValue)</Value>
        <Priority>$(UserProfileParametersFileParametersPriority)</Priority>
      </MsDeployDeclareParameters>
    </ItemGroup>

    <ExportParametersFile
      Condition=" $(EnablePackageProcessLoggingAndAssert)"
      Parameters="@(MsDeployDeclareParameters)"
      DeclareParameterFile="$(PackageLogDir)\AfterImportParametersFiles.parameters.xml"
      GenerateFileEvenIfEmpty="True"
      />

  </Target>

  <!--********************************************************************-->
  <!-- Target ImportPublishingParameterValues-->
  <!--**********************************************************************-->
  <Target Name="ImportPublishingParameterValues" DependsOnTargets="$(ImportPublishingParameterValuesDependsOn)"
          Condition="$(ImportPublishingParameterValues)">

    <ItemGroup Condition="'$(AutoParameterizationWebConfigCSNoDefaultValue)' == 'false'">
      <MsDeployDeclareParameters Include="@(MSDeployParameterValue)"  Condition="'%(MSDeployParameterValue.Identity)' !='' And '%(MSDeployParameterValue.UpdateDestWebConfig)' !='False'">
        <Value>%(MSDeployParameterValue.ParameterValue)</Value>
        <DefaultValue>%(MSDeployParameterValue.ParameterValue)</DefaultValue>
        <Description>%(MSDeployParameterValue.Description)</Description>
        <Priority>%(MSDeployParameterValue.ParameterPriority)</Priority>
      </MsDeployDeclareParameters>
    </ItemGroup>
    <ItemGroup Condition="'$(AutoParameterizationWebConfigCSNoDefaultValue)' != 'false'">
      <MsDeployDeclareParameters Include="@(MSDeployParameterValue)"  Condition="'%(MSDeployParameterValue.Identity)' !='' And '%(MSDeployParameterValue.UpdateDestWebConfig)' !='False'">
        <Value>%(MSDeployParameterValue.ParameterValue)</Value>
        <Description>%(MSDeployParameterValue.Description)</Description>
        <Priority>%(MSDeployParameterValue.ParameterPriority)</Priority>
      </MsDeployDeclareParameters>
    </ItemGroup>
  </Target>
  
  <!--Target ValidateGlobalSettings -->
  <PropertyGroup>
    <ValidateGlobalSettingsDependsOn>
      $(ValidateGlobalSettingsDependsOn);
      GetProjectWebProperties;
    </ValidateGlobalSettingsDependsOn>
  </PropertyGroup>
  <Target Name="ValidateGlobalSettings"
          DependsOnTargets="$(ValidateGlobalSettingsDependsOn)" Condition="'$(ValidateGlobalSettings)' != 'False'">

    <GetPublishingLocalizedString
          ID="PublishLocalizedString_MsBuildPropertySettingValue"
          ArgumentCount="2"
          Arguments="WPPAllFilesInSingleFolder;$(WPPAllFilesInSingleFolder)"
          Condition="'$(WPPAllFilesInSingleFolder)' == ''  Or 
          $([System.String]::IsNullOrWhiteSpace($(WPPAllFilesInSingleFolder)))"
          LogType="Error" />

    <Error Text="Target ValidateGlobalSettings Failed"
       Condition="'$(WPPAllFilesInSingleFolder)' == ''  Or 
          $([System.String]::IsNullOrWhiteSpace($(WPPAllFilesInSingleFolder)))"
       />

  </Target>

  <!--********************************************************************-->
  <!--Target ValidateGlobalPackageSetting -->
  <!--********************************************************************-->
  <PropertyGroup>
    <ValidateGlobalPackageSettingDependsOn>
      $(ValidateGlobalPackageSettingDependsOn);
      GetProjectWebProperties;
      ValidateGlobalSettings;
    </ValidateGlobalPackageSettingDependsOn>
  </PropertyGroup>

  <Target Name="ValidateGlobalPackageSetting"
          DependsOnTargets="$(ValidateGlobalPackageSettingDependsOn)" Condition="$(_CreatePackage)">

    <GetPublishingLocalizedString
          ID="PublishLocalizedString_MsBuildPropertySettingValue"
          ArgumentCount="2"
          Arguments="PackageAsSingleFile;$(PackageAsSingleFile)"
          LogType="Message" />
    
    <GetPublishingLocalizedString
      Condition="$(PackageAsSingleFile)"
          ID="PublishLocalizedString_CheckingForValidMsBuildPropertyValue"
          ArgumentCount="2"
          Arguments="PackageFileName;$(PackageFileName)"
          LogType="Message" />
    <GetPublishingLocalizedString
          Condition="!$(PackageAsSingleFile)"
          ID="PublishLocalizedString_CheckingForValidMsBuildPropertyValue"
          ArgumentCount="2"
          Arguments="PackageArchiveRootDir;$(PackageArchiveRootDir)"
          LogType="Message" />
    
    <PropertyGroup>
      <_CheckPackageLocation Condition="$(PackageAsSingleFile)">$(PackageFileName)</_CheckPackageLocation>
      <_CheckPackageLocation Condition="!$(PackageAsSingleFile)">$(PackageArchiveRootDir)</_CheckPackageLocation>
      <_CheckPackageLocationArchiveDir Condition="!$(PackageAsSingleFile)">$(PackageArchiveRootDir)\Archive</_CheckPackageLocationArchiveDir>
    </PropertyGroup>

    <CheckPathAttributes Path="$(_CheckPackageLocation)" Condition="'$(_CheckPackageLocation)' != ''">
      <Output TaskParameter="IsExists" PropertyName="_CheckPackageLocationExists" />
      <Output TaskParameter="IsFolder" PropertyName="_CheckPackageLocationIsFolder" />
    </CheckPathAttributes>

    <CheckPathAttributes Condition="'$(_CheckPackageLocationArchiveDir)' != ''" Path="$(_CheckPackageLocationArchiveDir)">
      <Output TaskParameter="IsExists" PropertyName="_CheckPackageLocationArchiveDirExists" />
      <Output TaskParameter="IsFolder" PropertyName="_CheckPackageLocationArchiveDirIsFolder" />
    </CheckPathAttributes>

    <!-- Check on valid location-->
    <GetPublishingLocalizedString
       Condition="$(PackageAsSingleFile) And $(_CheckPackageLocationExists) And $(_CheckPackageLocationIsFolder)"
       ID="PublishLocalizedString_ErrorPackageAsFilePointToAnExistingFolder"
       ArgumentCount="1"
       Arguments="$(PackageFileName)"
       LogType="Error" />
    <!-- "You can't package as a single file to be the same name as an existing folder. Please delete the folder before packaging. Alternative,you can call msbuild with /t:CleanWebsitesPackage target to remove it." />-->

    <GetPublishingLocalizedString
       Condition="!$(PackageAsSingleFile) And $(_CheckPackageLocationExists) And !$(_CheckPackageLocationIsFolder)"
       ID="PublishLocalizedString_ErrorPackageAsFolderPointToAnExistingFile"
       ArgumentCount="1"
       Arguments="$(PackageArchiveRootDir)"
       LogType="Error" />
    <!-- "'$(PackageArchiveRootDir)' exist as a file. You can't package as an archive directory to be the same as an existing file. Please delete the file before packaging. Alternative,you can call msbuild with /t:CleanWebsitesPackage target to remove it." />-->
    
    <GetPublishingLocalizedString
       Condition="!$(PackageAsSingleFile) And $(_CheckPackageLocationArchiveDirExists) And !$(_CheckPackageLocationArchiveDirIsFolder)"
       ID="PublishLocalizedString_ErrorPackageAsFolderPointToAnExistingFile"
       ArgumentCount="1"
       Arguments="$(PackageArchiveRootDir)\Archive"
       LogType="Error" />
    <!-- "'$(PackageArchiveRootDir)\Archive' exist as a file. You can't package as an archive directory to be the same as an existing file. Please delete the file before packaging.   Alternative,you can call msbuild with /t:CleanWebsitesPackage target to clean it." />-->

    <!--Check on the IIS version compatibility-->
    <GetPublishingLocalizedString
       Condition="$(DestinationUseIis) And ($(DestinationIisVersion) &lt; '7') And $(_DeploymentUseIis) And ($(LocalIisVersion) &gt;= '7')"
       ID="PublishLocalizedString_ErrorCannotDeployFromIIS7AboveToLowerIIS"
       ArgumentCount="3"
       Arguments="$(IncludeIisSettings);$(DestinationIisVersion);$(LocalIisVersion)"
       LogType="Error" />
    <!-- "Deploy with IIS Setting from a IIS 7 or above to a lower verstion of IIS server is not supported. To fix the problem, please set the %24(IncludeIisSettings) to false. Your current setting are %24(IncludeIisSettings) is $(IncludeIisSettings), %24(DestinationIisVersion) is $(DestinationIisVersion) and %24(LocalIisVersion) is  $(LocalIisVersion)." />-->

    <Error Text="Target ValidateGlobalPackageSetting Failed"
       Condition="(!$(PackageAsSingleFile) And $(_CheckPackageLocationExists) And !$(_CheckPackageLocationIsFolder)) 
       Or ($(PackageAsSingleFile) And $(_CheckPackageLocationExists) And $(_CheckPackageLocationIsFolder))
       Or (!$(PackageAsSingleFile) And $(_CheckPackageLocationArchiveDirExists) And !$(_CheckPackageLocationArchiveDirIsFolder))
       Or ($(DestinationUseIis) And ($(DestinationIisVersion) &lt; '7') And $(_DeploymentUseIis) And ($(LocalIisVersion) &gt;= '7'))"
       />    
  </Target>

  <Target Name="ProcessItemToExcludeFromDeployment">
    <ItemGroup>
      <ExcludeFromPackageFiles Condition="'$(ExcludeFilesFromDeployment)'!=''" Include="$(ExcludeFilesFromDeployment)" />
      <ExcludeFromPackageFolders Condition="'$(ExcludeFoldersFromDeployment)'!=''" Include="$(ExcludeFoldersFromDeployment)" />
    </ItemGroup>
  </Target>

  <!--********************************************************************-->
  <!-- Target WPPCopyWebApplicaitonPipelineCircularDependencyError -->
  <!--********************************************************************-->
  <Target Name="WPPCopyWebApplicaitonPipelineCircularDependencyError" Condition="$(WPPCopyWebApplicaitonPipelineCircularDependencyError)">
    <GetPublishingLocalizedString
       Condition="$(WPPCopyWebApplicaitonPipelineCircularDependencyError)"
       ID="PublishLocalizedString_ErrorUseWPP_CopyWebApplicationAndPipelineDependsOnBuildBothTrue"
       LogType="Error" />

    <Error Text="Target WPPCopyWebApplicaitonPipelineCircularDependencyError failed"
           Condition ="$(WPPCopyWebApplicaitonPipelineCircularDependencyError)" />
    <!--
    "These two Properties are not compatable  %24(UseWPP_CopyWebApplication) and  %24(PipelineDependsOnBuild) both are True.  
            Please correct the problem by either set %24(Disable_CopyWebApplication) to true or set %24(PipelineDependsOnBuild) to false to break the circular build dependency.
            Detail: %24(UseWPP_CopyWebApplication) make the publsih pipeline (WPP) to be Part of the build and %24(PipelineDependsOnBuild) make the WPP depend on build thus cause the build circular build dependency.  " />
            -->
  </Target>

  <!--********************************************************************-->
  <!--Target _CleanWPPIfNeedTo -->
  <!--********************************************************************-->
  <PropertyGroup>
    <_CleanWPPIfNeedToDependsOn>
      $(_CleanWPPIfNeedToDependsOn);
      ValidateGlobalSettings;
    </_CleanWPPIfNeedToDependsOn>
  </PropertyGroup>
  <Target Name="_CleanWPPIfNeedTo" Condition="'$(_CleanWPPIfNeedTo)' != 'False'" DependsOnTargets="$(_CleanWPPIfNeedToDependsOn)">
    <PropertyGroup>
      <_IsSameWPPBuildInfoAsLastBuildInfo>True</_IsSameWPPBuildInfoAsLastBuildInfo>
      <_IsSameWPPBuildInfoAsLastBuildInfo Condition="!Exists($(_WPPLastBuildInfoLocation))">False</_IsSameWPPBuildInfoAsLastBuildInfo>
    </PropertyGroup>
   
    <!-- This is the tracking property -->
    <PropertyGroup>
      <_WPPCurrentBuildInfo>$(_WPPCurrentBuildInfo);PlatformName=$(PlatformName);Configuration=$(Configuration);WPPAllFilesInSingleFolder=$([System.IO.Path]::GetFullPath($(WPPAllFilesInSingleFolder)));WebPublishProfileFile=$(WebPublishProfileFile)</_WPPCurrentBuildInfo>
    </PropertyGroup>

    <ItemGroup>
      <_WPPCurrentBuildInfoItems Include="$(_WPPCurrentBuildInfo)" />
    </ItemGroup>

    <ReadLinesFromFile  Condition="$(_IsSameWPPBuildInfoAsLastBuildInfo)"
                        File="$(_WPPLastBuildInfoLocation)">
      <Output TaskParameter="Lines" ItemName="_WPPLastBuildInfo"/>
    </ReadLinesFromFile>

    <PropertyGroup Condition="$(_IsSameWPPBuildInfoAsLastBuildInfo)">
      <_IsSameWPPBuildInfoAsLastBuildInfo Condition="'@(_WPPLastBuildInfo)' != '@(_WPPCurrentBuildInfoItems)'" >False</_IsSameWPPBuildInfoAsLastBuildInfo>
    </PropertyGroup>

    <Message  Text="_WPPLastBuildInfoLocation is $(_WPPLastBuildInfoLocation)
              _WPPCurrentBuildInfo is @(_WPPCurrentBuildInfoItems)
              _WPPLastBuildInfo is @(_WPPLastBuildInfo)
              _IsSameWPPBuildInfoAsLastBuildInfo is $(_IsSameWPPBuildInfoAsLastBuildInfo)" 
              Importance="Low"/>

    <CallTarget Condition="!$(_IsSameWPPBuildInfoAsLastBuildInfo)"
              Targets="$(_WPPCleanTargets)"
              RunEachTargetSeparately="false" />
    <PropertyGroup>
      <_WPPLastBuildInfoLocationDirectory>$([System.IO.Path]::GetDirectoryName($(_WPPLastBuildInfoLocation)))</_WPPLastBuildInfoLocationDirectory>
    </PropertyGroup>

    <MakeDir Directories="$(_WPPLastBuildInfoLocationDirectory)" Condition="!Exists($(_WPPLastBuildInfoLocationDirectory))"/>

    <WriteLinesToFile  File="$(_WPPLastBuildInfoLocation)"
                        Lines="@(_WPPCurrentBuildInfoItems)"
                        Overwrite="true"
                        Encoding="Unicode"/>
    
  </Target>
  <!--********************************************************************-->
  <!-- Target PipelineCollectFilesPhase -->
  <!--********************************************************************-->
  <PropertyGroup>
    <OnBeforePipelineCollectFilesPhase>
      $(OnBeforePipelineCollectFilesPhase);
      _CleanWPPIfNeedTo;
    </OnBeforePipelineCollectFilesPhase>
    <OnAfterPipelineCollectFilesPhase>
      $(OnAfterPipelineCollectFilesPhase);
      ExcludeFilesFromPackage;
      ImportParametersFiles;
    </OnAfterPipelineCollectFilesPhase>
    <PipelineCollectFilesPhaseDependsOn>
      $(OnBeforePipelineCollectFilesPhase);
      $(PipelineCollectFilesPhaseDependsOn);
      WPPCopyWebApplicaitonPipelineCircularDependencyError;
      ProcessItemToExcludeFromDeployment;
      GetProjectWebProperties;
      ValidateGlobalSettings;
      ValidateGlobalPackageSetting;
      $(PipelineDependsOn);
      $(PublishPipelineCollectFilesCore);
    </PipelineCollectFilesPhaseDependsOn>
  </PropertyGroup>

  <Target Name="PipelineCollectFilesPhase"
          Outputs="@(FilesForPackagingFromProject)"
          DependsOnTargets="$(PipelineCollectFilesPhaseDependsOn)">

    <GetPublishingLocalizedString
      ID="PublishLocalizedString_WebPublishPipelineCollectFilesPhase"
      LogType="Message" />

    <MakeDir Condition="$(EnablePackageProcessLoggingAndAssert) And !Exists('$(PackageLogDir)')"
             Directories="$(PackageLogDir)" />

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                      Encoding="utf-8"
                      File="$(PackageLogDir)\PreExcludePipelineCollectFilesPhaseFileList.txt"
                      Lines="@(FilesForPackagingFromProject->'
                      From:%(Identity) 
                      DestinationRelativePath:%(DestinationRelativePath) 
                      Exclude:%(Exclude) 
                      FromTarget:%(FromTarget) 
                      Category:%(Category)
                      ProjectFileType:%(ProjectFileType)')" Overwrite="True" />

    <CheckItemsForDuplication
      Condition="$(EnablePackageProcessLoggingAndAssert)"
      Items="@(FilesForPackagingFromProject)"
      TraceLevel="Error"
      MetadataToVerify="DestinationRelativePath">
      <Output TaskParameter="HasDuplicate"  PropertyName="_FilesForPackagingDestinationRelativePathHasDuplicate"/>
    </CheckItemsForDuplication>

    <CheckItemsForDuplication
      Condition="$(EnablePackageProcessLoggingAndAssert)"
      Items="@(FilesForPackagingFromProject)"
      TraceLevel="Warning">
      <Output TaskParameter="HasDuplicate"  PropertyName="_FilesForPackagingIdentityHasDuplicate"/>
    </CheckItemsForDuplication>

    <CallTarget Targets="$(OnAfterPipelineCollectFilesPhase)" RunEachTargetSeparately="False" />
  </Target>


  <!--********************************************************************-->
  <!--Target TransformWebConfig -->
  <!--********************************************************************-->
  <PropertyGroup>
    <TransformWebConfigBeforeTargets>
      $(TransformWebConfigBeforeTargets);
      MarkWebConfigAssistFilesAsExclude;
    </TransformWebConfigBeforeTargets>
    <TransformWebConfigDependsOn>
      $(OnBeforeTransformWebConfig);
      $(TransformWebConfigDependsOn);
      PreTransformWebConfig;
      TransformWebConfigCore;
      ParameterizeTransformWebConfigCore;
      PostTransformWebConfig;
    </TransformWebConfigDependsOn>
  </PropertyGroup>

  <Target Name="TransformWebConfig"
          DependsOnTargets="$(TransformWebConfigDependsOn)"
          BeforeTargets="$(TransformWebConfigBeforeTargets)">


    <CallTarget Targets="$(OnAfterTransformWebConfig)" RunEachTargetSeparately="False" />
  </Target>

  <!--********************************************************************-->
  <!--Target CollectWebConfigsToTransform -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectWebConfigsToTransformDependsOn>
      $(OnBeforeCollectWebConfigsToTransform);
      $(CollectWebConfigsToTransformDependsOn);
      PipelineCollectFilesPhase;
    </CollectWebConfigsToTransformDependsOn>
  </PropertyGroup>

  <Target Name="CollectWebConfigsToTransform" DependsOnTargets="$(CollectWebConfigsToTransformDependsOn)" Condition="'$(CollectWebConfigsToTransform)' != 'false'">
    <!-- Gather Sources, Transforms, and Destinations for the TransformXml task -->
    <ItemGroup Condition="'$(ProjectConfigTransformFileName)'!=''">
      <WebConfigsToTransform Include="@(FilesForPackagingFromProject)"
                             Condition="'%(FilesForPackagingFromProject.Filename)%(FilesForPackagingFromProject.Extension)'=='$(ProjectConfigFileName)'">
        <TransformFile>$([System.String]::new($(WebPublishPipelineProjectDirectory)\$([System.IO.Path]::GetDirectoryName($([System.String]::new(%(DestinationRelativePath)))))).TrimEnd('\'))\$(ProjectConfigTransformFileName)</TransformFile>
        <TransformOriginalFolder>$(TransformWebConfigIntermediateLocation)\original</TransformOriginalFolder>
        <TransformFileFolder>$(TransformWebConfigIntermediateLocation)\assist</TransformFileFolder>
        <TransformOutputFile>$(TransformWebConfigIntermediateLocation)\transformed\%(DestinationRelativePath)</TransformOutputFile>
        <TransformScope>$([System.IO.Path]::GetFullPath($(WPPAllFilesInSingleFolder)\%(DestinationRelativePath)))</TransformScope>
      </WebConfigsToTransform>
      <_WebConfigsToTransformOuputs Include="@(WebConfigsToTransform->'%(TransformOutputFile)')" />
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectWebConfigsToTransform)" RunEachTargetSeparately="False" />
  </Target>


  <!--********************************************************************-->
  <!--Target PreTransformWebConfig -->
  <!--********************************************************************-->
  <PropertyGroup>
    <PreTransformWebConfigDependsOn>
      CollectWebConfigsToTransform;
    </PreTransformWebConfigDependsOn>
  </PropertyGroup>

  <Target Name="PreTransformWebConfig"
          DependsOnTargets="$(PreTransformWebConfigDependsOn)">

    <ItemGroup>
      <!-- Gather all the web.config files which do not have a transform file -->
      <_TempNoTransformWebConfigToTransform Include="@(WebConfigsToTransform)"
                                            Condition="!Exists(%(TransformFile))" />
      <!-- Remove all these files -->
      <WebConfigsToTransform Remove="@(_TempNoTransformWebConfigToTransform)"/>
    </ItemGroup>
    
    <PropertyGroup>
      <_WebConfigsToTransformOuputsDirectories>@(_WebConfigsToTransformOuputs->'%(RootDir)%(Directory)')</_WebConfigsToTransformOuputsDirectories>
    </PropertyGroup>

    <ItemGroup>
      <_WebConfigsToTransformOuputsDirectories Include="$(_WebConfigsToTransformOuputsDirectories)" />
    </ItemGroup>

    <PropertyGroup>
      <_MessageArguments>@(WebConfigsToTransform->'%(DestinationRelativePath)', ', ')</_MessageArguments>
    </PropertyGroup>
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_FoundApplicationConfigForTransformation"
      ArgumentCount="1"
      Arguments="$(_MessageArguments)"
      LogType="Message"
      Condition="'$(_MessageArguments)'!=''"/>

    <!-- Make sure required directories exist -->
    <MakeDir Directories="@(_WebConfigsToTransformOuputsDirectories)" Condition="!Exists(%(Identity))"/>

    <PropertyGroup>
      <_WebConfigTransformOutput>@(WebConfigsToTransform->'%(TransformOutputFile)')</_WebConfigTransformOutput>
      <_WebConfigTransformOutput Condition="$(UseParameterizeToTransformWebConfig)">$(_WebConfigTransformOutput);$(_WebConfigTransformOutputParametersFile)</_WebConfigTransformOutput>
    </PropertyGroup>

    <!-- Remove the output file if there is change on $(UseParameterizeToTransformWebConfig)-->
    <Delete Files="@(WebConfigsToTransform->'%(TransformOutputFile)');$(_WebConfigTransformOutputParametersFile)"
            Condition="(!$(UseParameterizeToTransformWebConfig) And Exists($(_WebConfigTransformOutputParametersFile))) 
                       Or ($(UseParameterizeToTransformWebConfig) And !Exists($(_WebConfigTransformOutputParametersFile)))"
            ContinueOnError="true"/>

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert) And ('@(WebConfigsToTransform)'!='') And !%(Exclude)"
                      Encoding="utf-8"
                      Overwrite="True"
                      File="$(PackageLogDir)\PreTransformWebConfig.Log"
                      Lines="@(WebConfigsToTransform->' 
           Transforming  input: %(Identity) 
                      with:  %(TransformFile)
                      output: %(TransformOutputFile)
                      ')" />

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                      Encoding="utf-8"
                      Overwrite="False"
                      File="$(PackageLogDir)\PreTransformWebConfig.Log"
                      Lines="_WebConfigTransformOutput: $(_WebConfigTransformOutput)" />

    <!-- Copy the original web.config -->
    <CopyPipelineFiles PipelineItems="@(WebConfigsToTransform)"
                           SourceDirectory="$(WebPublishPipelineProjectDirectory)"
                           TargetDirectory="%(TransformOriginalFolder)"
                           SkipMetadataExcludeTrueItems="True"
                           UpdateItemSpec="False"
                           DeleteItemsMarkAsExcludeTrue ="True"
                       Condition="'@(WebConfigsToTransform)' != ''">
      <Output TaskParameter="UpdatedPipelineItems" ItemName="_UpdatedWebConfigsToTransform"/>
    </CopyPipelineFiles>

    <!-- Delete the updated web.config files -->
    <Delete Files="@(_UpdatedWebConfigsToTransform->'%(TransformOutputFile)')" />

    <!--Copy the transform file-->
    <CopyPipelineFiles PipelineItems="@(WebConfigsToTransform->'%(TransformFile)')"
                           SourceDirectory="$(WebPublishPipelineProjectDirectory)"
                           TargetDirectory="%(TransformFileFolder)"
                           SkipMetadataExcludeTrueItems="True"
                           UpdateItemSpec="False"
                           DeleteItemsMarkAsExcludeTrue ="True"
                       Condition="'@(WebConfigsToTransform)' != ''">
      <Output TaskParameter="UpdatedPipelineItems" ItemName="_UpdatedWebConfigsTransformFile"/>
    </CopyPipelineFiles>

    <!-- Delete the updated web.config files -->
    <Delete Files="@(_UpdatedWebConfigsTransformFile->'%(TransformOutputFile)')" />
  </Target>

  <!--********************************************************************-->
  <!--Target TransformWebConfigCore -->
  <!--********************************************************************-->
  <PropertyGroup>
    <TransformWebConfigCoreDependsOn>
      PreTransformWebConfig;
    </TransformWebConfigCoreDependsOn>
  </PropertyGroup>

  <Target Name="TransformWebConfigCore"
          DependsOnTargets="$(TransformWebConfigCoreDependsOn)"
          Inputs="@(WebConfigsToTransform);%(TransformFile)"
          Outputs="$(_WebConfigTransformOutput)"
          Condition="@(WebConfigsToTransform)!='' And $(TransformWebConfigEnabled) And !$(UseParameterizeToTransformWebConfig)">

    <!-- Execute transformations -->
    <TransformXml Source="@(WebConfigsToTransform)"
                  Transform="%(TransformFile)"
                  Destination="%(TransformOutputFile)"
                  Condition="!%(Exclude)"
                  StackTrace="$(TransformWebConfigStackTraceEnabled)"
                  SourceRootPath="$(WebPublishPipelineSourceRootDirectory)"
                  TransformRootPath="$(WebPublishPipelineTransformRootDirectory)"/>
  </Target>


  <!--********************************************************************-->
  <!--Target ParameterizeTransformWebConfigCore -->
  <!--********************************************************************-->
  <PropertyGroup>
    <ParameterizeTransformWebConfigCoreDependsOn>
      PreTransformWebConfig;
    </ParameterizeTransformWebConfigCoreDependsOn>
  </PropertyGroup>
  <Target Name="ParameterizeTransformWebConfigCore"
          DependsOnTargets="$(TransformWebConfigCoreDependsOn)"
          Inputs="@(WebConfigsToTransform);%(TransformFile)"
          Outputs="%(TransformOutputFile)"
          Condition="$(TransformWebConfigEnabled) And $(UseParameterizeToTransformWebConfig)">

    <!-- Remove the output files -->
    <Delete Files="@(WebConfigsToTransform->'%(TransformOutputFile)');$(_WebConfigTransformOutputParametersFile)" />

    <!-- Execute transformations -->
    <ParameterizeTransformXml
      Source="@(WebConfigsToTransform)"
      IsSourceAFile="True"
      Transform="%(TransformFile)"
      IsTransformAFile="True"
      Destination="%(TransformOutputFile)"
      IsDestinationAFile="True"
      Scope="%(TransformScope)"
      EnableTokenizeParameters="True"
      UseXpathToFormParameter="$(ParameterizeTransformXmlUseXPath)"
      StackTrace="$(TransformWebConfigStackTraceEnabled)"
      SourceRootPath="$(WebPublishPipelineSourceRootDirectory)"
      TransformRootPath="$(WebPublishPipelineTransformRootDirectory)"
      Condition="!%(Exclude)">
      <!-- Created the DeclareParameters for later after DB import is included-->
      <Output TaskParameter="DeclareParameters" ItemName="_ParameterizeTransformWebConfig"/>
    </ParameterizeTransformXml>


    <!-- Set GenerateFileEvenIfEmpty to true to make sure incremental build works-->
    <ExportParametersFile
      Parameters="@(_ParameterizeTransformWebConfig)"
      DeclareParameterFile="$(_WebConfigTransformOutputParametersFile)"
      GenerateFileEvenIfEmpty="True"
      />
  </Target>


  <!--********************************************************************-->
  <!--Target PostTransformWebConfig -->
  <!--********************************************************************-->
  <PropertyGroup>
    <PostTransformWebConfigDependsOn>
      TransformWebConfigCore;
      ParameterizeTransformWebConfigCore;
    </PostTransformWebConfigDependsOn>
  </PropertyGroup>

  <Target Name="PostTransformWebConfig"
          DependsOnTargets="$(PostTransformWebConfigDependsOn)">

    <ItemGroup>
      <!-- Remove untransformed Web.configs from the pipeline -->
      <FilesForPackagingFromProject Remove="@(WebConfigsToTransform)" Condition="'@(WebConfigsToTransform)'!='' And !%(WebConfigsToTransform.Exclude) And Exists(%(WebConfigsToTransform.TransformOutputFile))"/>
      <!-- Add the transformed Web.configs at the new loction to the pipeline-->
      <FilesForPackagingFromProject Include="@(WebConfigsToTransform->'%(TransformOutputFile)')" Condition="'@(WebConfigsToTransform)'!='' And !%(WebConfigsToTransform.Exclude) And Exists(%(WebConfigsToTransform.TransformOutputFile))"/>
    </ItemGroup>

    <GetPublishingLocalizedString
       Importance="High"
       Condition="'@(WebConfigsToTransform)'!='' And !%(WebConfigsToTransform.Exclude) And Exists(%(WebConfigsToTransform.TransformOutputFile))"
       ID="PublishLocalizedString_TransformConfigUsingTransformFileToTransformOutputFile"
       ArgumentCount="3"
       Arguments="@(WebConfigsToTransform->'%(Identity)');%(TransformFile); %(TransformOutputFile)"
       LogType="Message" />
    <!-- "Transformed @(WebConfigsToTransform) using %(TransformFile) into %(TransformOutputFile)" />-->

    <ImportParametersFile  Files="$(_WebConfigTransformOutputParametersFile)"
                           DisableEscapeMSBuildVariable="$(ImportParametersFile_DisableEscapeMSBuildVariable)"
                           Condition="$(UseParameterizeToTransformWebConfig) and Exists($(_WebConfigTransformOutputParametersFile))">
      <Output TaskParameter="Result" ItemName="_ImportParameterizeTransformWebConfig"/>
    </ImportParametersFile>

    <ItemGroup>
      <MsDeployDeclareParameters Include="@(_ImportParameterizeTransformWebConfig)"
                                 Condition="!$(DisableAllVSGeneratedMSDeployParameter) And '%(_ImportParameterizeTransformWebConfig.Identity)' !=''">
        <Value>%(_ImportParameterizeTransformWebConfig.DefaultValue)</Value>
        <Priority>$(UserWebConfigParametersPriority)</Priority>
      </MsDeployDeclareParameters>
    </ItemGroup>

    <MakeDir Condition="$(EnablePackageProcessLoggingAndAssert) And !Exists('$(PackageLogDir)')"
             Directories="$(PackageLogDir)" />
    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                      Encoding="utf-8"
                      File="$(PackageLogDir)\AfterTransformWebConfig.txt"
                      Lines="@(FilesForPackagingFromProject->'
                      From:%(Identity) 
                      DestinationRelativePath:%(DestinationRelativePath) 
                      Exclude:%(Exclude) 
                      FromTarget:%(FromTarget) 
                      Category:%(Category)
                      ProjectFileType:%(ProjectFileType)')" Overwrite="True" />
  </Target>


  <!--********************************************************************-->
  <!--Target MarkWebConfigAssistFilesAsExclude -->
  <!--********************************************************************-->
  <PropertyGroup>
    <MarkWebConfigAssistFilesAsExcludeDependsOn>
      $(OnBeforeMarkWebConfigAssistFilesAsExclude);
      $(MarkWebConfigAssistFilesAsExcludeDependsOn);
    </MarkWebConfigAssistFilesAsExcludeDependsOn>
  </PropertyGroup>

  <Target Name="MarkWebConfigAssistFilesAsExclude"
          DependsOnTargets="$(MarkWebConfigAssistFilesAsExcludeDependsOn)"
          Condition="'$(MarkWebConfigAssistFilesAsExclude)' != 'false'">

    <ItemGroup Condition="'$(ProjectConfigFileName)' != '' ">
      <_AllProjectConfigsInFiles Include="@(FilesForPackagingFromProject)"
                             Condition="'%(FilesForPackagingFromProject.Filename)%(FilesForPackagingFromProject.Extension)'=='$(ProjectConfigFileName)'" />
    </ItemGroup>


      <!-- Mark all the assistent file whose DependsOn is web.config to Exclude -->
    <FilterByItems PipelineItems="@(FilesForPackagingFromProject)"
                   SourceMetadataName="DependentUpon"
                   FilterMetadataName="DestinationRelativePath"
                 Filter="@(_AllProjectConfigsInFiles)"
                 Condition="$(ExcludeTransformAssistFilesFromPublish)"
                   >
      <Output TaskParameter="InFilter" ItemName="_NeedtoMarkasExclude_ConfigAssistFiles"/>
    </FilterByItems>

    <ItemGroup>
      <!-- Mark all the assistent file whose DependsOn is web.config to Exclude -->
      <FilesForPackagingFromProject Remove="@(_NeedtoMarkasExclude_ConfigAssistFiles)" Condition="$(ExcludeTransformAssistFilesFromPublish)">
      </FilesForPackagingFromProject>
      <FilesForPackagingFromProject Include="@(_NeedtoMarkasExclude_ConfigAssistFiles)" Condition="$(ExcludeTransformAssistFilesFromPublish)">
        <Exclude>True</Exclude>
      </FilesForPackagingFromProject>
    </ItemGroup>
    
    <CallTarget Targets="$(OnAfterMarkWebConfigAssistFilesAsExclude)" RunEachTargetSeparately="False" />
  </Target>

  <!--********************************************************************-->
  <!--Target ProfileTransformWebConfig -->
  <!--********************************************************************-->
  <PropertyGroup>
    <ProfileTransformWebConfigBeforeTargets>
      $(ProfileTransformWebConfigBeforeTargets);
      MarkWebConfigAssistFilesAsExclude;
    </ProfileTransformWebConfigBeforeTargets>
    <ProfileTransformWebConfigAfterTargets>
      $(ProfileTransformWebConfigAfterTargets);
      TransformWebConfig;
    </ProfileTransformWebConfigAfterTargets>
    <ProfileTransformWebConfigDependsOn>
      $(OnBeforeProfileTransformWebConfig);
      $(ProfileTransformWebConfigDependsOn);
      PreProfileTransformWebConfig;
      ProfileTransformWebConfigCore;
      ParameterizeProfileTransformWebConfigCore;
      PostProfileTransformWebConfig;
    </ProfileTransformWebConfigDependsOn>
  </PropertyGroup>

  <Target Name="ProfileTransformWebConfig"
          DependsOnTargets="$(ProfileTransformWebConfigDependsOn)"
          Condition="'$(ProfileTransformWebConfig)' != 'false'  And '$(PublishProfileName)' != ''"
          BeforeTargets="$(ProfileTransformWebConfigBeforeTargets)"
          AfterTargets="$(ProfileTransformWebConfigAfterTargets)">

    <CallTarget Targets="$(OnAfterProfileTransformWebConfig)" RunEachTargetSeparately="False" />
  </Target>


  <!--********************************************************************-->
  <!--Target CollectFilesForProfileTransformWebConfigs -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesForProfileTransformWebConfigsDependsOn>
      $(OnBeforeCollectFilesForProfileTransformWebConfigs);
      $(CollectFilesForProfileTransformWebConfigsDependsOn);
      PipelineCollectFilesPhase;
    </CollectFilesForProfileTransformWebConfigsDependsOn>
  </PropertyGroup>

  <Target Name="CollectFilesForProfileTransformWebConfigs" DependsOnTargets="$(CollectFilesForProfileTransformWebConfigsDependsOn)" Condition="'$(CollectFilesForProfileTransformWebConfigs)' != 'false' And '$(PublishProfileName)' != ''">

    <!-- Gather Sources, Transforms, and Destinations for the TransformXml task -->
    <ItemGroup Condition="'$(ProjectProfileTransformFileName)'!=''">
      <ProfileWebConfigsToTransform Include="@(FilesForPackagingFromProject)"
                             Condition="'%(FilesForPackagingFromProject.Filename)%(FilesForPackagingFromProject.Extension)'=='$(ProjectConfigFileName)'">
        <TransformFile>$(WebPublishPipelineProjectDirectory)\$([System.IO.Path]::GetDirectoryName($([System.String]::new(%(DestinationRelativePath)))))\$(ProjectProfileTransformFileName)</TransformFile>
        <TransformOriginalFolder>$(ProfileTransformWebConfigIntermediateLocation)\original</TransformOriginalFolder>
        <TransformFileFolder>$(ProfileTransformWebConfigIntermediateLocation)\assist</TransformFileFolder>
        <TransformOutputFile>$(ProfileTransformWebConfigIntermediateLocation)\transformed\%(DestinationRelativePath)</TransformOutputFile>
        <TransformScope>$([System.IO.Path]::GetFullPath($(WPPAllFilesInSingleFolder)\%(DestinationRelativePath)))</TransformScope>
      </ProfileWebConfigsToTransform>
      <_ProfileWebConfigsToTransformOuputs Include="@(ProfileWebConfigsToTransform->'%(TransformOutputFile)')" />
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesForProfileTransformWebConfigs)" RunEachTargetSeparately="False" />
  </Target>

  <!--********************************************************************-->
  <!--Target PreProfileTransformWebConfig -->
  <!--********************************************************************-->
  <PropertyGroup>
    <PreProfileTransformWebConfigDependsOn>
      CollectFilesForProfileTransformWebConfigs;
    </PreProfileTransformWebConfigDependsOn>
  </PropertyGroup>

  <Target Name="PreProfileTransformWebConfig"
          DependsOnTargets="$(PreProfileTransformWebConfigDependsOn)">


    <ItemGroup>
      <!-- Gather all the web.config which doesn't have the transform file -->
      <_TempNoTrnasformFileForProfileWebConfigsToTransform Include="@(ProfileWebConfigsToTransform)"
                                            Condition="!Exists(%(TransformFile))" />

      <!-- Remove all the web.config doesn't have transform file -->
      <ProfileWebConfigsToTransform Remove="@(_TempNoTrnasformFileForProfileWebConfigsToTransform)"/>
    </ItemGroup>

    <PropertyGroup>
      <_ProfileWebConfigsToTransformOuputsDirectories>@(_ProfileWebConfigsToTransformOuputs->'%(RootDir)%(Directory)')</_ProfileWebConfigsToTransformOuputsDirectories>
    </PropertyGroup>

    <ItemGroup>
      <_ProfileWebConfigsToTransformOuputsDirectories Include="$(_ProfileWebConfigsToTransformOuputsDirectories)" />
    </ItemGroup>

    <!-- Debugging messages -->
    <PropertyGroup>
      <_MessageArguments>@(ProfileWebConfigsToTransform->'%(DestinationRelativePath)', ', ')</_MessageArguments>
    </PropertyGroup>
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_FoundApplicationConfigForTransformation"
      ArgumentCount="1"
      Arguments="$(_MessageArguments)"
      LogType="Message" 
      Condition="'$(_MessageArguments)'!=''"/>

    <!-- Make sure required directories exist -->
    <MakeDir Directories="@(_ProfileWebConfigsToTransformOuputsDirectories)" Condition="!Exists(%(Identity))"/>

    <PropertyGroup>
      <_ProfileWebConfigTransformOutput>@(ProfileWebConfigsToTransform->'%(TransformOutputFile)')</_ProfileWebConfigTransformOutput>
      <_ProfileWebConfigTransformOutput Condition="$(UseParameterizeToProfileTransformWebConfig)">$(_ProfileWebConfigTransformOutput);$(_ProfileWebConfigTransformOutputParametersFile)</_ProfileWebConfigTransformOutput>
    </PropertyGroup>

    <!-- Remove the output file if there is change on $(UseParameterizeToProfileTransformWebConfig)-->
    <Delete Files="@(ProfileWebConfigsToTransform->'%(TransformOutputFile)');$(_ProfileWebConfigTransformOutputParametersFile)"
            Condition="(!$(UseParameterizeToProfileTransformWebConfig) And Exists($(_ProfileWebConfigTransformOutputParametersFile))) 
                       Or ($(UseParameterizeToProfileTransformWebConfig) And !Exists($(_ProfileWebConfigTransformOutputParametersFile)))"
            ContinueOnError="true"/>

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert) And ('@(ProfileWebConfigsToTransform)'!='') And !%(Exclude)"
                      Encoding="utf-8"
                      Overwrite="True"
                      File="$(PackageLogDir)\PreProfileTransformWebConfig.Log"
                      Lines="@(ProfileWebConfigsToTransform->' 
           Transforming  input: %(Identity) 
                      with:  %(TransformFile)
                      output: %(TransformOutputFile)
                      ')" />

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                      Encoding="utf-8"
                      Overwrite="False"
                      File="$(PackageLogDir)\PreProfileTransformWebConfig.Log"
                      Lines="_ProfileWebConfigTransformOutput: $(_ProfileWebConfigTransformOutput)" />

    <!-- Copy the original web.config -->
    <CopyPipelineFiles PipelineItems="@(ProfileWebConfigsToTransform)"
                           SourceDirectory="$(WebPublishPipelineProjectDirectory)"
                           TargetDirectory="%(TransformOriginalFolder)"
                           SkipMetadataExcludeTrueItems="True"
                           UpdateItemSpec="False"
                           DeleteItemsMarkAsExcludeTrue ="True"
                           Condition="'@(ProfileWebConfigsToTransform)' != ''">
      <Output TaskParameter="UpdatedPipelineItems" ItemName="_UpdatedProfileWebConfigsToTransform"/>
    </CopyPipelineFiles>

    <!-- Delete those web.config have been updated if existed-->
    <Delete Files="@(_UpdatedProfileWebConfigsToTransform->'%(TransformOutputFile)')" />


    <!--Copy the TransformFile-->
    <CopyPipelineFiles PipelineItems="@(ProfileWebConfigsToTransform->'%(TransformFile)')"
                           SourceDirectory="$(WebPublishPipelineProjectDirectory)"
                           TargetDirectory="%(TransformFileFolder)"
                           SkipMetadataExcludeTrueItems="True"
                           UpdateItemSpec="False"
                           DeleteItemsMarkAsExcludeTrue ="True"
                           Condition="'@(ProfileWebConfigsToTransform)' != ''">
      <Output TaskParameter="UpdatedPipelineItems" ItemName="_UpdatedProfileWebConfigsTransformFile"/>
    </CopyPipelineFiles>

    <!-- Delete those web.config have been updated if existed-->
    <Delete Files="@(_UpdatedProfileWebConfigsTransformFile->'%(TransformOutputFile)')" />
  </Target>

  <!--Target ProfileTransformWebConfigCore -->
  <PropertyGroup>
    <ProfileTransformWebConfigCoreDependsOn>
      PreProfileTransformWebConfig;
    </ProfileTransformWebConfigCoreDependsOn>
  </PropertyGroup>

  <Target Name="ProfileTransformWebConfigCore"
          DependsOnTargets="$(ProfileTransformWebConfigCoreDependsOn)"
          Inputs="@(ProfileWebConfigsToTransform);%(TransformFile)"
          Outputs="$(_ProfileWebConfigTransformOutput)"
          Condition="'@(ProfileWebConfigsToTransform)' != '' And $(ProfileTransformWebConfigEnabled) And !$(UseParameterizeToProfileTransformWebConfig)">

    <!-- Execute transformations -->
    <TransformXml Source="@(ProfileWebConfigsToTransform)"
                  Transform="%(TransformFile)"
                  Destination="%(TransformOutputFile)"
                  Condition="!%(Exclude)"
                  StackTrace="$(ProfileTransformWebConfigStackTraceEnabled)"
                  SourceRootPath="$(WebPublishPipelineSourceRootDirectory)"
                  TransformRootPath="$(WebPublishPipelineTransformRootDirectory)"/>

  </Target>

  <!--Target ParameterizeProfileTransformWebConfigCore -->
  <PropertyGroup>
    <ParameterizeProfileTransformWebConfigCoreDependsOn>
      PreProfileTransformWebConfig;
    </ParameterizeProfileTransformWebConfigCoreDependsOn>
  </PropertyGroup>
  <Target Name="ParameterizeProfileTransformWebConfigCore"
          DependsOnTargets="$(ProfileTransformWebConfigCoreDependsOn)"
          Inputs="@(ProfileWebConfigsToTransform);%(TransformFile)"
          Outputs="%(TransformOutputFile)"
          Condition="$(ProfileTransformWebConfigEnabled) And $(UseParameterizeToProfileTransformWebConfig)">

    <!-- Remove all the output file -->
    <Delete Files="@(ProfileWebConfigsToTransform->'%(TransformOutputFile)');$(_ProfileWebConfigTransformOutputParametersFile)" />

    <!-- Execute transformations -->
    <ParameterizeTransformXml
      Source="@(ProfileWebConfigsToTransform)"
      IsSourceAFile="True"
      Transform="%(TransformFile)"
      IsTransformAFile="True"
      Destination="%(TransformOutputFile)"
      IsDestinationAFile="True"
      Scope="%(TransformScope)"
      EnableTokenizeParameters="True"
      UseXpathToFormParameter="$(ParameterizeTransformXmlUseXPath)"
      StackTrace="$(ProfileTransformWebConfigStackTraceEnabled)"
      SourceRootPath="$(WebPublishPipelineSourceRootDirectory)"
      TransformRootPath="$(WebPublishPipelineTransformRootDirectory)"
      Condition="!%(Exclude)">
      <!-- Created the DeclareParameters for later after DB import is included-->
      <Output TaskParameter="DeclareParameters" ItemName="_ParameterizeProfileTransformWebConfig"/>
    </ParameterizeTransformXml>


    <!-- Set GenerateFileEvenIfEmpty to true to make sure incremental build works-->
    <ExportParametersFile
      Parameters="@(_ParameterizeProfileTransformWebConfig)"
      DeclareParameterFile="$(_ProfileWebConfigTransformOutputParametersFile)"
      GenerateFileEvenIfEmpty="True"
      />
  </Target>

  <!--********************************************************************-->
  <!--Target PostProfileTransformWebConfig -->
  <!--********************************************************************-->
  <PropertyGroup>
    <PostProfileTransformWebConfigDependsOn>
      ProfileTransformWebConfigCore;
      ParameterizeProfileTransformWebConfigCore;
    </PostProfileTransformWebConfigDependsOn>
  </PropertyGroup>

  <Target Name="PostProfileTransformWebConfig"
          DependsOnTargets="$(PostProfileTransformWebConfigDependsOn)">

    <ItemGroup>
      <!--Remove untransformed Web.configs from the pipeline-->
      <FilesForPackagingFromProject Remove="@(ProfileWebConfigsToTransform)" Condition="'@(ProfileWebConfigsToTransform)'!='' And !%(ProfileWebConfigsToTransform.Exclude) And Exists(%(ProfileWebConfigsToTransform.TransformOutputFile))"/>
      <!--Add the transformed Web.configs at the new loction to the pipeline-->
      <FilesForPackagingFromProject Include="@(ProfileWebConfigsToTransform->'%(TransformOutputFile)')" Condition="'@(ProfileWebConfigsToTransform)'!='' And !%(ProfileWebConfigsToTransform.Exclude) And Exists(%(ProfileWebConfigsToTransform.TransformOutputFile))"/>
    </ItemGroup>


    <GetPublishingLocalizedString
       Importance="High"
       Condition="'@(ProfileWebConfigsToTransform)'!='' And !%(ProfileWebConfigsToTransform.Exclude) And Exists(%(ProfileWebConfigsToTransform.TransformOutputFile))"
       ID="PublishLocalizedString_TransformConfigUsingTransformFileToTransformOutputFile"
       ArgumentCount="3"
       Arguments="@(ProfileWebConfigsToTransform->'%(Identity)');%(TransformFile); %(TransformOutputFile)"
       LogType="Message" />
    <!-- "Transformed @(ProfileWebConfigsToTransform) using %(TransformFile) into %(TransformOutputFile)" />-->

    <ImportParametersFile  Files="$(_ProfileWebConfigTransformOutputParametersFile)" 
                           DisableEscapeMSBuildVariable="$(ImportParametersFile_DisableEscapeMSBuildVariable)"
                           Condition="$(UseParameterizeToProfileTransformWebConfig) and Exists($(_ProfileWebConfigTransformOutputParametersFile))">
      <Output TaskParameter="Result" ItemName="_ImportParameterizeProfileTransformWebConfig"/>
    </ImportParametersFile>

    <ItemGroup>
      <MsDeployDeclareParameters Include="@(_ImportParameterizeProfileTransformWebConfig)"
                                 Condition="!$(DisableAllVSGeneratedMSDeployParameter) And '%(_ImportParameterizeProfileTransformWebConfig.Identity)' !=''">
        <Value>%(_ImportParameterizeProfileTransformWebConfig.DefaultValue)</Value>
        <Priority>$(UserWebConfigParametersPriority)</Priority>
      </MsDeployDeclareParameters>
    </ItemGroup>

    <MakeDir Condition="$(EnablePackageProcessLoggingAndAssert) And !Exists('$(PackageLogDir)')"
             Directories="$(PackageLogDir)" />
    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                      Encoding="utf-8"
                      File="$(PackageLogDir)\AfterProfileTransformWebConfig.txt"
                      Lines="@(FilesForPackagingFromProject->'
                      From:%(Identity) 
                      DestinationRelativePath:%(DestinationRelativePath) 
                      Exclude:%(Exclude) 
                      FromTarget:%(FromTarget) 
                      Category:%(Category)
                      ProjectFileType:%(ProjectFileType)')" Overwrite="True" />
  </Target>

  <!--********************************************************************-->
  <!-- Target PreInsertAdditionalWebCofigConnectionStrings -->
  <!--********************************************************************-->
  <PropertyGroup>
    <PreInsertAdditionalWebCofigConnectionStringsDependsOn>
      ProfileTransformWebConfig;
    </PreInsertAdditionalWebCofigConnectionStringsDependsOn>
  </PropertyGroup>

  <Target Name="PreInsertAdditionalWebCofigConnectionStrings"
          DependsOnTargets="$(PreInsertAdditionalWebCofigConnectionStringsDependsOn)">
    <ItemGroup>
      <_WebConfigsToInsertAdditionalCS Include="@(FilesForPackagingFromProject)"
                             Condition="$(InsertAdditionalWebConfigConnectionStringOnlyInRoot) 
                                         And '%(FilesForPackagingFromProject.Filename)%(FilesForPackagingFromProject.Extension)'=='$(ProjectConfigFileName)' 
                                         And !%(FilesForPackagingFromProject.Exclude)
                                         And '%(DestinationRelativePath)' == '$(ProjectConfigFileName)'">
        <TransformOriginalFolder>$(InsertAdditionalWebCofigConnectionStringsLocation)\original</TransformOriginalFolder>
        <TransformFileFolder>$(InsertAdditionalWebCofigConnectionStringsLocation)\assist</TransformFileFolder>
        <TransformOutputFile>$(InsertAdditionalWebCofigConnectionStringsLocation)\transformed\%(DestinationRelativePath)</TransformOutputFile>
        <TransformScope>$([System.IO.Path]::GetFullPath($(WPPAllFilesInSingleFolder)\%(DestinationRelativePath)))</TransformScope>
      </_WebConfigsToInsertAdditionalCS>
      <_WebConfigsToInsertAdditionalCS Include="@(FilesForPackagingFromProject)"
                             Condition="!$(InsertAdditionalWebConfigConnectionStringOnlyInRoot) 
                                         And '%(FilesForPackagingFromProject.Filename)%(FilesForPackagingFromProject.Extension)'=='$(ProjectConfigFileName)' 
                                         And !%(FilesForPackagingFromProject.Exclude)">
        <TransformOriginalFolder>$(InsertAdditionalWebCofigConnectionStringsLocation)\original</TransformOriginalFolder>
        <TransformFileFolder>$(InsertAdditionalWebCofigConnectionStringsLocation)\assist</TransformFileFolder>
        <TransformOutputFile>$(InsertAdditionalWebCofigConnectionStringsLocation)\transformed\%(DestinationRelativePath)</TransformOutputFile>
        <TransformScope>$([System.IO.Path]::GetFullPath($(WPPAllFilesInSingleFolder)\%(DestinationRelativePath)))</TransformScope>
      </_WebConfigsToInsertAdditionalCS>
        <_WebConfigsToInsertAdditionalCSOuputFiles Include="@(_WebConfigsToInsertAdditionalCS->'%(TransformOutputFile)')">
      </_WebConfigsToInsertAdditionalCSOuputFiles>
    </ItemGroup>

    <PropertyGroup>
      <_WebConfigsToInsertAdditionalCSOuputDirectories>@(_WebConfigsToInsertAdditionalCSOuputFiles->'%(RootDir)%(Directory)')</_WebConfigsToInsertAdditionalCSOuputDirectories>
      <_WebConfigsToInsertAdditionalCSOuput>@(_WebConfigsToInsertAdditionalCS->'%(TransformOutputFile)');</_WebConfigsToInsertAdditionalCSOuput>
    </PropertyGroup>

    <ItemGroup>
      <_WebConfigsToInsertAdditionalCSOuputDirectories Include="$(_WebConfigsToInsertAdditionalCSOuputDirectories)" />
    </ItemGroup>

    <MakeDir Directories="@(_WebConfigsToInsertAdditionalCSOuputDirectories)" Condition="!Exists(%(Identity))"/>

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert) And ('@(_WebConfigsToInsertAdditionalCS)'!='') And !%(Exclude)"
                      Encoding="utf-8"
                      Overwrite="True"
                      File="$(PackageLogDir)\PreInsertAdditionalWebCofigConnectionStrings.Log"
                      Lines="@(_WebConfigsToInsertAdditionalCS->'
    InsertAdditionalCSTransform   input: %(Identity) 
                      output: %(TransformOutputFile)
                      From:%(Identity) 
                      DestinationRelativePath:%(DestinationRelativePath) 
                      Exclude:%(Exclude) 
                      FromTarget:%(FromTarget) 
                      Category:%(Category)
                      ProjectFileType:%(ProjectFileType)
                      ')" />
    
    <!--Copy the original web.config-->
    <CopyPipelineFiles PipelineItems="@(_WebConfigsToInsertAdditionalCS)"
                           SourceDirectory="$(WebPublishPipelineProjectDirectory)"
                           TargetDirectory="%(TransformOriginalFolder)"
                           SkipMetadataExcludeTrueItems="True"
                           UpdateItemSpec="False"
                           DeleteItemsMarkAsExcludeTrue ="True"
                       Condition="'@(_WebConfigsToInsertAdditionalCS)' != ''">
      <Output TaskParameter="UpdatedPipelineItems" ItemName="_UpdatedWebConfigsToInsertAdditionalCS"/>
    </CopyPipelineFiles>

    <!-- Delete those web.config have been updated if existed-->
    <Delete Files="@(_UpdatedWebConfigsToInsertAdditionalCS->'%(TransformOutputFile)')" />

    <ItemGroup>
      <_InsertAdditionalConnectionStringNamesPrepare Include="@(_ConnectionStringsToInsert)">
        <ConnectionStringName>%(_ConnectionStringsToInsert.Identity)</ConnectionStringName>
        <ConnectionString>%(_ConnectionStringsToInsert.Identity)_ConnectionString</ConnectionString>
      </_InsertAdditionalConnectionStringNamesPrepare>
      <_InsertAdditionalConnectionStringNames Include="@(_InsertAdditionalConnectionStringNamesPrepare)">
        <TransformXMLFragement>
          &lt;add
          name=&quot;%(_InsertAdditionalConnectionStringNamesPrepare.ConnectionStringName)&quot;&#13;&#10;
          connectionString=&quot;%(_InsertAdditionalConnectionStringNamesPrepare.ConnectionString)&quot;&#13;&#10;
          providerName=&quot;$(InsertAdditionalWebConfigConnectionStringProviderName)&quot;&#13;&#10;
          xdt:Transform=&quot;InsertIfMissing&quot; &#13;&#10;
          xdt:Locator=&quot;Match(name)&quot;&#13;&#10;
          xdt:SupressWarnings=&quot;True&quot;&#13;&#10;
          /&gt;
        </TransformXMLFragement>
      </_InsertAdditionalConnectionStringNames>
    </ItemGroup>

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert) And ('@(_InsertAdditionalConnectionStringNames)'!='') "
                  Encoding="utf-8"
                  Overwrite="True"
                  File="$(PackageLogDir)\InsertAdditionalConnectionStringName.Log"
                  Lines="@(_InsertAdditionalConnectionStringNames->'
    _InsertAdditionalConnectionStringNames   
                      TransformXML: %(TransformXML) 
                      ConnectionString: %(ConnectionString)
                      TransformXMLFragement: %(TransformXMLFragement)
                      ')" />

    <PropertyGroup>
      <_WebConfigsToInsertAdditionalCS_Transform>&lt;?xml version=&quot;1.0&quot;?&gt;
        &lt;configuration xmlns:xdt=&quot;http://schemas.microsoft.com/XML-Document-Transform&quot;&gt;
        &lt;connectionStrings xdt:Transform=&quot;InsertIfMissing&quot; xdt:SupressWarnings=&quot;True&quot;&gt;
        @(_InsertAdditionalConnectionStringNames->'%(TransformXMLFragement)', '')
        &lt;/connectionStrings&gt;
        &lt;/configuration&gt;
      </_WebConfigsToInsertAdditionalCS_Transform>
    </PropertyGroup>
    
  </Target>

  <!--********************************************************************-->
  <!--Target InsertAdditionalWebCofigConnectionStringsCore -->
  <!--********************************************************************-->
  <PropertyGroup>
    <InsertAdditionalWebCofigConnectionStringsCoreDependsOn>
    </InsertAdditionalWebCofigConnectionStringsCoreDependsOn>
  </PropertyGroup>

  <Target Name="InsertAdditionalWebCofigConnectionStringsCore"
          Inputs="@(_WebConfigsToInsertAdditionalCS)"
          Outputs="%(TransformOutputFile)"
          DependsOnTargets="$(InsertAdditionalWebCofigConnectionStringsCoreDependsOn)">

    <!-- First Delete the output parameter file-->
    <!-- Remove the output file if there is change on $(UseParameterizeToTransformWebConfig)-->
    <Delete Files="@(_WebConfigsToInsertAdditionalCS->'%(TransformOutputFile)')"/>

    
    <PropertyGroup>
      <_WebConfigToInsertAdditionalCS_Identity>%(_WebConfigsToInsertAdditionalCS.Identity)</_WebConfigToInsertAdditionalCS_Identity>
      <_WebConfigToInsertAdditionalCS_TransformOutputFile>%(_WebConfigsToInsertAdditionalCS.TransformOutputFile)</_WebConfigToInsertAdditionalCS_TransformOutputFile>
      <_WebConfigsToInsertAdditionalCS_TransformScope>%(_WebConfigsToInsertAdditionalCS.TransformScope)</_WebConfigsToInsertAdditionalCS_TransformScope>
    </PropertyGroup>

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                  Encoding="utf-8"
                  Overwrite="False"
                  File="$(PackageLogDir)\InsertAdditionalConnectionStringName.Log"
                  Lines="_WebConfigToInsertAdditionalCS_Identity: $(_WebConfigToInsertAdditionalCS_Identity) 
                      _WebConfigToInsertAdditionalCS_TransformOutputFile: $(_WebConfigToInsertAdditionalCS_TransformOutputFile)
                      _WebConfigsToInsertAdditionalCS_TransformScope: $(_WebConfigsToInsertAdditionalCS_TransformScope)
                      _WebConfigsToInsertAdditionalCS_Transform: $(_WebConfigsToInsertAdditionalCS_Transform)
                      " />
    
    <!-- Now we use the tokenize transform to auto parameterize the web.config-->
    <ParameterizeTransformXml
      Source="$(_WebConfigToInsertAdditionalCS_Identity)"
      IsSourceAFile="True"
      Transform="$(_WebConfigsToInsertAdditionalCS_Transform)"
      IsTransformAFile="False"
      Destination="$(_WebConfigToInsertAdditionalCS_TransformOutputFile)"
      IsDestinationAFile="True"
      Scope="$(_WebConfigsToInsertAdditionalCS_TransformScope)"
      StackTrace="$(TransformWebConfigStackTraceEnabled)"
      SourceRootPath="$(WebPublishPipelineSourceRootDirectory)">
    </ParameterizeTransformXml>
  </Target>

  <!--********************************************************************-->
  <!--Target PostInsertAdditionalWebCofigConnectionStrings -->
  <!--********************************************************************-->
  <PropertyGroup>
    <PostInsertAdditionalWebCofigConnectionStringsDependsOn>
    </PostInsertAdditionalWebCofigConnectionStringsDependsOn>
  </PropertyGroup>

  <Target Name="PostInsertAdditionalWebCofigConnectionStrings"
          DependsOnTargets="$(PostInsertAdditionalWebCofigConnectionStringsDependsOn)">

    <ItemGroup>
      <!-- Remove untransformed Web.configs from the pipeline -->
      <FilesForPackagingFromProject Remove="@(_WebConfigsToInsertAdditionalCS)" Condition="'@(_WebConfigsToInsertAdditionalCS)'!='' And !%(_WebConfigsToInsertAdditionalCS.Exclude) And Exists(%(_WebConfigsToInsertAdditionalCS.TransformOutputFile))"/>
      <!-- Add the transformed Web.configs at the new loction to the pipeline -->
      <FilesForPackagingFromProject Include="@(_WebConfigsToInsertAdditionalCS->'%(TransformOutputFile)')" Condition="'@(_WebConfigsToInsertAdditionalCS)'!='' And !%(_WebConfigsToInsertAdditionalCS.Exclude) And Exists(%(_WebConfigsToInsertAdditionalCS.TransformOutputFile))"/>
    </ItemGroup>

    <GetPublishingLocalizedString
       Importance="High"
       Condition="'@(_WebConfigsToInsertAdditionalCS)'!='' And !%(_WebConfigsToInsertAdditionalCS.Exclude) And Exists(%(_WebConfigsToInsertAdditionalCS.TransformOutputFile))"
       ID="PublishLocalizedString_InsertAdditionalConnectionStringTransformConfigToTransformOutputFile"
       ArgumentCount="2"
       Arguments="@(_WebConfigsToInsertAdditionalCS->'%(Identity)');%(TransformOutputFile)"
       LogType="Message" />
    <!-- "Auto ConnectionString Transformed @(_WebConfigsToInsertAdditionalCS) into %(TransformOutputFile)" />-->

    
    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                      Encoding="utf-8"
                      File="$(PackageLogDir)\PostInsertAdditionalWebCofigConnectionStrings.txt"
                      Lines="@(FilesForPackagingFromProject->'
                      From:%(Identity) 
                      DestinationRelativePath:%(DestinationRelativePath) 
                      Exclude:%(Exclude) 
                      FromTarget:%(FromTarget) 
                      Category:%(Category)
                      ProjectFileType:%(ProjectFileType)')" Overwrite="True" />
  </Target>

  <!--********************************************************************-->
  <!--Target InsertAdditionalWebCofigConnectionStrings-->
  <!--**********************************************************************-->
  <PropertyGroup>
    <InsertAdditionalWebCofigConnectionStringsDependsOn>
      $(OnBeforeInsertAdditionalWebCofigConnectionStrings);
      $(InsertAdditionalWebCofigConnectionStringsDependsOn);
      TransformWebConfig;
      ProfileTransformWebConfig;
      PreInsertAdditionalWebCofigConnectionStrings;
      InsertAdditionalWebCofigConnectionStringsCore;
      PostInsertAdditionalWebCofigConnectionStrings;
    </InsertAdditionalWebCofigConnectionStringsDependsOn>
    <InsertAdditionalWebCofigConnectionStringsBeforeTargets>
      $(InsertAdditionalWebCofigConnectionStringsBeforeTargets);
      PreAutoParameterizationWebConfigConnectionStrings;
      AutoParameterizationWebConfigConnectionStrings;
    </InsertAdditionalWebCofigConnectionStringsBeforeTargets>
  </PropertyGroup>
  <Target Name="InsertAdditionalWebCofigConnectionStrings" 
          DependsOnTargets="$(InsertAdditionalWebCofigConnectionStringsDependsOn)"
          BeforeTargets="$(InsertAdditionalWebCofigConnectionStringsBeforeTargets)"
          Condition="$(InsertAdditionalWebCofigConnectionStrings) And '@(_ConnectionStringsToInsert)' != ''">

  </Target>

  <!--********************************************************************-->
  <!--Target PreAutoParameterizationWebConfigConnectionStrings -->
  <!--********************************************************************-->
  <PropertyGroup>
    <PreAutoParameterizationWebConfigConnectionStringsDependsOn>
      PipelineTransformPhase;
    </PreAutoParameterizationWebConfigConnectionStringsDependsOn>
  </PropertyGroup>

  <Target Name="PreAutoParameterizationWebConfigConnectionStrings"
          DependsOnTargets="$(PreAutoParameterizationWebConfigConnectionStringsDependsOn)">
    <ItemGroup>
      <_WebConfigsToAutoParmeterizeCS Include="@(FilesForPackagingFromProject)"
                             Condition="'%(FilesForPackagingFromProject.Filename)%(FilesForPackagingFromProject.Extension)'=='$(ProjectConfigFileName)' And !%(FilesForPackagingFromProject.Exclude)">
        <TransformOriginalFolder>$(AutoParameterizationWebConfigConnectionStringsLocation)\original</TransformOriginalFolder>
        <TransformFileFolder>$(AutoParameterizationWebConfigConnectionStringsLocation)\assist</TransformFileFolder>
        <TransformOutputFile>$(AutoParameterizationWebConfigConnectionStringsLocation)\transformed\%(DestinationRelativePath)</TransformOutputFile>
        <TransformScope>$([System.IO.Path]::GetFullPath($(WPPAllFilesInSingleFolder)\%(DestinationRelativePath)))</TransformScope>
      </_WebConfigsToAutoParmeterizeCS>
      <_WebConfigsToAutoParmeterizeCSOuputFiles Include="@(_WebConfigsToAutoParmeterizeCS->'%(TransformOutputFile)')">
      </_WebConfigsToAutoParmeterizeCSOuputFiles>
    </ItemGroup>

    <PropertyGroup>
      <_WebConfigsToAutoParmeterizeCSOuputDirectories>@(_WebConfigsToAutoParmeterizeCSOuputFiles->'%(RootDir)%(Directory)')</_WebConfigsToAutoParmeterizeCSOuputDirectories>
      <_WebConfigsToAutoParmeterizeCSOuput>@(_WebConfigsToAutoParmeterizeCS->'%(TransformOutputFile)');$(_WebConfigsToAutoParmeterizeCsTransformOutputParametersFile)</_WebConfigsToAutoParmeterizeCSOuput>
    </PropertyGroup>

    <ItemGroup>
      <_WebConfigsToAutoParmeterizeCSOuputDirectories Include="$(_WebConfigsToAutoParmeterizeCSOuputDirectories)" />
    </ItemGroup>

    <!-- Remove the output file if there is change on $(UseParameterizeToTransformWebConfig)-->
    <Delete Files="@(_WebConfigsToAutoParmeterizeCS->'%(TransformOutputFile)');$(_WebConfigsToAutoParmeterizeCsTransformOutputParametersFile)"
            Condition="!Exists($(_WebConfigsToAutoParmeterizeCsTransformOutputParametersFile))"
            ContinueOnError="true"/>

    <MakeDir Directories="@(_WebConfigsToAutoParmeterizeCSOuputDirectories)" Condition="!Exists(%(Identity))"/>

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert) And ('@(_WebConfigsToAutoParmeterizeCS)'!='') And !%(Exclude)"
                      Encoding="utf-8"
                      Overwrite="True"
                      File="$(PackageLogDir)\PreAutoParameterizationWebConfigConnectionStrings.Log"
                      Lines="@(_WebConfigsToAutoParmeterizeCS->'
    AutoCSTransform   input: %(Identity) 
                      output: %(TransformOutputFile)
                      ')" />

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                      Encoding="utf-8"
                      Overwrite="False"
                      File="$(PackageLogDir)\PreAutoParameterizationWebConfigConnectionStrings.Log"
                      Lines="_WebConfigsToAutoParmeterizeCsTransformOutputParametersFile: $(_WebConfigsToAutoParmeterizeCsTransformOutputParametersFile)" />


    <!--Copy the original web.config-->
    <CopyPipelineFiles PipelineItems="@(_WebConfigsToAutoParmeterizeCS)"
                           SourceDirectory="$(WebPublishPipelineProjectDirectory)"
                           TargetDirectory="%(TransformOriginalFolder)"
                           SkipMetadataExcludeTrueItems="True"
                           UpdateItemSpec="False"
                           DeleteItemsMarkAsExcludeTrue ="True"
                       Condition="'@(_WebConfigsToAutoParmeterizeCS)' != ''">
      <Output TaskParameter="UpdatedPipelineItems" ItemName="_UpdatedWebConfigsToAutoParmeterizeCS"/>
    </CopyPipelineFiles>

    <!-- Delete those web.config have been updated if existed-->
    <Delete Files="@(_UpdatedWebConfigsToAutoParmeterizeCS->'%(TransformOutputFile)')" />

  </Target>

  <!--********************************************************************-->
  <!--Target AutoParameterizationWebConfigConnectionStringsCore -->
  <!--********************************************************************-->
  <PropertyGroup>
    <AutoParameterizationWebConfigConnectionStringsCoreDependsOn>
    </AutoParameterizationWebConfigConnectionStringsCoreDependsOn>
  </PropertyGroup>

  <Target Name="AutoParameterizationWebConfigConnectionStringsCore"
          Inputs="@(_WebConfigsToAutoParmeterizeCS)"
          Outputs="$(_WebConfigsToAutoParmeterizeCSOuput)"
          DependsOnTargets="$(AutoParameterizationWebConfigConnectionStringsCoreDependsOn)">

    <!-- First Delete the output parameter file-->
    <!-- Remove the output file if there is change on $(UseParameterizeToTransformWebConfig)-->
    <Delete Files="@(_WebConfigsToAutoParmeterizeCS->'%(TransformOutputFile)');$(_WebConfigsToAutoParmeterizeCsTransformOutputParametersFile)"/>

    <GetPublishingLocalizedString
      ID="PublishLocalizedString_WebConfigConnectionStringParameterDescription">
      <Output TaskParameter="Result" PropertyName="_PublishLocalizedString_WebConfigConnectionStringParameterDescription" />
    </GetPublishingLocalizedString>

    <PropertyGroup>
      <DeployParameterWebConfigConnectionStringName Condition="'$(DeployParameterWebConfigConnectionStringName)'==''">$(DeployParameterPrefix)%24(name)-$(ProjectConfigFileName) Connection String</DeployParameterWebConfigConnectionStringName>
      <DeployParameterWebConfigConnectionStringDescription Condition="'$(DeployParameterWebConfigConnectionStringDescription)'==''">%24(name) $(_PublishLocalizedString_WebConfigConnectionStringParameterDescription)</DeployParameterWebConfigConnectionStringDescription>
    </PropertyGroup>

    <!-- Description might have the xml special character, we need to escape it. -->
    <EscapeXMLString
      Source="$(DeployParameterWebConfigConnectionStringDescription)" >
      <Output TaskParameter="Result" PropertyName="_EscapedDeployParameterWebConfigConnectionStringDescription" />
    </EscapeXMLString>

    <!-- We need another escape otherwise, the following CreateProperty for the transformation  will evaluate it to lost the escape-->
    <EscapeXMLString
      Source="$(_EscapedDeployParameterWebConfigConnectionStringDescription)" >
      <Output TaskParameter="Result" PropertyName="_EscapedDeployParameterWebConfigConnectionStringDescription2" />
    </EscapeXMLString>

    <!-- First create the Tokenized Transformation in line -->
    <CreateProperty Value="
      &lt;?xml version=&quot;1.0&quot;?&gt;
        &lt;configuration xmlns:xdt=&quot;http://schemas.microsoft.com/XML-Document-Transform&quot;&gt;
          &lt;connectionStrings&gt;
            &lt;add
              connectionString=&quot;{% token='%24(ReplacableToken_%23(parameter)_%23(tokennumber))' xpathlocator='name' parameter='$(DeployParameterWebConfigConnectionStringName)' description='$(_EscapedDeployParameterWebConfigConnectionStringDescription2)' defaultValue='%24(connectionString)' tags='$(MsDeployDatabaseTag)' %}&quot;
              xdt:Transform=&quot;SetTokenizedAttributes(connectionString)&quot; xdt:SupressWarnings=&quot;True&quot; /&gt;
          &lt;/connectionStrings&gt;
        &lt;/configuration&gt;
        " >
      <Output TaskParameter="Value" PropertyName="_AutoParameterizeCSTransformWithDefaultValue"/>
    </CreateProperty>

    <!-- First create the Tokenized Transformation in line without Default Value -->
    <CreateProperty Value="
      &lt;?xml version=&quot;1.0&quot;?&gt;
        &lt;configuration xmlns:xdt=&quot;http://schemas.microsoft.com/XML-Document-Transform&quot;&gt;
          &lt;connectionStrings&gt;
            &lt;add
              connectionString=&quot;{% token='%24(ReplacableToken_%23(parameter)_%23(tokennumber))' xpathlocator='name' parameter='$(DeployParameterWebConfigConnectionStringName)' description='$(_EscapedDeployParameterWebConfigConnectionStringDescription2)'  tags='$(MsDeployDatabaseTag)' %}&quot;
              xdt:Transform=&quot;SetTokenizedAttributes(connectionString)&quot; xdt:SupressWarnings=&quot;True&quot; /&gt;
          &lt;/connectionStrings&gt;
        &lt;/configuration&gt;
        " >
      <Output TaskParameter="Value" PropertyName="_AutoParameterizeCSTransformNoDefaultValue"/>
    </CreateProperty>


    <PropertyGroup>
      <_AutoParameterizeCSTransform>$(_AutoParameterizeCSTransformWithDefaultValue)</_AutoParameterizeCSTransform>
      <_AutoParameterizeCSTransform Condition="$(AutoParameterizationWebConfigCSNoDefaultValue)">$(_AutoParameterizeCSTransformNoDefaultValue)</_AutoParameterizeCSTransform>
      <_AutoParameterizeCSUseXPath Condition="'$(_AutoParameterizeCSUseXPath)'==''">True</_AutoParameterizeCSUseXPath>
    </PropertyGroup>

    <!-- Now we use the tokenize transform to auto parameterize the web.config-->
    <ParameterizeTransformXml
      Source="@(_WebConfigsToAutoParmeterizeCS)"
      IsSourceAFile="True"
      Transform="$(_AutoParameterizeCSTransform)"
      IsTransformAFile="False"
      Destination="%(TransformOutputFile)"
      IsDestinationAFile="True"
      Scope="%(TransformScope)"
      EnableTokenizeParameters="True"
      UseXpathToFormParameter="$(_AutoParameterizeCSUseXPath)" 
      StackTrace="$(TransformWebConfigStackTraceEnabled)"
      SourceRootPath="$(WebPublishPipelineSourceRootDirectory)"
      Condition="!%(Exclude)">
      <!-- Created the DeclareParameters for later after DB import is included-->
      <Output TaskParameter="DeclareParameters" ItemName="_ParamsFromWebConfigsToAutoParmeterizeCS"/>
    </ParameterizeTransformXml>


    <!-- Set GenerateFileEvenIfEmpty to true to make sure incremental build works-->
    <ExportParametersFile
      Parameters="@(_ParamsFromWebConfigsToAutoParmeterizeCS)"
      DeclareParameterFile="$(_WebConfigsToAutoParmeterizeCsTransformOutputParametersFile)"
      GenerateFileEvenIfEmpty="True"
      />

  </Target>

  <!--********************************************************************-->
  <!--Target PostAutoParameterizationWebConfigConnectionStrings -->
  <!--********************************************************************-->
  <PropertyGroup>
    <PostAutoParameterizationWebConfigConnectionStringsDependsOn>
    </PostAutoParameterizationWebConfigConnectionStringsDependsOn>
  </PropertyGroup>

  <Target Name="PostAutoParameterizationWebConfigConnectionStrings"
          DependsOnTargets="$(PostAutoParameterizationWebConfigConnectionStringsDependsOn)">

    <ItemGroup>
      <!-- Remove untransformed Web.configs from the pipeline-->
      <FilesForPackagingFromProject Remove="@(_WebConfigsToAutoParmeterizeCS)" Condition="'@(_WebConfigsToAutoParmeterizeCS)'!='' And !%(_WebConfigsToAutoParmeterizeCS.Exclude) And Exists(%(_WebConfigsToAutoParmeterizeCS.TransformOutputFile))"/>
      <!-- Add the transformed Web.configs at the new loction to the pipeline-->
      <FilesForPackagingFromProject Include="@(_WebConfigsToAutoParmeterizeCS->'%(TransformOutputFile)')" Condition="'@(_WebConfigsToAutoParmeterizeCS)'!='' And !%(_WebConfigsToAutoParmeterizeCS.Exclude) And Exists(%(_WebConfigsToAutoParmeterizeCS.TransformOutputFile))"/>
    </ItemGroup>

    <GetPublishingLocalizedString
       Importance="High"
       Condition="'@(_WebConfigsToAutoParmeterizeCS)'!='' And !%(_WebConfigsToAutoParmeterizeCS.Exclude) And Exists(%(_WebConfigsToAutoParmeterizeCS.TransformOutputFile))"
       ID="PublishLocalizedString_AutoConnectionStringTransformConfigToTransformOutputFile"
       ArgumentCount="2"
       Arguments="@(_WebConfigsToAutoParmeterizeCS->'%(Identity)');%(TransformOutputFile)"
       LogType="Message" />
    <!-- "Auto ConnectionString Transformed @(_WebConfigsToAutoParmeterizeCS) into %(TransformOutputFile)" />-->

    <ImportParametersFile  Files="$(_WebConfigsToAutoParmeterizeCsTransformOutputParametersFile)"
                           DisableEscapeMSBuildVariable="$(ImportParametersFile_DisableEscapeMSBuildVariable)"
                           Condition="!$(DisableAllVSGeneratedMSDeployParameter) And Exists($(_WebConfigsToAutoParmeterizeCsTransformOutputParametersFile))" >
      <Output TaskParameter="Result" ItemName="_ImportAutoParameterizeCSTransformWebConfig"/>
    </ImportParametersFile>

    <ItemGroup>
      <MsDeployDeclareParameters Include="@(_ImportAutoParameterizeCSTransformWebConfig)"  Condition="'%(_ImportAutoParameterizeCSTransformWebConfig.Identity)' !=''">
        <Value>%(_ImportAutoParameterizeCSTransformWebConfig.DefaultValue)</Value>
        <Priority>$(VsWebConfigAutoCsParametersPriority)</Priority>
      </MsDeployDeclareParameters>
    </ItemGroup>

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                      Encoding="utf-8"
                      File="$(PackageLogDir)\PostAutoParameterizationWebConfigConnectionStrings.txt"
                      Lines="@(FilesForPackagingFromProject->'
                      From:%(Identity) 
                      DestinationRelativePath:%(DestinationRelativePath) 
                      Exclude:%(Exclude) 
                      FromTarget:%(FromTarget) 
                      Category:%(Category)
                      ProjectFileType:%(ProjectFileType)')" Overwrite="True" />
  </Target>

  <!--********************************************************************-->
  <!--Target AutoParameterizationWebConfigConnectionStrings-->
  <!--**********************************************************************-->
  <PropertyGroup>
    <AutoParameterizationWebConfigConnectionStringsDependsOn>
      $(OnBeforeAutoParameterizationWebConfigConnectionStrings);
      $(AutoParameterizationWebConfigConnectionStringsDependsOn);
      PipelineTransformPhase;
      PreAutoParameterizationWebConfigConnectionStrings;
      AutoParameterizationWebConfigConnectionStringsCore;
      PostAutoParameterizationWebConfigConnectionStrings;
    </AutoParameterizationWebConfigConnectionStringsDependsOn>
  </PropertyGroup>
  <Target Name="AutoParameterizationWebConfigConnectionStrings" DependsOnTargets="$(AutoParameterizationWebConfigConnectionStringsDependsOn)"
          Condition="$(AutoParameterizationWebConfigConnectionStrings)">

  </Target>

  <!--********************************************************************-->
  <!--Target PipelineTransformPhase -->
  <!--********************************************************************-->
  <PropertyGroup>
    <!-- Default global ExcludeTransformAssistFilesFromPublish to True. By default remove web.debug.config, web.release.config-->
    <ExcludeTransformAssistFilesFromPublish Condition="'$(ExcludeTransformAssistFilesFromPublish)'==''">True</ExcludeTransformAssistFilesFromPublish>
    <!-- Targets get execute before this Target-->
    <PipelineTransformPhaseDependsOn>
      $(OnBeforePipelineTransformPhase);
      $(PipelineTransformPhaseDependsOn);
      PipeLineCollectFilesPhase;
      TransformWebConfig;
      ProfileTransformWebConfig;
      InsertAdditionalWebCofigConnectionStrings;
      MarkWebConfigAssistFilesAsExclude;
    </PipelineTransformPhaseDependsOn>
  </PropertyGroup>

  <Target Name="PipelineTransformPhase"
          Outputs="@(FilesForPackagingFromProject)"
          DependsOnTargets="$(PipelineTransformPhaseDependsOn)">
    <!-- This is a phase separation point-->

    <GetPublishingLocalizedString
       ID="PublishLocalizedString_WebPublishPipelineTransformPhase"
       LogType="Message" />
    <!--<Message Text="Pipeline Transform Phase" />-->
    
    <CallTarget Targets="$(OnAfterPipelineTransformPhase)" RunEachTargetSeparately="False" />
  </Target>





  <!--********************************************************************-->
  <!--Target PipelineMsdeploySpecificTransformPhase -->
  <!--********************************************************************-->
  <PropertyGroup>
    <PipelineMsdeploySpecificTransformPhaseBeforeTargets>
      $(PipelineMsdeploySpecificTransformPhaseBeforeTargets);
      CopyAllFilesToSingleFolderForMsdeploy;
      PipelineCopyAllFilesToOneFolderForMsdeploy;
      PipelineDeployPhase;
    </PipelineMsdeploySpecificTransformPhaseBeforeTargets>
    <PipelineMsdeploySpecificTransformPhaseDependsOn>
      $(OnBeforePipelineMsdeploySpecificTransformPhase);
      $(PipelineMsdeploySpecificTransformPhaseDependsOn);
      InsertAdditionalWebCofigConnectionStrings;
      PreAutoParameterizationWebConfigConnectionStrings;
      AutoParameterizationWebConfigConnectionStrings;
    </PipelineMsdeploySpecificTransformPhaseDependsOn>
  </PropertyGroup>

  <Target Name="PipelineMsdeploySpecificTransformPhase"
          Outputs="@(FilesForPackagingFromProject)"
          DependsOnTargets="$(PipelineMsdeploySpecificTransformPhaseDependsOn)"
          BeforeTargets="$(PipelineMsdeploySpecificTransformPhaseBeforeTargets)"
          AfterTargets="$(PipelineMsdeploySpecificTransformPhaseAfterTargets)">
    <!--This is a phase separation point-->

    <!--This is a phase separation point. Some deploy doesn't need to copy all file to a folder, But for MSDeploy, it required to have all file in a single folder-->
    <GetPublishingLocalizedString
       ID="PublishLocalizedString_WebPublishPipelineDeployPhaseStage1"
       ArgumentCount="1"
       Arguments="PipelineMsdeploySpecificTransformPhase"
       LogType="Message" />
    
    <CallTarget Targets="$(OnAfterPipelineMsdeploySpecificTransformPhase)" RunEachTargetSeparately="False" />
  </Target>



  <!--********************************************************************    -->
  <!-- _WPPCopyWebApplication  Target                             -->
  <!-- This will materialize all the in-memory files list which is not mark   -->
  <!-- as excluded from packaging into the package temp folder.               -->
  <!--ToDo: remove the condition on ContentPath Package (we are doing it now because IIS team is not support it yet-->
  <!-- ********************************************************************   -->
  <PropertyGroup>
    <_WPPCopyWebApplicationDependsOn>
      $(OnBefore_WPPCopyWebApplication);
      $(_WPPCopyWebApplicationDependsOn);
      CleanWebProjectOutputDir;
      PipelineTransformPhase;
    </_WPPCopyWebApplicationDependsOn>
  </PropertyGroup>
  <Target Name="_WPPCopyWebApplication"
          DependsOnTargets="$(_WPPCopyWebApplicationDependsOn)"
          >
    <!-- In the case of the incremental Packaging/Publish, we need to find out the extra files and delete them-->
    <ItemGroup>
      <_AllExtraFilesUnderProjectOuputFolder Include="$(WebProjectOutputDir)\**" />
      <_AllExtraFilesUnderProjectOuputFolder
        Remove="@(FilesForPackagingFromProject->'$(WebProjectOutputDir)\%(DestinationRelativePath)')" />
    </ItemGroup>
    <!--Remove all extra files in the temp folder that's not in the @(FilesForPackagingFromProject-->
    <Delete Files="@(_AllExtraFilesUnderProjectOuputFolder)" />

    <!-- Make sure the folder exist -->
    <MakeDir Directories="$(WebProjectOutputDir)" Condition="!Exists('$(WebProjectOutputDir)')"/>

    <GetPublishingLocalizedString
       Importance="High"
       ID="PublishLocalizedString_WebPublishPipelineCopyWebApplication"
       ArgumentCount="2"
       Arguments="$(WebPublishPipelineProjectName);$(WebProjectOutputDir)"
       LogType="Message" />
    <!-- <Message Importance="high"
         Text="Copying Web Application Project Files for $(WebPublishPipelineProjectName) to $(WebProjectOutputDir)" />-->

    <!-- Copy the files to WebProjectOutputDir if needed-->
    <CopyPipelineFiles PipelineItems="@(FilesForPackagingFromProject)"
                           SourceDirectory="$(WebPublishPipelineProjectDirectory)"
                           TargetDirectory="$(WebProjectOutputDir)"
                           SkipMetadataExcludeTrueItems="True"
                           UpdateItemSpec="True"
                           DeleteItemsMarkAsExcludeTrue ="True"
                       Condition="'@(FilesForPackagingFromProject)' != ''">
    </CopyPipelineFiles>


    <!--Remove all empty folder that's left. Since it is not critical, we only log warning if we failed to delete empty folder.-->
    <RemoveEmptyDirectories Directories="$(WebProjectOutputDir)"  LogErrorAsWarning="True" />
    <MakeDir Directories="$(WebProjectOutputDir)" Condition="!Exists('$(WebProjectOutputDir)')"/>

    <CallTarget Targets="$(OnAfter_WPPCopyWebApplication)" RunEachTargetSeparately="False" />

  </Target>

  <!--********************************************************************-->
  <!--Target PipelinePreDeployCopyAllFilesToOneFolder -->
  <!--********************************************************************-->
  <PropertyGroup>
    <PipelinePreDeployCopyAllFilesToOneFolderDependsOn>
      $(OnBeforePipelinePreDeployCopyAllFilesToOneFolder);
      $(PipelinePreDeployCopyAllFilesToOneFolderDependsOn);
      PipelineTransformPhase;
      CopyAllFilesToSingleFolderForPackage;
    </PipelinePreDeployCopyAllFilesToOneFolderDependsOn>
  </PropertyGroup>

  <Target Name="PipelinePreDeployCopyAllFilesToOneFolder"
          DependsOnTargets="$(PipelinePreDeployCopyAllFilesToOneFolderDependsOn)">

    <!--This is a phase separation point.
        Some publish methods do not require copying all the files, but for MSDeploy
        we need to copy all the files to one location -->
    <GetPublishingLocalizedString
       ID="PublishLocalizedString_WebPublishPipelineDeployPhaseStage1"
       ArgumentCount="1"
       Arguments="PipelinePreDeployCopyAllFilesToOneFolder"
       LogType="Message" />
    
    <CallTarget Targets="$(OnAfterPipelinePreDeployCopyAllFilesToOneFolder)" RunEachTargetSeparately="False" />
  </Target>

  <!--
  CopyAllFilesToSingleFolderForMsdeploy target.
    This will copy all the files which are not marked to be excluded into the 
    package temp folder.
  -->
  <PropertyGroup>
    <OnBeforeCopyAllFilesToSingleFolderForMsdeploy Condition="$(CleanWPPAllFilesInSingleFolder)">
      $(OnBeforeCopyAllFilesToSingleFolderForMsdeploy);
      CleanWebsitesWPPAllFilesInSingleFolder;
    </OnBeforeCopyAllFilesToSingleFolderForMsdeploy>
    <CopyAllFilesToSingleFolderForMsdeployDependsOn>
      $(OnBeforeCopyAllFilesToSingleFolderForMsdeploy);
      $(CopyAllFilesToSingleFolderForMsdeployDependsOn);
    </CopyAllFilesToSingleFolderForMsdeployDependsOn>
  </PropertyGroup>
  <Target Name="CopyAllFilesToSingleFolderForMsdeploy"
          DependsOnTargets="$(CopyAllFilesToSingleFolderForMsdeployDependsOn)">

    <!--  In the case of the incremental Packaging/Publish, we need to find out the extra file and delete them -->
    <ItemGroup>
      <_AllExtraFilesUnderTempFolder Include="$(WPPAllFilesInSingleFolder)\**" />
      <_AllExtraFilesUnderTempFolder
        Remove="@(FilesForPackagingFromProject->'$(WPPAllFilesInSingleFolder)\%(DestinationRelativePath)')" />
    </ItemGroup>
    <!--Remove all extra files in the temp folder that are not in the @(FilesForPackagingFromProject-->
    <Delete Files="@(_AllExtraFilesUnderTempFolder)" />

    <!-- Make sure the folder exist -->
    <MakeDir Directories="$(WPPAllFilesInSingleFolder)" Condition="!Exists('$(WPPAllFilesInSingleFolder)')"/>

    <GetPublishingLocalizedString
       Importance="High"
       ID="PublishLocalizedString_WebPublishPipelineMaterializeAllFilesToTempDir"
       ArgumentCount="1"
       Arguments="$(WPPAllFilesInSingleFolder)"
       LogType="Message" />

    <!--Force Copy Of all file to the $(WPPAllFilesInSingleFolder) if needed-->
    <CopyPipelineFiles PipelineItems="@(FilesForPackagingFromProject)"
                           SourceDirectory="$(WebPublishPipelineProjectDirectory)"
                           TargetDirectory="$(WPPAllFilesInSingleFolder)"
                           SkipMetadataExcludeTrueItems="True"
                           UpdateItemSpec="True"
                           DeleteItemsMarkAsExcludeTrue ="True"
                       Condition="'@(FilesForPackagingFromProject)' != ''">
      <Output TaskParameter="ResultPipelineItems" ItemName="_FilesForPackagingFromProjectTempory"/>
    </CopyPipelineFiles>

    <ItemGroup>
      <FileWrites Include="@(_FilesForPackagingFromProjectTempory)" />
    </ItemGroup>

    <!--Workaround the MSBuild 2.0 limitation-->
    <ItemGroup>
      <FilesForPackagingFromProject Remove="@(FilesForPackagingFromProject)" />
      <FilesForPackagingFromProject Include="@(_FilesForPackagingFromProjectTempory)" />
    </ItemGroup>

    <!--Remove all Empty folder that's left. Since it is not critical, we only log warning if we failed to delete empty folder.-->
    <RemoveEmptyDirectories Directories="$(WPPAllFilesInSingleFolder)"  LogErrorAsWarning="True" />
    <MakeDir Directories="$(WPPAllFilesInSingleFolder)" Condition="!Exists('$(WPPAllFilesInSingleFolder)')"/>

    <!--Now we are all file in the single folder-->
    <PropertyGroup>
      <IsWPPAllFilesInSingleFolder>True</IsWPPAllFilesInSingleFolder>
    </PropertyGroup>

    <CallTarget Targets="$(OnAfterCopyAllFilesToSingleFolderForMsdeploy)" RunEachTargetSeparately="False" />

  </Target>

  <!--Target PipelineCopyAllFilesToOneFolderForMsdeploy -->
  <PropertyGroup>
    <PipelineCopyAllFilesToOneFolderForMsdeployDependsOn>
      $(OnBeforePipelineCopyAllFilesToOneFolderForMsdeploy);
      $(PipelineCopyAllFilesToOneFolderForMsdeployDependsOn);
      PipelineTransformPhase;
      CopyAllFilesToSingleFolderForMsdeploy;
    </PipelineCopyAllFilesToOneFolderForMsdeployDependsOn>
  </PropertyGroup>

  <Target Name="PipelineCopyAllFilesToOneFolderForMsdeploy"
          DependsOnTargets="$(PipelineCopyAllFilesToOneFolderForMsdeployDependsOn)">

    <!-- This is a phase separation point. -->
    <GetPublishingLocalizedString
       ID="PublishLocalizedString_WebPublishPipelineDeployPhaseStage1"
       ArgumentCount="1"
       Arguments="PipelineCopyAllFilesToOneFolderForMsdeploy"
       LogType="Message" />
    
    <CallTarget Targets="$(OnAfterPipelineCopyAllFilesToOneFolderForMsdeploy)" RunEachTargetSeparately="False" />
  </Target>
  
  
  <Target Name="GatherAllFilesToPublish" DependsOnTargets="PipelineTransformPhase;CopyAllFilesToSingleFolderForPackage;" />

  <Target Name="_CheckPublishToolsUpToDate" Condition="'$(_WebPublishProfileFileWillBeImported)'=='true'">
    <PublishToolsVersionCheck VersionInPublishProfile="$(_MinWebPublishToolsVersion)" />
  </Target>  
  
  <!--GetProjectProperties Task-->
  <PropertyGroup>
    <GetProjectWebPropertiesDependsOn Condition="'$(GetProjectWebPropertiesDependsOn)'==''">
      _CheckPublishToolsUpToDate
    </GetProjectWebPropertiesDependsOn>
  </PropertyGroup>

  <Target Name="GetProjectWebProperties"
          Condition="'$(UseIis)' == '' Or '$(IisUrl)' == '' Or '$(LocalIisVersion)' == '' Or '$(DestinationIisVersion)' ==''"
          DependsOnTargets="$(GetProjectWebPropertiesDependsOn)">

    <PropertyGroup>
      <LocalIisVersion>$(Registry:HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Inetstp@MajorVersion)</LocalIisVersion>
      <LocalIisVersion Condition="'$(LocalIisVersion)'==''">0</LocalIisVersion>
    </PropertyGroup>

    <GetProjectProperties ProjectFileFullPath="$(WebPublishPipelineWAPProjectSettings)">
      <Output TaskParameter="UseIis"  PropertyName="UseIis" Condition="'$(UseIis)' == ''" />
      <Output TaskParameter="IisUrl"  PropertyName="IisUrl" Condition="'$(IisUrl)' == ''"/>
    </GetProjectProperties>

    <PropertyGroup>
      <_DeploymentUseIis Condition="'$(_DeploymentUseIis)'=='' And $(IncludeIisSettings)">$(UseIis)</_DeploymentUseIis>
      <_DeploymentUseIis Condition="'$(_DeploymentUseIis)'==''">False</_DeploymentUseIis>
      <_DeploymentAsIisApp>$(DeployAsIisApp)</_DeploymentAsIisApp>
      <_DeploymentAsIisApp Condition="$(_DeploymentUseIis)">False</_DeploymentAsIisApp>
      <DestinationUseIis Condition="'$(DestinationUseIis)'=='' And $(IncludeIisSettings)">$(UseIis)</DestinationUseIis>
      <DestinationUseIis Condition="'$(DestinationUseIis)'==''">False</DestinationUseIis>
      <_DeploymentUseIISExpressHint>$(UseIISExpress)</_DeploymentUseIISExpressHint>
      <_DeploymentUseIISExpressHint Condition="'$(_DeploymentUseIISExpressHint)'==''">False</_DeploymentUseIISExpressHint>
    </PropertyGroup>

    <!--We should only get IIS relative information when $(IncludeIisSettings) is true-->
    <MapUriToIisWebServer Condition="$(UseIis) And ('$(IisUrl)' != '') And $(IncludeIisSettings)"
      ProjectIisUrl="$(IisUrl)"
      UseIISExpressHintFlag="$(_DeploymentUseIISExpressHint)">
      <Output TaskParameter="MetakeyPath" PropertyName="_MSDeployIisUrlSource_MetakeyPathSearchPath"  />
      <Output TaskParameter="MetakeyPathUsingSiteName" PropertyName="_MSDeployIisUrlSource_MetakeyPath"  />
      <Output TaskParameter="AppHostConfigPath" PropertyName="_MSDeployIisUrlSource_AppHostConfigPath"  />
      <Output TaskParameter="IisWebsiteId" PropertyName="IisUrl_SiteID"  />
      <Output TaskParameter="IisWebsiteFriendlyName" PropertyName="IisUrl_SiteFriendlyName"  />
      <Output TaskParameter="SiteRelativePath" PropertyName="IisUrl_SiteRelativePath"  />
      <Output TaskParameter="WebServerAppHostConfigDirectory" PropertyName="IisServer_WebServerAppHostConfigDirectory"  />
      <Output TaskParameter="WebServerDirectory" PropertyName="IisServer_WebServerDirectory"  />
      <Output TaskParameter="WebServerManifest" PropertyName="IisServer_WebServerManifest"  />
      <Output TaskParameter="IisMajorVersion" PropertyName="IisServer_IisMajorVersion"  />
      <Output TaskParameter="ApplicationPoolName" PropertyName="IisServer_IisAppPoolName"  />
    </MapUriToIisWebServer>

    <FilterByItems PipelineItems="@(_IISApplicationPool)"
                   Filter="$(IisServer_IisAppPoolName)">
      <Output TaskParameter="InFilter" ItemName="_IISApplicationPoolFound"/>
    </FilterByItems>

    <PropertyGroup>
      <_Destination_IisServer_IisAppPoolName Condition="'@(_IISApplicationPoolFound)'!=''">%(_IISApplicationPoolFound.DestinationIISApplicationPool)</_Destination_IisServer_IisAppPoolName>
      <_Destination_IisServer_IisAppPoolName Condition="'$(_Destination_IisServer_IisAppPoolName)'==''">$(IisServer_IisAppPoolName)</_Destination_IisServer_IisAppPoolName>
    </PropertyGroup>
    
    <PropertyGroup>
      <_IsRootWebsite>False</_IsRootWebsite>
      <_IsRootWebsite Condition="('$(IisUrl_SiteRelativePath)' == '/')">True</_IsRootWebsite>
      <DefaultDeployIisAppPath Condition="$(_IsRootWebsite)">$(DefaultDeployIisRootAppPath)</DefaultDeployIisAppPath>
      <LocalIisVersion Condition="'$(IisServer_IisMajorVersion)' != '0' And '$(IisServer_IisMajorVersion)' != '' ">$(IisServer_IisMajorVersion)</LocalIisVersion>
      <DestinationIisVersion Condition="'$(DestinationIisVersion)'==''">$(LocalIisVersion)</DestinationIisVersion>
    </PropertyGroup>

    <!-- elevate this property out to global scope to be used by transport target-->
    <CreateProperty Value="$(_DeploymentUseIis)">
      <Output TaskParameter="Value" PropertyName="IncludeIisSettingsOnPublish"/>
    </CreateProperty>
  </Target>

  <!-- 
  GetMSDeployInstalledVersionPath 
    This target will determine where msdeploy.exe has been installed
  -->
  <PropertyGroup>
    <GetMSDeployInstalledVersionPathDependsOn Condition="'$(GetMSDeployInstalledVersionPathDependsOn)'==''">
    </GetMSDeployInstalledVersionPathDependsOn>
  </PropertyGroup>

  <Target Name="GetMSDeployInstalledVersionPath"
          Condition="'$(_MSDeployVersionsToTry)' == '' Or '$(MSDeployPath)' == '' "
          DependsOnTargets="$(GetMSDeployInstalledVersionPathDependsOn)">

    <!--get a list of subkeyNames from the registry-->
    <GetLocalMachineRegistrySubKeyNames Key="SOFTWARE\Microsoft\IIS Extensions\MSDeploy">
      <Output TaskParameter="Result" ItemName="_MSDeployInstalledVersionPathFromRegistry"/>
    </GetLocalMachineRegistrySubKeyNames>

    <ItemGroup>
      <_MSDeployInstalledVersionPath Include="@(_MSDeployInstalledVersionPathFromRegistry)">
        <InstallPath>$([MSBuild]::GetRegistryValue('HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\IIS Extensions\MSDeploy\%(_MSDeployInstalledVersionPathFromRegistry.Identity)', 'InstallPath'))</InstallPath>
        <DllVersionString>$([MSBuild]::GetRegistryValue('HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\IIS Extensions\MSDeploy\%(_MSDeployInstalledVersionPathFromRegistry.Identity)', 'Version'))</DllVersionString>
      </_MSDeployInstalledVersionPath>
      <_MSDeployInstalledVersions Include="@(_MSDeployInstalledVersionPath)" Condition="'%(_MSDeployInstalledVersionPath.InstallPath)'!='' And  '%(_MSDeployInstalledVersionPath.DllVersionString)'!=''" >
        <DllVersion>$([System.Version]::New(%(_MSDeployInstalledVersionPath.DllVersionString)).Major).$([System.Version]::New(%(_MSDeployInstalledVersionPath.DllVersionString)).Minor)</DllVersion>
      </_MSDeployInstalledVersions>
    </ItemGroup>

    <FilterByItems PipelineItems="@(_MSDeployInstalledVersions)"
                   SourceMetadataName="DllVersion"
                   Condition="'$(_MSDeployVersionsToTry)' != ''"
                   Filter="$(_MSDeployVersionsToTry)">
      <Output TaskParameter="OutFilter" ItemName="_NeedtoRemove_MSDeployInstalledVersions"/>
    </FilterByItems>

    <ItemGroup>
      <_MSDeployInstalledVersions Remove="@(_NeedtoRemove_MSDeployInstalledVersions)" />
    </ItemGroup>

    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                      Encoding="utf-8"
                      File="$(PackageLogDir)\MSDeployInstalledVersions.txt"
                      Lines="@(_MSDeployInstalledVersions->'
                      Identity:%(Identity)
                      InstallPath:%(InstallPath) 
                      DllVersionString:%(DllVersionString) 
                      DllVersion:%(DllVersion)')" Overwrite="True" />


    
    <PropertyGroup>
      <_DefaultMSDeployMaxVersion>0</_DefaultMSDeployMaxVersion>
      <_DefaultMSDeployMaxVersion Condition="'%(_MSDeployInstalledVersions.Identity)' != ''">$([System.Math]::Max($([System.Convert]::ToUInt32('$(_DefaultMSDeployMaxVersion)')),$([System.Convert]::ToUInt32('%(_MSDeployInstalledVersions.Identity)'))))</_DefaultMSDeployMaxVersion>
    </PropertyGroup>

    <GetPublishingLocalizedString
     Condition="'$(_DefaultMSDeployMaxVersion)' != ''"
     ID="PublishLocalizedString_MsBuildPropertySettingValue"
     ArgumentCount="2"
     Arguments="_DefaultMSDeployMaxVersion;$(_DefaultMSDeployMaxVersion)"
     LogType="Message" />

    <ItemGroup>
      <_SelectedMSDeployMaxVersion Include="@(_MSDeployInstalledVersions)"
                               Condition="'%(_MSDeployInstalledVersions.Identity)' =='$(_DefaultMSDeployMaxVersion)' " />
    </ItemGroup>

    <!--If $(MSDeployPath) is specified, we try the best to match the matching msdeploy dll version-->
    <ItemGroup Condition="'$(_MSDeployVersionsToTry)' == '' And '$(MSDeployPath)' != '' ">
      <_SelectedMSDeployPath Include="@(_MSDeployInstalledVersions)"
                             Condition="'%(_MSDeployInstalledVersions.InstallPath)' =='$(MSDeployPath)' " />
    </ItemGroup>
    <PropertyGroup Condition="'$(_MSDeployVersionsToTry)' == '' And '$(MSDeployPath)' != '' " >
      <_MSDeployVersionsToTry>@(_SelectedMSDeployPath->'%(DllVersion)')</_MSDeployVersionsToTry>
    </PropertyGroup>

    <!--
    If $(MSDeployPath) is not specified and $(_MSDeployVersionToTry) is not specified.  
    We use the Higest version of the MSDeploy Version that's currently installed--> 
    <PropertyGroup>
      <_MSDeployVersionsToTry  Condition="'$(_MSDeployVersionsToTry)' == ''">@(_SelectedMSDeployMaxVersion->'%(DllVersion)')</_MSDeployVersionsToTry>
      <MSDeployPath Condition="'$(MSDeployPath)' == ''">@(_SelectedMSDeployMaxVersion->'%(InstallPath)')</MSDeployPath>
    </PropertyGroup>

    <GetPublishingLocalizedString
     Condition="'$(_MSDeployVersionsToTry)' != ''"
     ID="PublishLocalizedString_MsBuildPropertySettingValue"
     ArgumentCount="2"
     Arguments="_MSDeployVersionsToTry;$(_MSDeployVersionsToTry)"
     LogType="Message" />
    
    <GetPublishingLocalizedString
     Condition="'$(MSDeployPath)' != ''"
     ID="PublishLocalizedString_MsBuildPropertySettingValue"
     ArgumentCount="2"
     Arguments="MSDeployPath;$(MSDeployPath)"
     LogType="Message" />

  </Target>

  <!--********************************************************************-->
  <!-- Package target -->
  <!--********************************************************************-->
  <PropertyGroup>
    <PackageDependsOn Condition="'$(PackageDependsOn)'==''">
      PipelineCopyAllFilesToOneFolderForMsdeploy;
      ImportPublishingParameterValues;
      PipelineMsdeploySpecificTransformPhase;
    </PackageDependsOn>
  </PropertyGroup>
  <Target Name="Package"
          Condition="$(_CreatePackage)"
          DependsOnTargets="$(PackageDependsOn)">

    <MakeDir Condition="$(EnablePackageProcessLoggingAndAssert) And !Exists('$(PackageLogDir)')"
             Directories="$(PackageLogDir)" />
    <WriteLinesToFile Condition="$(EnablePackageProcessLoggingAndAssert)"
                      Encoding="utf-8"
                      File="$(PackageLogDir)\Prepackage.txt"
                      Lines="@(FilesForPackagingFromProject->'
                      From:%(Identity) 
                      DestinationRelativePath:%(DestinationRelativePath) 
                      Exclude:%(Exclude) 
                      FromTarget:%(FromTarget) 
                      Category:%(Category)
                      ProjectFileType:%(ProjectFileType)')" Overwrite="True" />


    <GetPublishingLocalizedString
       ID="PublishLocalizedString_WebPublishPipelineMSDeploySettings"
       LogType="Message" />

    <GetPublishingLocalizedString
          Condition="'$(LocalIisVersion)' != ''"
          ID="PublishLocalizedString_MsBuildPropertySettingValue"
          ArgumentCount="2"
          Arguments="LocalIisVersion;$(LocalIisVersion)"
          LogType="Message" />
    <GetPublishingLocalizedString
          Condition="'$(DestinationIisVersion)' != ''"
          ID="PublishLocalizedString_MsBuildPropertySettingValue"
          ArgumentCount="2"
          Arguments="DestinationIisVersion;$(DestinationIisVersion)"
          LogType="Message" />
    <GetPublishingLocalizedString
          Condition="'$(UseIis)' != ''"
          ID="PublishLocalizedString_MsBuildPropertySettingValue"
          ArgumentCount="2"
          Arguments="UseIis;$(UseIis)"
          LogType="Message" />
    <GetPublishingLocalizedString
      Condition="'$(IisUrl)' != ''"
      ID="PublishLocalizedString_MsBuildPropertySettingValue"
      ArgumentCount="2"
      Arguments="IisUrl;$(IisUrl)"
      LogType="Message" />
    <GetPublishingLocalizedString
          Condition="'$(IncludeIisSettings)' != ''"
          ID="PublishLocalizedString_MsBuildPropertySettingValue"
          ArgumentCount="2"
          Arguments="IncludeIisSettings;$(IncludeIisSettings)"
          LogType="Message" />
    <GetPublishingLocalizedString
          Condition="'$(_DeploymentUseIis)' != ''"
          ID="PublishLocalizedString_MsBuildPropertySettingValue"
          ArgumentCount="2"
          Arguments="_DeploymentUseIis;$(_DeploymentUseIis)"
          LogType="Message" />
    <GetPublishingLocalizedString
          Condition="'$(DestinationUseIis)' != ''"
          ID="PublishLocalizedString_MsBuildPropertySettingValue"
          ArgumentCount="2"
          Arguments="DestinationUseIis;$(DestinationUseIis)"
          LogType="Message" />
    <GetPublishingLocalizedString
          Condition="'$(IisUrl_SiteID)' != ''"
          ID="PublishLocalizedString_MsBuildPropertySettingValue"
          ArgumentCount="2"
          Arguments="IisUrl_SiteID;$(IisUrl_SiteID)"
          LogType="Message" />
    <GetPublishingLocalizedString
          Condition="'$(IisUrl_SiteFriendlyName)' != ''"
          ID="PublishLocalizedString_MsBuildPropertySettingValue"
          ArgumentCount="2"
          Arguments="IisUrl_SiteFriendlyName;$(IisUrl_SiteFriendlyName)"
          LogType="Message" />
    <GetPublishingLocalizedString
          Condition="'$(IisUrl_SiteRelativePath)' != ''"
          ID="PublishLocalizedString_MsBuildPropertySettingValue"
          ArgumentCount="2"
          Arguments="IisUrl_SiteRelativePath;$(IisUrl_SiteRelativePath)"
          LogType="Message" />


    <GetPublishingLocalizedString
          Condition="'$(IisServer_IisAppPoolName)' != ''"
          ID="PublishLocalizedString_MsBuildPropertySettingValue"
          ArgumentCount="2"
          Arguments="IisServer_IisAppPoolName;$(IisServer_IisAppPoolName)"
          LogType="Message" />

    
    <GetPublishingLocalizedString
          Condition="'$(_Destination_IisServer_IisAppPoolName)' != ''"
          ID="PublishLocalizedString_MsBuildPropertySettingValue"
          ArgumentCount="2"
          Arguments="_Destination_IisServer_IisAppPoolName;$(_Destination_IisServer_IisAppPoolName)"
          LogType="Message" />


    <!--"Invoking MSDeploy to generate the package 
             Machine IIS Version is $(LocalIisVersion)
             Deploy IIS Version is $(DestinationIisVersion)
             UseIis is $(UseIis)
             IisUrl is $(IisUrl)
             IncludeIisSettings is $(IncludeIisSettings)
             _DeploymentUseIis is $(_DeploymentUseIis)
             DestinationUseIis is $(DestinationUseIis)
             IisUrl_SiteID is $(IisUrl_SiteID)
             IisUrl_SiteFriendlyName is $(IisUrl_SiteFriendlyName)
             IisUrl_SiteRelativePath is $(IisUrl_SiteRelativePath)"
             />-->
    <ItemGroup>
      <_PackageFileLocation Include="$(PackageFileName)" />
    </ItemGroup>

    <PropertyGroup>
      <_PackageRoot Condition="$(PackageAsSingleFile)">@(_PackageFileLocation->'%(RootDir)%(Directory)')</_PackageRoot>
      <_PackageRoot Condition="!$(PackageAsSingleFile)">$(PackageArchiveRootDir)</_PackageRoot>
    </PropertyGroup>

    <MakeDir Condition="!Exists($(_PackageRoot))"  Directories="$(_PackageRoot)" />
    <CallTarget Targets="PackageUsingManifest"/>
  </Target>

  <PropertyGroup>
    <OnBeforeCopyAllFilesToSingleFolderForPackage Condition="$(CleanWPPAllFilesInSingleFolder)">
      $(OnBeforeCopyAllFilesToSingleFolderForPackage);
      CleanWebsitesWPPAllFilesInSingleFolder;
    </OnBeforeCopyAllFilesToSingleFolderForPackage>
    <CopyAllFilesToSingleFolderForPackageDependsOn>
      $(OnBeforeCopyAllFilesToSingleFolderForPackage);
      $(CopyAllFilesToSingleFolderForPackageDependsOn);
    </CopyAllFilesToSingleFolderForPackageDependsOn>
  </PropertyGroup>
  <Target Name="CopyAllFilesToSingleFolderForPackage"
          DependsOnTargets="$(CopyAllFilesToSingleFolderForPackageDependsOn)"
          >

    <!-- In the case of the incremental Packaging/Publish, we need to find out the extra file and delete them-->
    <ItemGroup>
      <_AllExtraFilesUnderTempFolder Include="$(WPPAllFilesInSingleFolder)\**" />
      <_AllExtraFilesUnderTempFolder
        Remove="@(FilesForPackagingFromProject->'$(WPPAllFilesInSingleFolder)\%(DestinationRelativePath)')" />
    </ItemGroup>
    <!--Remove all extra files in the temp folder that's not in the @(FilesForPackagingFromProject-->
    <Delete Files="@(_AllExtraFilesUnderTempFolder)" />

    <MakeDir Directories="$(WPPAllFilesInSingleFolder)" Condition="!Exists('$(WPPAllFilesInSingleFolder)')"/>

    <GetPublishingLocalizedString
       Importance="High"
       ID="PublishLocalizedString_WebPublishPipelineMaterializeAllFilesToTempDir"
       ArgumentCount="1"
       Arguments="$(WPPAllFilesInSingleFolder)"
       LogType="Message" />
    
    <!--Force Copy Of all file to the $(WPPAllFilesInSingleFolder) if needed-->
    <CopyPipelineFiles PipelineItems="@(FilesForPackagingFromProject)"
                           SourceDirectory="$(WebPublishPipelineProjectDirectory)"
                           TargetDirectory="$(WPPAllFilesInSingleFolder)"
                           SkipMetadataExcludeTrueItems="True"
                           UpdateItemSpec="True"
                           DeleteItemsMarkAsExcludeTrue ="True"
                       Condition="'@(FilesForPackagingFromProject)' != ''">
      <Output TaskParameter="ResultPipelineItems" ItemName="_FilesForPackagingFromProjectTempory"/>
    </CopyPipelineFiles>

    <ItemGroup>
      <FileWrites Include="@(_FilesForPackagingFromProjectTempory)" />
    </ItemGroup>
    
    <!--Workaround the MSBuild 2.0 limitation-->
    <ItemGroup>
      <FilesForPackagingFromProject Remove="@(FilesForPackagingFromProject)" />
      <FilesForPackagingFromProject Include="@(_FilesForPackagingFromProjectTempory)" />
    </ItemGroup>

    <!--Remove all Empty folder that's left. Since it is not critical, we only log warning if we failed to delete empty folder.-->
    <RemoveEmptyDirectories Directories="$(WPPAllFilesInSingleFolder)"  LogErrorAsWarning="True" />
    <MakeDir Directories="$(WPPAllFilesInSingleFolder)" Condition="!Exists('$(WPPAllFilesInSingleFolder)')"/>

    <!--Now we are all file in the single folder-->
    <PropertyGroup>
      <IsWPPAllFilesInSingleFolder>True</IsWPPAllFilesInSingleFolder>
    </PropertyGroup>

    <CallTarget Targets="$(OnAfterCopyAllFilesToSingleFolderForPackage)" RunEachTargetSeparately="False" />
  </Target>


  <!--********************************************************************-->
  <!-- AddIis7ToSourceManifest  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <BeforeAddIis7ToSourceManifest Condition="'$(BeforeAddIis7ToSourceManifest)'==''">
    </BeforeAddIis7ToSourceManifest>
    <AfterAddIis7ToSourceManifest Condition="'$(AfterAddIis7ToSourceManifest)'==''">
    </AfterAddIis7ToSourceManifest>
    <AddIis7ToSourceManifestDependsOn Condition="'$(AddIis7ToSourceManifestDependsOn)' == ''">
      $(BeforeAddIis7ToSourceManifest);
      PipelineCopyAllFilesToOneFolderForMsdeploy;
      GenerateMsDeployManifestSettings;
    </AddIis7ToSourceManifestDependsOn>
  </PropertyGroup>
  <Target Name="AddIis7ToSourceManifest"
          Condition="$(_DeploymentUseIis) And ($(LocalIisVersion) &gt;= '7')"
          DependsOnTargets="$(AddIis7ToSourceManifestDependsOn)">
    <GetPublishingLocalizedString
       Condition="'$(IisUrl)'==''"
       ID="PublishLocalizedString_ErrorUseIisIsTrueButIisUrlIsEmpty"
       LogType="Error" />
    
    <Error Text="Target AddIis7ToSourceManifest failed"
           Condition="'$(IisUrl)'==''" />
      
    <ItemGroup>
      <MsDeploySourceManifest Include="appHostConfig">
        <Path>$(_MSDeployIisUrlSource_AppHostConfigPath)</Path>
      </MsDeploySourceManifest>
    </ItemGroup>

    <CallTarget Condition="'$(AfterAddIis7ToSourceManifest)' != ''"
                Targets="$(AfterAddIis7ToSourceManifest)"
                RunEachTargetSeparately="False" />
  </Target>


  <!--********************************************************************-->
  <!-- AddDeclareParametersItemsForIis7  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <BeforeAddDeclareParametersItemsForIis7 Condition="'$(BeforeAddDeclareParametersItemsForIis7)'==''">
    </BeforeAddDeclareParametersItemsForIis7>
    <AfterAddDeclareParametersItemsForIis7 Condition="'$(AfterAddDeclareParametersItemsForIis7)'==''">
    </AfterAddDeclareParametersItemsForIis7>
    <AddDeclareParametersItemsForIis7DependsOn Condition="'$(AddDeclareParametersItemsForIis7DependsOn)' == ''">
      $(BeforeAddDeclareParametersItemsForIis7);
      PipelineCopyAllFilesToOneFolderForMsdeploy;
      GenerateMsDeployManifestSettings;
    </AddDeclareParametersItemsForIis7DependsOn>
  </PropertyGroup>
  <Target Name="AddDeclareParametersItemsForIis7"
          Condition="$(_DeploymentUseIis) And ($(LocalIisVersion) &gt;= '7')"
          DependsOnTargets="$(AddDeclareParametersItemsForIis7DependsOn)">
    <!--Parameterize the default value of the Path-->
    <EscapeTextForRegularExpressions Text="$(_MSDeployIisUrlSource_AppHostConfigPath)">
      <Output TaskParameter="Result" PropertyName="_EscapeRegEx_MSDeployIisUrlSource_AppHostConfigPath" />
    </EscapeTextForRegularExpressions>
    <ItemGroup Condition="!$(DisableAllVSGeneratedMSDeployParameter) And $(_DeploymentUseIis) And ($(LocalIisVersion) &gt;= '7')" >
      <MsDeployDeclareParameters Include="$(DeployParameterIISAppName)">
        <Kind>ProviderPath</Kind>
        <Scope>AppHostConfig</Scope>
        <Match>^$(_EscapeRegEx_MSDeployIisUrlSource_AppHostConfigPath)$</Match>
        <Description>$(DeployParameterIISAppNameDescription)</Description>
        <DefaultValue>$(_MSDeployIisAppPathIIS7)</DefaultValue>
        <Value>$(_MSDeployIisAppPathIIS7)</Value>
        <Tags>IisApp</Tags>
        <Priority>$(VsIisAppParametersPriority)</Priority>
      </MsDeployDeclareParameters>

      <MsDeployDeclareParameters Include="$(DeployParameterIISAppPoolName)"
                                Condition="'$(IisServer_IisAppPoolName)' != '' And '$(ParameterApplicationPool)' == 'True'">
        <Kind>DeploymentObjectAttribute</Kind>
        <Scope>application</Scope>
        <Match>application[@applicationPool='$(IisServer_IisAppPoolName)']/@applicationPool</Match>
        <Description>$(DeployParameterIISAppPoolNameDescription)</Description>
        <DefaultValue>$(_Destination_IisServer_IisAppPoolName)</DefaultValue>
        <Value>$(_Destination_IisServer_IisAppPoolName)</Value>
        <Tags></Tags>
        <Priority>$(VsIisAppParametersPriority)</Priority>
      </MsDeployDeclareParameters>
     
    </ItemGroup>


    <CallTarget Condition="'$(AfterAddDeclareParametersItemsForIis7)' != ''"
                Targets="$(AfterAddDeclareParametersItemsForIis7)"
                RunEachTargetSeparately="false" />
  </Target>

  <!--********************************************************************-->
  <!-- AddIis6ToSourceManifest  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <BeforeAddIis6ToSourceManifest Condition="'$(BeforeAddIis6ToSourceManifest)'==''">
    </BeforeAddIis6ToSourceManifest>
    <AfterAddIis6ToSourceManifest Condition="'$(AfterAddIis6ToSourceManifest)'==''">
    </AfterAddIis6ToSourceManifest>
    <AddIis6ToSourceManifestDependsOn Condition="'$(AddIis6ToSourceManifestDependsOn)' == ''">
      $(BeforeAddIis6ToSourceManifest);
      PipelineCopyAllFilesToOneFolderForMsdeploy;
      GenerateMsDeployManifestSettings;
    </AddIis6ToSourceManifestDependsOn>
  </PropertyGroup>
  <Target Name="AddIis6ToSourceManifest"
          Condition="$(_DeploymentUseIis) And ($(LocalIisVersion) &lt; '7') "
          DependsOnTargets="$(AddIis6ToSourceManifestDependsOn)">

    <GetPublishingLocalizedString
       Condition="'$(IisUrl)'==''"
       ID="PublishLocalizedString_ErrorUseIisIsTrueButIisUrlIsEmpty"
       LogType="Error" />

    <Error Text="Target AddIis6ToSourceManifest failed"
          Condition="'$(IisUrl)'==''" />
    
    <ItemGroup>
      <MsDeploySourceManifest Include="MetaKey">
        <Path>$(_MSDeployIisUrlSource_MetakeyPath)</Path>
      </MsDeploySourceManifest>
    </ItemGroup>
    <CallTarget Condition="'$(AfterAddIis6ToSourceManifest)' != ''"
                Targets="$(AfterAddIis6ToSourceManifest)"
                RunEachTargetSeparately="False" />
  </Target>


  <!--********************************************************************-->
  <!-- AddDeclareParametersItemsForIis6  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <BeforeAddDeclareParametersItemsForIis6 Condition="'$(BeforeAddDeclareParametersItemsForIis6)'==''">
    </BeforeAddDeclareParametersItemsForIis6>
    <AfterAddDeclareParametersItemsForIis6 Condition="'$(AfterAddDeclareParametersItemsForIis6)'==''">
    </AfterAddDeclareParametersItemsForIis6>
    <AddDeclareParametersItemsForIis6DependsOn Condition="'$(AddDeclareParametersItemsForIis6DependsOn)' == ''">
      $(BeforeAddDeclareParametersItemsForIis6);
      PipelineCopyAllFilesToOneFolderForMsdeploy;
      GenerateMsDeployManifestSettings;
    </AddDeclareParametersItemsForIis6DependsOn>
  </PropertyGroup>
  <Target Name="AddDeclareParametersItemsForIis6"
          Condition="$(_DeploymentUseIis) And ($(LocalIisVersion) &lt; '7') "
          DependsOnTargets="$(AddDeclareParametersItemsForIis6DependsOn)">
    <GetPublishingLocalizedString
       Condition="'$(IisUrl)'==''"
       ID="PublishLocalizedString_ErrorUseIisIsTrueButIisUrlIsEmpty"
       LogType="Error" />
    
    <Error Text="Target AddDeclareParametersItemsForIis6 failed"
          Condition="'$(IisUrl)'==''" />

    <!--Parameterize the default value of the Path  Note that IIS6 have to use the /lm/w3svc/1/xxxx-->
    <EscapeTextForRegularExpressions Text="$(_MSDeployIisUrlSource_MetakeyPath)">
      <Output TaskParameter="Result" PropertyName="_EscapeRegEx_MSDeployIisUrlSource_MetakeyPathSearchPath" />
    </EscapeTextForRegularExpressions>

    <ItemGroup
      Condition="!$(DisableAllVSGeneratedMSDeployParameter) And $(_DeploymentUseIis) And ($(LocalIisVersion) &lt; '7') ">
      <MsDeployDeclareParameters Include="$(DeployParameterIISAppName)" >
        <Kind>ProviderPath</Kind>
        <Scope>MetaKey</Scope>
        <Match>^$(_EscapeRegEx_MSDeployIisUrlSource_MetakeyPathSearchPath)$</Match>
        <Description>$(DeployParameterIISAppNameDescription)</Description>
        <DefaultValue>$(_MSDeployIisAppPathIIS6)</DefaultValue>
        <Value>$(_MSDeployIisAppPathIIS6)</Value>
        <Tags>IisApp</Tags>
        <Priority>$(VsIisAppParametersPriority)</Priority>
      </MsDeployDeclareParameters>

      <MsDeployDeclareParameters Include="$(DeployParameterIISAppPoolName)"
                              Condition="'$(IisServer_IisAppPoolName)' != '' And '$(ParameterApplicationPool)' == 'True' And '$(IIS6ParameterApplicationPool)' == 'True'" >
        <Kind>DeploymentObjectAttribute</Kind>
        <Scope>metaProperty</Scope>
        <Match>metaProperty[@name='AppPoolId' and @value='$(IisServer_IisAppPoolName)']/@value</Match>
        <Description>$(DeployParameterIISAppPoolNameDescription)</Description>
        <DefaultValue>$(_Destination_IisServer_IisAppPoolName)</DefaultValue>
        <Value>$(_Destination_IisServer_IisAppPoolName)</Value>
        <Tags></Tags>
        <Priority>$(VsIisAppParametersPriority)</Priority>
      </MsDeployDeclareParameters>


    </ItemGroup>

    <CallTarget Condition="'$(AfterAddDeclareParametersItemsForIis6)' != ''"
                Targets="$(AfterAddDeclareParametersItemsForIis6)"
                RunEachTargetSeparately="false" />
  </Target>


  <!--********************************************************************-->
  <!-- AddIisSettingAndFileContentsToSourceManifest  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <BeforeAddIisSettingAndFileContentsToSourceManifest Condition="'$(BeforeAddIisSettingAndFileContentsToSourceManifest)'==''">
    </BeforeAddIisSettingAndFileContentsToSourceManifest>
    <AfterAddIisSettingAndFileContentsToSourceManifest Condition="'$(AfterAddIisSettingAndFileContentsToSourceManifest)'==''">
    </AfterAddIisSettingAndFileContentsToSourceManifest>
    <AddIisSettingAndFileContentsToSourceManifestDependsOn Condition="'$(AddIisSettingAndFileContentsToSourceManifestDependsOn)' == ''">
      $(BeforeAddIisSettingAndFileContentsToSourceManifest);
      PipelineCopyAllFilesToOneFolderForMsdeploy;
      GenerateMsDeployManifestSettings;
      AddContentPathToSourceManifest;
    </AddIisSettingAndFileContentsToSourceManifestDependsOn>
  </PropertyGroup>
  <Target Name="AddIisSettingAndFileContentsToSourceManifest"
          DependsOnTargets="$(AddIisSettingAndFileContentsToSourceManifestDependsOn)">
    <!--Create the Source Manifest file-->
    <CallTarget Targets="$(AfterAddIisSettingAndFileContentsToSourceManifest)" RunEachTargetSeparately="false" Condition="'$(AfterAddIisSettingAndFileContentsToSourceManifest)' != ''" />
  </Target>

  <!--********************************************************************-->
  <!-- AddContentPathToSourceManifest  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <BeforeAddContentPathToSourceManifest Condition="'$(BeforeAddContentPathToSourceManifest)'==''">
    </BeforeAddContentPathToSourceManifest>
    <AfterAddContentPathToSourceManifest Condition="'$(AfterAddContentPathToSourceManifest)'==''">
    </AfterAddContentPathToSourceManifest>
    <AddContentPathToSourceManifestDependsOn Condition="'$(AddContentPathToSourceManifestDependsOn)' == ''">
      $(BeforeAddContentPathToSourceManifest);
      PipelineCopyAllFilesToOneFolderForMsdeploy;
      GenerateMsDeployManifestSettings;
      AddIis7ToSourceManifest;
      AddIis6ToSourceManifest
    </AddContentPathToSourceManifestDependsOn>
  </PropertyGroup>
  <Target Name="AddContentPathToSourceManifest"
          DependsOnTargets="$(AddContentPathToSourceManifestDependsOn)">
    <!-- Create the Source Manifest file -->
    <!--
    If indicated to mark the dest as an IIS app it will be created with iisApp, 
    otherwise contentPath will be used.
    -->

    <GetDeployManagedRuntimeVersion Condition="'$(DeployManagedRuntimeVersion)'=='' And $(_DeploymentAsIisApp) And !$(IgnoreDeployManagedRuntimeVersion)"
                                    TargetFrameworkVersion="$(DeployDefaultTargetFrameworkVersion)">
      <Output TaskParameter="Result" PropertyName="DeployManagedRuntimeVersion" />
      <Output TaskParameter="Version" PropertyName="DeployManagedRuntimeVersionNumeric" />
    </GetDeployManagedRuntimeVersion>

    <PropertyGroup Condition="!$(IgnoreDeployManagedRuntimeVersion)">
      <_DeployManagedRuntimeVersion Condition="'$(DeployManagedRuntimeVersionNumeric)' != '' And ($(DeployManagedRuntimeVersionNumeric) &gt;= '4.0') ">$(DeployManagedRuntimeVersion)</_DeployManagedRuntimeVersion>
      <!-- User explicit set the $(DeployManagedRuntimeVersion), we should honor it -->
      <_DeployManagedRuntimeVersion Condition="'$(DeployManagedRuntimeVersionNumeric)' == '' And '$(DeployManagedRuntimeVersion)'!=''">$(DeployManagedRuntimeVersion)</_DeployManagedRuntimeVersion>
    </PropertyGroup>

    <ItemGroup>
      <!-- For IIS App, below are default to the known value
      <DeployManagedRuntimeVersion Condition="'$(DeployManagedRuntimeVersion)'==''">$(DeployDefaultTargetFrameworkVersion)</DeployManagedRuntimeVersion>
      <DeployEnable32bitAppOnWin64 Condition="'$(DeployEnable32bitAppOnWin64)'==''"></DeployEnable32bitAppOnWin64>
      <DeployManagedPipelineMode Condition="'$(DeployManagedPipelineMode)'==''"></DeployManagedPipelineMode>
      -->
      <MsDeploySourceManifest Condition="$(_DeploymentAsIisApp)"
                              Include="IisApp">
        <Path>$(_MSDeployDirPath_FullPath)</Path>
        <managedRuntimeVersion>$(_DeployManagedRuntimeVersion)</managedRuntimeVersion>
        <enable32bitAppOnWin64>$(DeployEnable32bitAppOnWin64)</enable32bitAppOnWin64>
        <managedPipelineMode>$(DeployManagedPipelineMode)</managedPipelineMode>
        <AdditionalProviderSettings>managedRuntimeVersion;enable32bitAppOnWin64;managedPipelineMode</AdditionalProviderSettings>
      </MsDeploySourceManifest>

      <MsDeploySourceManifest Condition="!$(_DeploymentAsIisApp)"
                              Include="contentPath">
        <Path>$(_MSDeployDirPath_FullPath)</Path>
      </MsDeploySourceManifest>

      <!-- Make sure by default the Networkservice/AppPoolIdentity have read permission to the folder -->
      <MsDeploySourceManifest Include="setAcl"
                              Condition="$(_DeploymentAsIisApp) And $(IncludeSetAclProviderOnDestination)">
        <Path>$(_MSDeployDirPath_FullPath)</Path>
        <setAclResourceType>Directory</setAclResourceType>
        <AdditionalProviderSettings>setAclResourceType</AdditionalProviderSettings>
      </MsDeploySourceManifest>

      <!-- Make sure by default the anonymousAuthenticationUser have read permission to the folder so MIME type files will work -->
      <MsDeploySourceManifest Include="setAcl"
                              Condition="$(_DeploymentAsIisApp) And $(IncludeSetAclProviderOnDestination)">
        <Path>$(_MSDeployDirPath_FullPath)</Path>
        <setAclUser>anonymousAuthenticationUser</setAclUser>
        <setAclResourceType>Directory</setAclResourceType>
        <AdditionalProviderSettings>setAclUser;setAclResourceType</AdditionalProviderSettings>
      </MsDeploySourceManifest>

      <!-- Additionally if App_Data Need to be ReadWrite -->
      <MsDeploySourceManifest Include="setAcl"
                        Condition="$(_DeploymentAsIisApp) And $(IncludeSetAclProviderOnDestination) And $(MarkApp_DataWritableOnDestination) And Exists('$(_MSDeployDirPath_FullPath)\App_Data')">
        <Path>$(_MSDeployDirPath_FullPath)\App_Data</Path>
        <setAclAccess>Write</setAclAccess>
        <setAclResourceType>Directory</setAclResourceType>
        <AdditionalProviderSettings>setAclResourceType;setAclAccess</AdditionalProviderSettings>
      </MsDeploySourceManifest>
    </ItemGroup>

    <CallTarget Targets="$(AfterAddContentPathToSourceManifest)" RunEachTargetSeparately="false" Condition="'$(AfterAddContentPathToSourceManifest)' != ''" />
  </Target>


  <!--********************************************************************-->
  <!-- AddIisAndContentDeclareParametersItems  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <BeforeAddIisAndContentDeclareParametersItems Condition="'$(BeforeAddIisAndContentDeclareParametersItems)'==''">
    </BeforeAddIisAndContentDeclareParametersItems>
    <AfterAddIisAndContentDeclareParametersItems Condition="'$(AfterAddIisAndContentDeclareParametersItems)'==''">
    </AfterAddIisAndContentDeclareParametersItems>
    <AddIisAndContentDeclareParametersItemsDependsOn Condition="'$(AddIisAndContentDeclareParametersItemsDependsOn)' == ''">
      $(BeforeAddIisAndContentDeclareParametersItems);
      PipelineCopyAllFilesToOneFolderForMsdeploy;
      GenerateMsDeployManifestSettings;
      AddDeclareParametersItemsForContentPath;
    </AddIisAndContentDeclareParametersItemsDependsOn>
  </PropertyGroup>
  <Target Name="AddIisAndContentDeclareParametersItems"
          DependsOnTargets="$(AddIisAndContentDeclareParametersItemsDependsOn)">
    <CallTarget Targets="$(AfterAddIisAndContentDeclareParametersItems)" RunEachTargetSeparately="false" Condition="'$(AfterAddIisAndContentDeclareParametersItems)' != ''" />
  </Target>


  <!--********************************************************************-->
  <!-- AddDeclareParametersItemsForContentPath  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <BeforeAddDeclareParametersItemsForContentPath Condition="'$(BeforeAddDeclareParametersItemsForContentPath)'==''">
    </BeforeAddDeclareParametersItemsForContentPath>
    <AfterAddDeclareParametersItemsForContentPath Condition="'$(AfterAddDeclareParametersItemsForContentPath)'==''">
    </AfterAddDeclareParametersItemsForContentPath>
    <AddDeclareParametersItemsForContentPathDependsOn Condition="'$(AddDeclareParametersItemsForContentPathDependsOn)' == ''">
      $(BeforeAddDeclareParametersItemsForContentPath);
      PipelineCopyAllFilesToOneFolderForMsdeploy;
      GenerateMsDeployManifestSettings;
      AddDeclareParametersItemsForIis7;
      AddDeclareParametersItemsForIis6;
    </AddDeclareParametersItemsForContentPathDependsOn>
  </PropertyGroup>
  <Target Name="AddDeclareParametersItemsForContentPath"
          DependsOnTargets="$(AddDeclareParametersItemsForContentPathDependsOn)">
    <!--  Now we create the destination manifest file -->

    <PropertyGroup>
      <!-- default fall back to physical path-->
      <_DestinationContentPath>$(_DestinationIisAppPhysicalPath)</_DestinationContentPath>
      <!-- For iis6 scenario-->
      <_DestinationContentPath Condition="($(DestinationUseIis) or $(_DeploymentAsIisApp)) And ($(DestinationIisVersion) &lt; '7') And ('$(_MSDeployIisAppPathIIS6)'!= '') " >$(_MSDeployIisAppPathIIS6)</_DestinationContentPath>
      <!-- For iis7 and above scenario-->
      <_DestinationContentPath Condition="($(DestinationUseIis) or $(_DeploymentAsIisApp)) And ($(DestinationIisVersion) &gt;= '7') And ('$(_MSDeployIisAppPathIIS7)' !='')">$(_MSDeployIisAppPathIIS7)</_DestinationContentPath>
      <!-- default ParameterName for contentPath, by default they are the same as ApphostConfig/MetaKey so they can reuse the parameter-->
      <_MsDeployParameterNameForContentPath>$(DeployParameterIISAppName)</_MsDeployParameterNameForContentPath>
      <_MsDeployParameterNameForContentPath Condition="$(_DeploymentUseIis) And (!$(DestinationUseIis))">$(DeployParameterIISAppPhysicalPath)</_MsDeployParameterNameForContentPath>
      <_MsDeployParameterTagForContentPath>PhysicalPath</_MsDeployParameterTagForContentPath>
      <_MsDeployParameterTagForContentPath  Condition="$(_DeploymentAsIisApp)">IisApp</_MsDeployParameterTagForContentPath>
      <_MsDeployParameterDefaultValueForContentPath>$(_DestinationContentPath)</_MsDeployParameterDefaultValueForContentPath>
      <_MsDeployParameterExcludeSetParameterForContentPath>False</_MsDeployParameterExcludeSetParameterForContentPath>
      <_MsDeployParameterPhysicalFolderForSetAcl>{$(_MsDeployParameterNameForContentPath)}</_MsDeployParameterPhysicalFolderForSetAcl>
      <_MsDeployParameterPhysicalFolderAppDataForSetAcl>{$(_MsDeployParameterNameForContentPath)}/App_Data</_MsDeployParameterPhysicalFolderAppDataForSetAcl>
    
  </PropertyGroup>

    <GetPublishingLocalizedString
       Condition="$(DestinationUseIis) 
             And ((($(DestinationIisVersion) &lt; '7') And ('$(_MSDeployIisAppPathIIS6)'== ''))
                   Or ( ($(DestinationIisVersion) &gt;= '7') And ('$(_MSDeployIisAppPathIIS7)' ==''))) "
       ID="PublishLocalizedString_ErrorInvalidPropertyWhenSettingPropertyIsTrue"
       ArgumentCount="2"
       Arguments="DeployIisAppPath;DestinationUseIIS"
       LogType="Error" />
    
    <Error  Text ="Target AddDeclareParametersItemsForContentPath Failed"
            Condition="$(DestinationUseIis) 
             And ((($(DestinationIisVersion) &lt; '7') And ('$(_MSDeployIisAppPathIIS6)'== ''))
                   Or ( ($(DestinationIisVersion) &gt;= '7') And ('$(_MSDeployIisAppPathIIS7)' ==''))) "
                   />
    <!--
    If indicated to mark the dest as an IIS app it will be created with iisApp, 
    otherwise contentPath will be used.
    -->

    <!--Parameterize the default value of the Path-->
    <EscapeTextForRegularExpressions Text="$(_MSDeployDirPath_FullPath)">
      <Output TaskParameter="Result" PropertyName="_EscapeRegEx_MSDeployDirPath" />
    </EscapeTextForRegularExpressions>


    <ItemGroup 
      Condition="!$(DisableAllVSGeneratedMSDeployParameter) And '$(_DestinationContentPath)' != ''">
      <MsDeployDeclareParameters Include="$(_MsDeployParameterNameForContentPath)"
                                 Condition="$(_DeploymentAsIisApp)">
        <Kind>ProviderPath</Kind>
        <Scope>IisApp</Scope>
        <Match>^$(_EscapeRegEx_MSDeployDirPath)$</Match>
        <Description>$(DeployParameterIISAppNameDescription)</Description>
        <DefaultValue>$(_MsDeployParameterDefaultValueForContentPath)</DefaultValue>
        <Value>$(_DestinationContentPath)</Value>
        <Tags>$(_MsDeployParameterTagForContentPath)</Tags>
        <Priority>$(VsIisAppParametersPriority)</Priority>
        <ExcludeFromSetParameter>$(_MsDeployParameterExcludeSetParameterForContentPath)</ExcludeFromSetParameter>
      </MsDeployDeclareParameters>
    
      <MsDeployDeclareParameters Include="$(_MsDeployParameterNameForContentPath)"
                                 Condition="!$(_DeploymentAsIisApp)">
        <Kind>ProviderPath</Kind>
        <Scope>contentPath</Scope>
        <Match>^$(_EscapeRegEx_MSDeployDirPath)$</Match>
        <Description>$(DeployParameterIISAppPhysicalPathDescription)</Description>
        <DefaultValue>$(_MsDeployParameterDefaultValueForContentPath)</DefaultValue>
        <Value>$(_DestinationContentPath)</Value>
        <Tags>$(_MsDeployParameterTagForContentPath)</Tags>
        <Priority>$(VsContentPathParametersPriority)</Priority>
        <ExcludeFromSetParameter>$(_MsDeployParameterExcludeSetParameterForContentPath)</ExcludeFromSetParameter>
      </MsDeployDeclareParameters>
    </ItemGroup>


    <!-- SetAcl Provider-->
    <ItemGroup Condition="!$(DisableAllVSGeneratedMSDeployParameter) and '$(_DestinationContentPath)' != '' and $(IncludeSetAclProviderOnDestination)" >
      <MsDeployDeclareParameters Include="$(_MsDeployParameterNameForContentPath)">
        <Kind>ProviderPath</Kind>
        <Scope>setAcl</Scope>
        <Match>^$(_EscapeRegEx_MSDeployDirPath)$</Match>
        <Description>$(DeployParameterIISAppPhysicalPathDescription)</Description>
        <DefaultValue>$(_MsDeployParameterDefaultValueForContentPath)</DefaultValue>
        <Value>$(_DestinationContentPath)</Value>
        <Tags></Tags>
        <ExcludeFromSetParameter>$(_MsDeployParameterExcludeSetParameterForContentPath)</ExcludeFromSetParameter>
        <Priority>$(VsSetAclPriority)</Priority>
      </MsDeployDeclareParameters>
       <!-- Optionally mark write permission for the AppData Folder-->
       <MsDeployDeclareParameters Include="$(DeployParameterApp_DataWritePermission)"
                                 Condition="$(MarkApp_DataWritableOnDestination) and Exists('$(_MSDeployDirPath_FullPath)\App_Data')">
        <Kind>ProviderPath</Kind>
        <Scope>setAcl</Scope>
        <Match>^$(_EscapeRegEx_MSDeployDirPath)\\App_Data$</Match>
        <Description>Add write permission to App_Data folder</Description>
        <DefaultValue>{$(_MsDeployParameterNameForContentPath)}/App_Data</DefaultValue>
        <Value>$(_DestinationContentPath)/App_Data</Value>
        <Tags>Hidden</Tags>
        <Priority>$(VsSetAclPriority)</Priority>
        <ExcludeFromSetParameter>True</ExcludeFromSetParameter>
      </MsDeployDeclareParameters>
    </ItemGroup>


    <!-- We need this value to generate skip rule to skip App_Data folder-->
    <CreateProperty Value="$(_DestinationContentPath)">
      <Output TaskParameter="Value" PropertyName="DestinationAppRoot"/>
    </CreateProperty>
    <CallTarget Targets="$(AfterAddDeclareParametersItemsForContentPath)" RunEachTargetSeparately="false" Condition="'$(AfterAddDeclareParametersItemsForContentPath)' != ''" />
  </Target>

  
  <!--********************************************************************-->
  <!-- WriteItemsToSourceManifest  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <BeforeWriteItemsToSourceManifest Condition="'$(BeforeWriteItemsToSourceManifest)'==''">
    </BeforeWriteItemsToSourceManifest>
    <AfterWriteItemsToSourceManifest Condition="'$(AfterWriteItemsToSourceManifest)'==''">
    </AfterWriteItemsToSourceManifest>
    <WriteItemsToSourceManifestDependsOn>
      $(BeforeWriteItemsToSourceManifest);
      $(WriteItemsToSourceManifestDependsOn);
      AddIisSettingAndFileContentsToSourceManifest;
    </WriteItemsToSourceManifestDependsOn>
  </PropertyGroup>
  <Target Name="WriteItemsToSourceManifest"
          DependsOnTargets="$(WriteItemsToSourceManifestDependsOn)">

    <ExportManifestFile Manifests="@(MsDeploySourceManifest)" ManifestFile="$(PackageSourceManifest)" />
    <ItemGroup>
      <FileWrites Include="$(PackageSourceManifest)" />
    </ItemGroup>

    <CallTarget Targets="$(AfterWriteItemsToSourceManifest)" RunEachTargetSeparately="false" Condition="'$(AfterWriteItemsToSourceManifest)' != ''" />
  </Target>


  <!--********************************************************************-->
  <!-- AddDeclareParametersItems  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <BeforeAddDeclareParametersItems Condition="'$(BeforeAddDeclareParametersItems)'==''">
    </BeforeAddDeclareParametersItems>
    <AfterAddDeclareParametersItems Condition="'$(AfterAddDeclareParametersItems)'==''">
    </AfterAddDeclareParametersItems>
    <AddDeclareParametersItemsDependsOn Condition="'$(AddDeclareParametersItemsDependsOn)' == ''">
      $(BeforeAddDeclareParametersItems);
      AddIisAndContentDeclareParametersItems;
      AddDeclareParametersItemsForDatabaseScript;
    </AddDeclareParametersItemsDependsOn>
  </PropertyGroup>
  <Target Name="AddDeclareParametersItems"
          DependsOnTargets="$(AddDeclareParametersItemsDependsOn)">
    <PropertyGroup>
      <_PublishDefaultVDirectoryPath>$(RemoteSitePhysicalPath)</_PublishDefaultVDirectoryPath>
      <_PublishDefaultVDirectoryPath Condition="'$(_PublishDefaultVDirectoryPath)'==''">$(_DestinationIisAppPhysicalPath)</_PublishDefaultVDirectoryPath>
    </PropertyGroup>

    <!-- Create Virtual Directory Parameter if needed-->
    <GetPublishingLocalizedString
       Condition="'$(_DestinationIisAppPhysicalPath)' == '' And $(_DeploymentUseIis)"
       ID="PublishLocalizedString_ErrorInvalidPropertyWhenSettingPropertyIsTrue"
       ArgumentCount="2"
       Arguments="_DestinationIisAppPhysicalPath;_DeploymentUseIis"
       LogType="Error" />

    <Error  Text ="Target AddDeclareParametersItems Failed"
             Condition="'$(_DestinationIisAppPhysicalPath)' == '' And $(_DeploymentUseIis)"
                   />
    <ItemGroup>
      <_MSBuildReplaceRuleItemMSBuildProjectDirectory Include="$(WebPublishPipelineProjectDirectory)" />
    </ItemGroup>

    <EscapeTextForRegularExpressions Text="@(_MSBuildReplaceRuleItemMSBuildProjectDirectory->'%(FullPath)')">
      <Output TaskParameter="Result" PropertyName="_EscapeRegExMSBuildProjectDirectory" />
    </EscapeTextForRegularExpressions>

    <ItemGroup>
      <MsDeployDeclareParameters  Include="$(DeployParameterPrefix)IisVirtualDirectoryPhysicalPath"
                                  Condition="!$(DisableAllVSGeneratedMSDeployParameter) And $(_DeploymentUseIis)" >
        <Kind>DestinationVirtualDirectory</Kind>
        <Scope>.*</Scope>
        <Match>^$(_EscapeRegExMSBuildProjectDirectory)$</Match>
        <Description>$(DeployParameterIISAppPhysicalPathDescription)</Description>
        <DefaultValue>$(_DestinationIisAppPhysicalPath)</DefaultValue>
        <!--Value is used in the SetParameters when directly publish-->
        <Value>$(_PublishDefaultVDirectoryPath)</Value>
        <Tags>PhysicalPath</Tags>
        <Priority>$(VsDestinationVDirParametersPriority)</Priority>
      </MsDeployDeclareParameters>
    </ItemGroup>

    <CallTarget Targets="$(AfterAddDeclareParametersItems)" RunEachTargetSeparately="false" Condition="'$(AfterAddDeclareParametersItems)' != ''" />
  </Target>


  <!--********************************************************************-->
  <!-- _GatherMSDeployParams  -->
  <!-- Internal target used to resolve the msdeploy parameters and run config transformations -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <_GatherParamsDependsOn>
      $(_GatherParamsDependsOn);
      GetProjectWebProperties;
      ImportParametersFiles;
      TransformWebConfig;
      ProfileTransformWebConfig;
      AutoParameterizationWebConfigConnectionStrings;
      AddDeclareParametersItems;
      ImportPublishingParameterValues;
      <!-- We don't need IIS parameters
      AddDeclareParametersItemsForIis7;
      AddDeclareParametersItemsForIis6;
      AddDeclareParametersItemsForContentPath;-->
    </_GatherParamsDependsOn>
  </PropertyGroup>
  
  <Target Name="_GatherMSDeployParams" DependsOnTargets="$(_GatherParamsDependsOn)" 
          Outputs="$(_PackageTempDir)\web.config;$(PublishParametersFile)">

    <ExportParametersFile
      Parameters="@(MsDeployDeclareParameters)"
      DeclareSetParameterFile="$(PublishParametersFile)"
      IncludeDefaultValue="True"
      GenerateFileEvenIfEmpty="True"
    />

  </Target>

  <!--********************************************************************-->
  <!-- GenerateMsDeployManifestSettings  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <GenerateMsDeployManifestSettingsDependsOn Condition="'$(GenerateMsDeployManifestSettingsDependsOn)'==''">
    </GenerateMsDeployManifestSettingsDependsOn>
  </PropertyGroup>

  <Target Name="GenerateMsDeployManifestSettings" DependsOnTargets="$(GenerateMsDeployManifestSettingsDependsOn)">
    <!-- we can only set this property after we get the existing IisUrl -->
    <ItemGroup>
      <_MSDeployDirPath Include="$(WPPAllFilesInSingleFolder)" />
    </ItemGroup>

    <GetPublishingLocalizedString
      Condition="'$(DeployParameterIISAppNameDescription)'=='' And !$(DeployParameterAutoDescriptionbyTags)"
      ID="PublishLocalizedString_IISAppParameterDescription">
      <Output TaskParameter="Result" PropertyName="DeployParameterIISAppNameDescription" />
    </GetPublishingLocalizedString>

    <!--if $(DeployParameterAutoDescriptionbyTags), we turn off the description.-->
    <DeployParameterIISAppNameDescription Condition="'$(DeployParameterIISAppNameDescription)'!='' And $(DeployParameterAutoDescriptionbyTags)"></DeployParameterIISAppNameDescription>

    <!--Get localized $(DeployParameterIISAppPhysicalPathDescription) from the Task.dll if it wasn't bee set by user.-->
    <GetPublishingLocalizedString
          Condition="'$(DeployParameterIISAppPhysicalPathDescription)'=='' And !$(DeployParameterAutoDescriptionbyTags)"
          ID="PublishLocalizedString_IISAppPhysicalPathDescription">
      <Output TaskParameter="Result" PropertyName="DeployParameterIISAppPhysicalPathDescription" />
    </GetPublishingLocalizedString>

    <!--if $(DeployParameterAutoDescriptionbyTags), we turn off the description.-->
    <DeployParameterIISAppPhysicalPathDescription Condition="'$(DeployParameterIISAppPhysicalPathDescription)'!='' And $(DeployParameterAutoDescriptionbyTags)"></DeployParameterIISAppPhysicalPathDescription>

    <PropertyGroup>
      <_MSDeployDirPath_FullPath>@(_MSDeployDirPath->'%(FullPath)')</_MSDeployDirPath_FullPath>
      <_MSDeployIisAppPathIIS7>$(DeployIisAppPath)</_MSDeployIisAppPathIIS7>
      <_MSDeployIisAppPathIIS6>$(DeployIisAppPath)</_MSDeployIisAppPathIIS6>
      <_MSDeployIisAppPathIIS7 Condition="'$(_MSDeployIisAppPathIIS7)'==''">$(DefaultDeployIisAppPath)</_MSDeployIisAppPathIIS7>
      <_MSDeployIisAppPathIIS6 Condition="'$(_MSDeployIisAppPathIIS6)'==''">$(DefaultDeployIisAppPath)</_MSDeployIisAppPathIIS6>
    </PropertyGroup>

    <ItemGroup>
      <_MSDeploySourceManifest Include="$(PackageSourceManifest)" />
      <_MSDeployParametersFile Include="$(PackageParametersFile)" />
      <_MSDeploySampleParametersValue Condition="$(GenerateSampleDeployScript)" Include="$(GenerateSampleParametersValueLocation)" />
      <_MSDeployGenerateSampleDeployScriptReadMeLocation Condition="$(GenerateSampleDeployScript)" Include="$(GenerateSampleDeployScriptReadMeLocation)" />
    </ItemGroup>
  </Target>

  <!-- CleanWebsitesPackageCore  -->
  <PropertyGroup>
    <CleanWebsitesPackageCoreDependsOn Condition="'$(CleanWebsitesPackageCoreDependsOn)' == ''">
      GenerateMsDeployManifestSettings;
    </CleanWebsitesPackageCoreDependsOn>
  </PropertyGroup>
  <Target Name="CleanWebsitesPackageCore" DependsOnTargets="$(CleanWebsitesPackageCoreDependsOn)">
    <!--Assertion check-->
    <CheckItemsCount Items="@(_MSDeployPackageLocation)" Condition="$(EnablePackageProcessLoggingAndAssert)">
      <Output TaskParameter="Count" PropertyName="_InternalItemsCount" />
    </CheckItemsCount>

    <GetPublishingLocalizedString
       Condition="$(EnablePackageProcessLoggingAndAssert) AND '$(_InternalItemsCount)' != '1'"
       ID="PublishLocalizedString_ErrorInvalidMSBuildItemCollectionCount"
       ArgumentCount="3"
       Arguments="_MSDeployPackageLocation;$(_InternalItemsCount);1"
       LogType="Error" />

    <Error  Text ="Target CleanWebsitesPackageCore Failed"
             Condition="$(EnablePackageProcessLoggingAndAssert) AND '$(_InternalItemsCount)' != '1'" />

    <CheckPathAttributes Path="@(_MSDeployPackageLocation->'%(Identity)')">
      <Output TaskParameter="IsExists" PropertyName="_CheckPackageLocationExists" />
      <Output TaskParameter="IsFolder" PropertyName="_CheckPackageLocationIsFolder" />
    </CheckPathAttributes>

    <RemoveDir Directories="@(_MSDeployPackageLocation)"
               Condition="$(_CheckPackageLocationExists) AND $(_CheckPackageLocationIsFolder)"
               ContinueOnError="True"/>

    <Delete Files="@(_MSDeployPackageLocation)" 
               Condition="$(_CheckPackageLocationExists) AND !$(_CheckPackageLocationIsFolder)" 
               TreatErrorsAsWarnings="True"  />
  </Target>

  <!--********************************************************************-->
  <!-- CleanWebsitesPackage  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <CleanWebsitesPackageDependsOn Condition="'$(CleanWebsitesPackageDependsOn)' == ''">
      GenerateMsDeployManifestSettings;
      CleanWebsitesPackageCore;
    </CleanWebsitesPackageDependsOn>
  </PropertyGroup>
  <Target Name="CleanWebsitesPackage" DependsOnTargets="$(CleanWebsitesPackageDependsOn)">
    
    <!--<RemoveDir Condition="Exists($(PackageLogDir))" Directories="$(PackageLogDir)" ContinueOnError="true" />-->
    <Delete Files="$(PackageSourceManifest)" Condition="Exists('$(PackageSourceManifest)')" TreatErrorsAsWarnings="True" />
    <Delete Files="$(PublishParametersFile)" Condition="Exists('$(PublishParametersFile)')" TreatErrorsAsWarnings="True" />
    <Delete Files="$(GenerateSampleDeployScriptLocation)" Condition="Exists('$(GenerateSampleDeployScriptLocation)')" TreatErrorsAsWarnings="True" />
    <Delete Files="$(GenerateSampleDeployScriptReadMeLocation)" Condition="Exists('$(GenerateSampleDeployScriptReadMeLocation)')" TreatErrorsAsWarnings="True" />
    <Delete Files="$(GenerateSampleParametersValueLocation)" Condition="Exists('$(GenerateSampleParametersValueLocation)')" TreatErrorsAsWarnings="True" />
    <Delete Files="$(GenerateSampleParametersValueLocation)" Condition="Exists('$(GenerateSampleParametersValueLocation)')" TreatErrorsAsWarnings="True" />
  </Target>


  <!--********************************************************************-->
  <!-- CleanWebsitesWPPAllFilesInSingleFolder  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <CleanWebsitesWPPAllFilesInSingleFolderDependsOn Condition="'$(CleanWebsitesWPPAllFilesInSingleFolderDependsOn)' == ''">
    </CleanWebsitesWPPAllFilesInSingleFolderDependsOn>
  </PropertyGroup>
  <Target Name="CleanWebsitesWPPAllFilesInSingleFolder" DependsOnTargets="$(CleanWebsitesWPPAllFilesInSingleFolderDependsOn)">
    <!-- Assertion check-->
    <!-- In the case of Clean Packaging/Publish, we simply delete the WPPAllFilesInSingleFolder -->
    <RemoveDir Condition="Exists($(WPPAllFilesInSingleFolder))" Directories="$(WPPAllFilesInSingleFolder)" ContinueOnError="true" />
  </Target>


  <!--********************************************************************-->
  <!-- CleanWebPublishPipelineIntermediateOutput  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <CleanWebPublishPipelineIntermediateOutputDependsOn Condition="'$(CleanWebPublishPipelineIntermediateOutputDependsOn)' == ''">
    </CleanWebPublishPipelineIntermediateOutputDependsOn>
  </PropertyGroup>
  <Target Name="CleanWebPublishPipelineIntermediateOutput" DependsOnTargets="$(CleanWebPublishPipelineIntermediateOutputDependsOn)">
    <RemoveDir Condition="Exists($(AutoParameterizationWebConfigConnectionStringsLocation))" Directories="$(AutoParameterizationWebConfigConnectionStringsLocation)" ContinueOnError="true" />
    <RemoveDir Condition="Exists($(TransformWebConfigIntermediateLocation))" Directories="$(TransformWebConfigIntermediateLocation)" ContinueOnError="true" />
    <RemoveDir Condition="Exists($(ProfileTransformWebConfigIntermediateLocation))" Directories="$(ProfileTransformWebConfigIntermediateLocation)" ContinueOnError="true" />
    <RemoveDir Condition="Exists($(DatabaseDeployIntermediateOutputPath))" Directories="$(DatabaseDeployIntermediateOutputPath)" ContinueOnError="true" />
    <RemoveDir Condition="Exists($(InsertAdditionalWebCofigConnectionStringsLocation))" Directories="$(InsertAdditionalWebCofigConnectionStringsLocation)" ContinueOnError="true" />
    <Delete Condition="Exists($(_WebConfigTransformOutputParametersFile))" Files="$(_WebConfigTransformOutputParametersFile)" TreatErrorsAsWarnings="true" />
    <Delete Condition="Exists($(_WebConfigsToAutoParmeterizeCsTransformOutputParametersFile))" Files="$(_WebConfigsToAutoParmeterizeCsTransformOutputParametersFile)" TreatErrorsAsWarnings="true" />
    <Delete Condition="Exists($(_ProfileWebConfigTransformOutputParametersFile))" Files="$(_ProfileWebConfigTransformOutputParametersFile)" TreatErrorsAsWarnings="true" />
    <Delete Condition="Exists($(_WPPLastBuildInfoLocation))" Files="$(_WPPLastBuildInfoLocation)" TreatErrorsAsWarnings="true" />
  </Target>


  <!--********************************************************************-->
  <!-- CheckAndCleanMSDeployPackageIfNeeded  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <CheckAndCleanMSDeployPackageIfNeededDependsOn Condition="'$(CheckAndCleanMSDeployPackageIfNeededDependsOn)'==''">
      GenerateMsDeployManifestSettings;
    </CheckAndCleanMSDeployPackageIfNeededDependsOn>
  </PropertyGroup>
  <Target Name="CheckAndCleanMSDeployPackageIfNeeded" >
    <IsCleanMSDeployPackageNeeded
      MSDeployVersionsToTry="$(_MSDeployVersionsToTry)"
      PackageLocation="@(_MSDeployPackageLocation->'%(FullPath)')"
      PackageAsSingleFile="$(PackageAsSingleFile)"
      CurrentSourceManifestPath="@(_MSDeploySourceManifest->'%(FullPath)')"  >
      <Output TaskParameter="Result"
              PropertyName="_IsCleanMSDeployPackageNeeded" />
    </IsCleanMSDeployPackageNeeded>
    
    <!-- Only delete the Zip file -->
    <CallTarget Condition="$(_IsCleanMSDeployPackageNeeded)"
                Targets="CleanWebsitesPackageCore"  
                RunEachTargetSeparately="false" />
  </Target>


  <!--********************************************************************-->
  <!-- GenerateMsdeployManifestFiles  -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <OnAfterGenerateMsdeployManifestFiles>
      $(OnAfterGenerateMsdeployManifestFiles);
      AddDeclareParametersItems;
      CheckAndCleanMSDeployPackageIfNeeded;
    </OnAfterGenerateMsdeployManifestFiles>
    <GenerateMsdeployManifestFilesDependsOn>
      $(GenerateMsdeployManifestFilesDependsOn);
      PipelineCopyAllFilesToOneFolderForMsdeploy;
      GenerateMsDeployManifestSettings;
    </GenerateMsdeployManifestFilesDependsOn>
  </PropertyGroup>
  <Target Name="GenerateMsdeployManifestFiles" DependsOnTargets="$(GenerateMsdeployManifestFilesDependsOn)">

    <GetPublishingLocalizedString
       ID="PublishLocalizedString_GenerateMsdeploySourceManifestFile"
       LogType="Message" />

    <MakeDir Directories="@(_MSDeploySourceManifest->'%(RootDir)%(Directory)')" Condition="!Exists(@(_MSDeploySourceManifest->'%(RootDir)%(Directory)'))"/>
    <MakeDir Directories="@(_MSDeployParametersFile->'%(RootDir)%(Directory)')" Condition="!Exists(@(_MSDeployParametersFile->'%(RootDir)%(Directory)'))"/>

    <!--================================================-->
    <!--Create the Source Manifest filePath list-->
    <!--================================================-->
    <CallTarget Targets="WriteItemsToSourceManifest" RunEachTargetSeparately="false" />
    <CallTarget Targets="$(OnAfterGenerateMsdeployManifestFiles)" RunEachTargetSeparately="false" />
  </Target>

  <!--================================================-->
  <!--Create skip rules used in sample script generation as well as publish-->
  <!--================================================-->
  <Target Name="GenerateSkipRuleForAppData" Condition="$(SkipApp_DataFolder)">
    <EscapeTextForRegularExpressions Text="$(WPPAllFilesInSingleFolder)">
      <Output TaskParameter="Result" PropertyName="_Escaped_WPPAllFilesInSingleFolder" />
    </EscapeTextForRegularExpressions>
    <EscapeTextForRegularExpressions Text="$(_DestinationContentPath)">
      <Output TaskParameter="Result" PropertyName="_Escaped_DestinationContentPath" />
    </EscapeTextForRegularExpressions>
    <ItemGroup>
      <MsDeploySkipRules Include="SkipAppDataOnDeploy">
        <SkipAction></SkipAction>
        <ObjectName>dirPath</ObjectName>
        <AbsolutePath>$(_Escaped_WPPAllFilesInSingleFolder)\\App_Data$</AbsolutePath>
        <Apply>Source</Apply>
        <XPath></XPath>
      </MsDeploySkipRules>
      <MsDeploySkipRules Include="SkipAppDataOnDeploy">
        <SkipAction></SkipAction>
        <ObjectName>dirPath</ObjectName>
        <AbsolutePath>$(_Escaped_DestinationContentPath)\\App_Data$</AbsolutePath>
        <Apply>Destination</Apply>
        <XPath></XPath>
      </MsDeploySkipRules>

    </ItemGroup>
  </Target>

  <!--********************************************************************-->
  <!-- PackageUsingManifest Manifest -->
  <!-- ********************************************************************-->
  <PropertyGroup>
    <PackageUsingManifestDependsOn>
      $(OnBeforePackageUsingManifest);
      $(PackageUsingManifestDependsOn);
      GetMSDeployInstalledVersionPath;
      GenerateMsDeployManifestSettings;
      GenerateMsdeployManifestFiles;
      GenerateSkipRuleForAppData;
    </PackageUsingManifestDependsOn>

  </PropertyGroup>
  <Target Name="PackageUsingManifest" DependsOnTargets="$(PackageUsingManifestDependsOn)">

    <PropertyGroup>
      <!-- Always package from a manifest -->
      <PackageSourceType>manifest</PackageSourceType>
      <PackageSourceRoot>@(_MSDeploySourceManifest->'%(FullPath)')</PackageSourceRoot>
      <!-- target is either a single file or archive dir -->
      <PackageDestinationType>archiveDir</PackageDestinationType>
      <PackageDestinationRoot>@(_MSDeployArchiveDir->'%(FullPath)')</PackageDestinationRoot>
      <PackageDestinationType Condition="$(PackageAsSingleFile)">package</PackageDestinationType>
      <PackageDestinationRoot Condition="$(PackageAsSingleFile)">@(_MSDeployPackageFile->'%(FullPath)')</PackageDestinationRoot>
      <PackageResult>True</PackageResult>
    </PropertyGroup>

    <GetPublishingLocalizedString
       Importance="High"
       ID="PublishLocalizedString_PackagingIntoLocation"
       ArgumentCount="1"
       Arguments="$(PackageDestinationRoot)"
       LogType="Message" />
    <!-- "Packaging into $(PackageDestinationRoot)" -->

    <ItemGroup>
      <MsDeploySourceProviderSetting Remove="@(MsDeploySourceProviderSetting)" />
      <MsDeploySourceProviderSetting Include="$(PackageSourceType)">
        <Path>$(PackageSourceRoot)</Path>
        <EncryptPassword>$(DeployEncryptKey)</EncryptPassword>
        <WebServerAppHostConfigDirectory>$(IisServer_WebServerAppHostConfigDirectory)</WebServerAppHostConfigDirectory>
        <WebServerManifest>$(IisServer_WebServerManifest)</WebServerManifest>
        <WebServerDirectory>$(IisServer_WebServerDirectory)</WebServerDirectory>
    </MsDeploySourceProviderSetting>
      <MsDeployDestinationProviderSetting Remove="@(MsDeployDestinationProviderSetting)" />
      <MsDeployDestinationProviderSetting Include="$(PackageDestinationType)">
        <Path>$(PackageDestinationRoot)</Path>
        <EncryptPassword>$(DeployEncryptKey)</EncryptPassword>
        <IncludeAcls>False</IncludeAcls>
      </MsDeployDestinationProviderSetting>
    </ItemGroup>

    <!--Debugging helper don't need to be localized-->
    <Message Text="PackageUsingManifest MsDeploySourceProviderSetting is @(MsDeploySourceProviderSetting)" Condition="$(EnablePackageProcessLoggingAndAssert)" />
    <Message Text="PackageUsingManifest MsDeployDestinationProviderSetting is @(MsDeployDestinationProviderSetting)" Condition="$(EnablePackageProcessLoggingAndAssert)"/>

    <ExportParametersFile
      Condition="!$(UseDeclareParametersXMLInMsDeploy) And $(EnablePackageProcessLoggingAndAssert)"
      Parameters="@(MsDeployDeclareParameters)"
      DeclareParameterFile="$(PackageLogDir)\PackageUsingManifest.parameters.xml"
      GenerateFileEvenIfEmpty="True"
      />

    <!-- First delete the ParameterFile-->
    <Delete Files="$(PackageParametersFile)"  Condition="Exists($(PackageParametersFile))"  ContinueOnError="true"/>

    <ExportParametersFile
      Condition="$(UseDeclareParametersXMLInMsDeploy)"
      Parameters="@(MsDeployDeclareParameters)"
      DeclareParameterFile="$(PackageParametersFile)"
      GenerateFileEvenIfEmpty="True"
      />

    <PropertyGroup>
      <_VsPackageParametersFile></_VsPackageParametersFile>
      <_VsPackageParametersFile Condition="$(UseDeclareParametersXMLInMsDeploy)">$(PackageParametersFile)</_VsPackageParametersFile>
    </PropertyGroup>
    <ItemGroup>
      <!-- We will include the parameter when $(UseDeclareParametersXMLInMsDeploy) is false-->
      <_Package_MsDeployDeclareParameters Include="@(MsDeployDeclareParameters)" Condition="!$(UseDeclareParametersXMLInMsDeploy)" />
    </ItemGroup>
   

    <VSMSDeploy Condition="!$(UseMsdeployExe)"
       MSDeployVersionsToTry="$(_MSDeployVersionsToTry)"
       Source="@(MsDeploySourceProviderSetting)"
       Destination="@(MsDeployDestinationProviderSetting)"
       DeploymentTraceLevel="$(PackageTraceLevel)"
       DisableLink="$(PackageDisableLinks)"
       EnableLink="$(PackageEnableLinks)"
       DeclareParameterItems="@(_Package_MsDeployDeclareParameters)"
       OptimisticParameterDefaultValue="$(EnableOptimisticParameterDefaultValue)"
       ImportDeclareParametersItems="$(_VsPackageParametersFile)"
       ReplaceRuleItems="@(MsDeployReplaceRules)"
       RetryAttempts="$(RetryAttemptsForDeployment)">
      <Output TaskParameter="Result" PropertyName="PackageResult" />
    </VSMSDeploy>
    <!--"msdeploy.exe" 
    -verb:sync 
    -source:manifest=obj\Debug\Package\SourceManifest.xml 
    -Destination:archiveDir=c:\test\archiveDir 
    -disableLink:"ContentExtension,CertificateExtension"
    -replace:-->
    <MSdeploy
      Condition="$(UseMsdeployExe)"
      Verb="sync"
      Source="@(MsDeploySourceProviderSetting)"
      Destination="@(MsDeployDestinationProviderSetting)"
      DisableLink="$(PackageDisableLinks)"
      EnableLink="$(PackageEnableLinks)"
      DeclareParameterItems="@(_Package_MsDeployDeclareParameters)"
      OptimisticParameterDefaultValue="$(EnableOptimisticParameterDefaultValue)"
      ImportDeclareParametersItems="$(_VsPackageParametersFile)"
      ReplaceRuleItems="@(MsDeployReplaceRules)"
      RetryAttempts="$(RetryAttemptsForDeployment)"
      ExePath="$(MSDeployPath)"
      />
    <ItemGroup>
      <FileWrites Include="$(PackageSourceRoot)" Condition="$(PackageAsSingleFile)"/>
      <FileWrites Include="$(PackageSourceRoot)\**" Condition="$(PackageAsSingleFile)"/>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterPackageUsingManifest)" RunEachTargetSeparately="false" />
  </Target>


 
  <!--***********************************************************************-->
  <!--TestDeployPackageToLocal task-->
  <!--***********************************************************************-->
  <PropertyGroup>
    <TestDeployPackageToLocalDependsOn Condition="'$(TestDeployPackageToLocalDependsOn)'==''">
      GetMSDeployInstalledVersionPath;
      GenerateMsDeployManifestSettings;
      GenerateMsdeployManifestFiles;
      Package;
    </TestDeployPackageToLocalDependsOn>
  </PropertyGroup>

  <Target Name="TestDeployPackageToLocal" DependsOnTargets="$(TestDeployPackageToLocalDependsOn)">

    <GetPublishingLocalizedString
       ID="PublishLocalizedString_TestDeployPackageOnCurrentMachine"
       LogType="Message" />
    <!-- "TestDeployPackageToLocal the package..."/>-->
    <PropertyGroup>
      <!--
        MSDeployPublishSourceType can be 
        SingleFilePackage(a single ziped file package)
        Manifest(a file with Iis VDir info plus file path)
        RawIisVDir(Let MSDeploy publish all files underneath the physical path)
        ArchiveDir(a folder genearated by package with all files to be transferred)
      -->
      <!--So far, if we see the single file package, we pick it up; otherwise, we get TestDeployPackageToLocal* from Iis vdir -->
      <TestDeployPackageToLocalSourceType>manifest</TestDeployPackageToLocalSourceType>
      <TestDeployPackageToLocalSourceRoot>@(_MSDeploySourceManifest->'%(FullPath)')</TestDeployPackageToLocalSourceRoot>
      <TestDeployPackageToLocalSourceType Condition="$(_CreatePackage) And $(PackageAsSingleFile)">package</TestDeployPackageToLocalSourceType>
      <TestDeployPackageToLocalSourceRoot Condition="$(_CreatePackage) And $(PackageAsSingleFile)">@(_MSDeployPackageFile->'%(FullPath)')</TestDeployPackageToLocalSourceRoot>
      <TestDeployPackageToLocalSourceType Condition="$(_CreatePackage) And !$(PackageAsSingleFile)">archiveDir</TestDeployPackageToLocalSourceType>
      <TestDeployPackageToLocalSourceRoot Condition="$(_CreatePackage) And !$(PackageAsSingleFile)">@(_MSDeployArchiveDir->'%(FullPath)')</TestDeployPackageToLocalSourceRoot>
      <!--Destination is always a manifest-->
      <TestDeployPackageToLocalDestinationType>auto</TestDeployPackageToLocalDestinationType>
      <TestDeployPackageToLocalDestinationRoot></TestDeployPackageToLocalDestinationRoot>
    </PropertyGroup>


    <ItemGroup>
      <MsDeploySourceProviderSetting Remove="@(MsDeploySourceProviderSetting)" />
      <MsDeploySourceProviderSetting Include="$(TestDeployPackageToLocalSourceType)">
        <Path>$(TestDeployPackageToLocalSourceRoot)</Path>
        <EncryptPassword>$(DeployEncryptKey)</EncryptPassword>
      </MsDeploySourceProviderSetting>
      <MsDeployDestinationProviderSetting Remove="@(MsDeployDestinationProviderSetting)" />
      <MsDeployDestinationProviderSetting Include="$(TestDeployPackageToLocalDestinationType)">
        <Path>$(TestDeployPackageToLocalDestinationRoot)</Path>
        <EncryptPassword>$(DeployEncryptKey)</EncryptPassword>
        <IncludeAcls>False</IncludeAcls>
      </MsDeployDestinationProviderSetting>
    </ItemGroup>

    <!--Debug/Diagnostic message is not localized -->
    <Message Text="TestDeployPackageToLocal MsDeploySourceProviderSetting is @(MsDeploySourceProviderSetting)" Condition="$(EnablePackageProcessLoggingAndAssert)" />
    <Message Text="TestDeployPackageToLocal MsDeployDestinationProviderSetting is @(MsDeployDestinationProviderSetting)" Condition="$(EnablePackageProcessLoggingAndAssert)"/>

    <VSMSDeploy
      Condition="!$(UseMsdeployExe)"
      MSDeployVersionsToTry="$(_MSDeployVersionsToTry)"
      Source="@(MsDeploySourceProviderSetting)"
      Destination="@(MsDeployDestinationProviderSetting)"
      DisableLink="$(PublishDisableLinks)"
      EnableLink="$(PublishEnableLinks)">
      <Output TaskParameter="Result" PropertyName="_TestDeployPackageToLocalResult" />
    </VSMSDeploy>
    <MSdeploy
      Condition="$(UseMsdeployExe)"
      Verb="sync"
      Source="@(MsDeploySourceProviderSetting)"
      Destination="@(MsDeployDestinationProviderSetting)"
      DisableLink="$(PublishDisableLinks)"
      EnableLink="$(PublishEnableLinks)"
      ExePath="$(MSDeployPath)" />
  </Target>


  <!--********************************************************************-->
  <!-- Target GenerateGlobalPublishSetting -->
  <!--********************************************************************-->
  <PropertyGroup>
    <NormalizePublishSettings Condition="'$(NormalizePublishSettings)'==''">true</NormalizePublishSettings>
    <AuthType Condition="'$(AuthType)'==''" >Basic</AuthType>
    <!-- Supported value for $(MSDeployPublishMethod): WMSVC, RemoteAgent, InProc -->
    <MSDeployPublishMethod Condition="'$(MSDeployPublishMethod)'==''" >WMSVC</MSDeployPublishMethod>
    <GenerateGlobalPublishSettingDependsOn Condition="'$(GenerateGlobalPublishSettingDependsOn)'==''">
      GetProjectWebProperties;
      ValidateGlobalSettings;
    </GenerateGlobalPublishSettingDependsOn>
  </PropertyGroup>

  <Target Name="GenerateGlobalPublishSetting"
          DependsOnTargets="$(GenerateGlobalPublishSettingDependsOn)">
    <PropertyGroup>
      <_UseWMSVC>false</_UseWMSVC>
      <_UseWMSVC Condition="'$(MSDeployPublishMethod)'=='WMSVC'">true</_UseWMSVC>
      <_UseRemoteAgent>false</_UseRemoteAgent>
      <_UseRemoteAgent Condition="'$(MSDeployPublishMethod)'=='RemoteAgent'">true</_UseRemoteAgent>
    </PropertyGroup>

    <PropertyGroup>
      <DestinationUseIis>true</DestinationUseIis>
    </PropertyGroup>

    <GetPublishingLocalizedString
       ID="PublishLocalizedString_ValidatingMsDeployPublishSettings"
       LogType="Message" />
    <!-- "Validate publish related properties" />-->
    
    <CreateProperty Value="$(CreatePackageOnPublish)">
      <Output TaskParameter="Value" PropertyName="_CreatePackage"/>
    </CreateProperty>
    
    <!--Debug/Diagnostic message is not localized-->
    <Message Text="_CreatePackage is $(_CreatePackage)" Condition="$(EnablePackageProcessLoggingAndAssert)" />

    <GetPublishingLocalizedString
       Condition="'$(MsDeployServiceUrl)'==''"
       ID="PublishLocalizedString_ValidateErrorMsDeployPublishSetting"
       ArgumentCount="1"
       Arguments="MsDeployServiceUrl"
       LogType="Error" />

    <Error  Text ="Target GenerateGlobalPublishSetting Failed"
       Condition="'$(MsDeployServiceUrl)'==''"
      />

    <!-- If $(IncludeIisSettingsOnPublish) is set but $(RemoteSitePhysicalPath) give a warning -->
    <GetPublishingLocalizedString
       Condition="!$(IncludeIisSettingsOnPublish) And '$(RemoteSitePhysicalPath)' != ''"
       ID="PublishLocalizedString_WarningOnSettingRemoteSitePhysicalPathWhenIISSettingIsNotInclude"
       LogType="Warning" />
    <!-- "Setting value to property of RemoteSitePhysicalPath might not work if IIS setting is not included" />-->

    <!-- If IncludeIisSettingsOnPublish is set then $(DeployAsIisApp) is not needed  -->
    <GetPublishingLocalizedString
       Condition="$(IncludeIisSettingsOnPublish) and $(DeployAsIisApp)"
       ID="PublishLocalizedString_WarningOnSettingBothIncludeIisSettingsOnPublishAndDeployAsIisApp"
       LogType="Warning" />
    <!-- "Setting both property values of DeployAsIisApp and IncludeIisSettingsOnPublish to true is not recommended, as IncludeIisSettingsOnPublish is a superset of DeployAsIisApp" />-->

    <CreateProperty Value="NTLM" Condition="!$(_UseWMSVC) And $(NormalizePublishSettings)">
      <Output TaskParameter="Value" PropertyName="AuthType"/>
    </CreateProperty>
    
    <!-- Debug/Diagnostic message is not localized-->
    <Message Text="AuthType is $(AuthType)" Condition="$(EnablePackageProcessLoggingAndAssert)" />

    <!-- Normalize service url such as convert a server name to format like https://<server>:8172/msdeploy.axd-->
    <NormalizeServiceUrl ServiceUrl="$(MsDeployServiceUrl)" UseWMSVC="$(_UseWMSVC)" UseRemoteAgent="$(_UseRemoteAgent)" 
                         Condition="$(NormalizePublishSettings)">
      <Output TaskParameter="ResultUrl" PropertyName="MsDeployServiceUrl" />
    </NormalizeServiceUrl>
    
    <!-- Debug/Diagnostic message is not localized-->
    <Message Text="MsDeployServiceUrl is $(MsDeployServiceUrl)" Condition="$(EnablePackageProcessLoggingAndAssert)" />

    <!-- Append the site name at the end of ServiceUrl like site=mysite-->
    <ConcatFullServiceUrlWithSiteName ServiceUrl="$(MsDeployServiceUrl)" SiteAppName="$(DeployIisAppPath)" 
                                      Condition="$(_UseWMSVC) And $(NormalizePublishSettings)">
      <Output TaskParameter="ResultUrl" PropertyName="MsDeployServiceUrl" />
    </ConcatFullServiceUrlWithSiteName>

    <!-- Debug/Diagnostic message is not localized-->
    <Message Text="MsDeployServiceUrl is $(MsDeployServiceUrl)" Condition="$(EnablePackageProcessLoggingAndAssert)" />
    
  </Target>

  <!--********************************************************************-->
  <!-- Target GatherLinkExtensionsToBeExposedInVS -->
  <!--********************************************************************-->
  <Target Name="GatherLinkExtensionsToBeExposedInVS">
    <PropertyGroup>
      <LinkExtensionFileName>LinkExtensioninfo.xml</LinkExtensionFileName>
      <LinksHiddenFromVS>HttpCertConfigExtension;CertificateExtension;FrameworkConfigExtension</LinksHiddenFromVS>
      <LinksDisabledByVS>ContentExtension</LinksDisabledByVS>
    </PropertyGroup>
    <GatherLinkExtensionsToBeExposedInVS LinksHiddenFromVS="$(LinksHiddenFromVS)"
                                         LinksDisabledByVS="$(LinksDisabledByVS)"
                                         LinkInfoFilePath="$(PackageTempRootDir)\\$(LinkExtensionFileName)" />
  </Target>

  <!--********************************************************************-->
  <!-- Target MSDeployPublish -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CreatePackageOnPublish Condition="'$(CreatePackageOnPublish)'==''">false</CreatePackageOnPublish>
    <MSDeployPublishDependsOn Condition="'$(MSDeployPublishDependsOn)'==''">
      GenerateGlobalPublishSetting;
      _CheckAzureNet46Support;      
      ValidateGlobalPackageSetting;
      GenerateMsDeployManifestSettings;
      GenerateMsdeployManifestFiles;
      GenerateSkipRuleForAppData;
      ImportPublishingParameterValues;
      PipelineMsdeploySpecificTransformPhase;
      Package;
    </MSDeployPublishDependsOn>
  </PropertyGroup>

  <Target Name="MSDeployPublish" DependsOnTargets="$(MSDeployPublishDependsOn)">

    <GetPublishingLocalizedString
       Condition="'$(MsDeployServiceUrl)'!=''"
       Importance="High"
       ID="PublishLocalizedString_StartMsDeployPublishToRemote"
       ArgumentCount="1"
       Arguments="$(MsDeployServiceUrl)"
       LogType="Message" />
    <!-- "Start Msdeploy Publish the Application/package to $(MsDeployServiceUrl) ..."/>-->
    
    <PropertyGroup>
      <!--
        MSDeployPublishSourceType can be 
        SingleFilePackage(a single ziped file package)
        Manifest(a file with Iis VDir info plus file pathes)
        RawIisVDir(Let MSDeploy published all files underneath the phisical path)
        ArchiveDir(a folder genearated by package with all files to be transferred)
      -->
      <!--So far, if we see the single file package, we pick it up; otherwise, we transport from Iis vdir -->
      <MSDeployPublishSourceType>manifest</MSDeployPublishSourceType>
      <MSDeployPublishSourceRoot>@(_MSDeploySourceManifest->'%(FullPath)')</MSDeployPublishSourceRoot>
      <MSDeployPublishSourceType Condition="$(CreatePackageOnPublish) And $(PackageAsSingleFile)">package</MSDeployPublishSourceType>
      <MSDeployPublishSourceRoot Condition="$(CreatePackageOnPublish) And $(PackageAsSingleFile)">@(_MSDeployPackageFile->'%(FullPath)')</MSDeployPublishSourceRoot>
      <MSDeployPublishSourceType Condition="$(CreatePackageOnPublish) And !$(PackageAsSingleFile)">archiveDir</MSDeployPublishSourceType>
      <MSDeployPublishSourceRoot Condition="$(CreatePackageOnPublish) And !$(PackageAsSingleFile)">@(_MSDeployArchiveDir->'%(FullPath)')</MSDeployPublishSourceRoot>

      <!--Destination is alway a manifest-->
      <MSDeployPublishDestinationType>auto</MSDeployPublishDestinationType>
      <MSDeployPublishDestinationRoot></MSDeployPublishDestinationRoot>

      <!--Property used when we invoke msdeploy process for publish-->   
      <MsDeployDoNotDeleteRule Condition="$(SkipExtraFilesOnServer)">DoNotDeleteRule</MsDeployDoNotDeleteRule>
    </PropertyGroup>

    <PropertyGroup >
      <_MSDeploySourceWebServerAppHostConfigDirectory>$(IisServer_WebServerAppHostConfigDirectory)</_MSDeploySourceWebServerAppHostConfigDirectory>
      <_MSDeploySourceWebServerManifest>$(IisServer_WebServerManifest)</_MSDeploySourceWebServerManifest>
      <_MSDeploySourceWebServerDirectory>$(IisServer_WebServerDirectory)</_MSDeploySourceWebServerDirectory>
    </PropertyGroup>

    
    <MapUriToIisWebServer Condition="('$(MSDeployPublishMethod)'=='INPROC') And ('$(MsDeployServiceUrl)' != '') "  
                          ProjectIisUrl="$(MsDeployServiceUrl)"
                          UseIISExpressHintFlag="$(_DeploymentUseIISExpressHint)">
      <Output TaskParameter="WebServerAppHostConfigDirectory" PropertyName="_MSDeployDestinationWebServerAppHostConfigDirectory"  />
      <Output TaskParameter="WebServerDirectory" PropertyName="_MSDeployDestinationWebServerDirectory"  />
      <Output TaskParameter="WebServerManifest" PropertyName="_MSDeployDestinationWebServerManifest"  />
    </MapUriToIisWebServer>

    
    <PropertyGroup>
      <_PublishMsDeployServiceUrl>$(MsDeployServiceUrl)</_PublishMsDeployServiceUrl>
      <_PublishMsDeployServiceUrl Condition="('$(MSDeployPublishMethod)'=='INPROC')"></_PublishMsDeployServiceUrl>
    </PropertyGroup>

    <PropertyGroup>
      <MSDeployEncryptProviderName Condition="'$(_DeploymentAsIisApp)' == 'true'">iisApp</MSDeployEncryptProviderName>
      <MSDeployEncryptProviderName Condition="'$(_DeploymentAsIisApp)' != 'true'">contentPath</MSDeployEncryptProviderName>
    </PropertyGroup>

    <ItemGroup>
      <MsDeploySourceProviderSetting Remove="@(MsDeploySourceProviderSetting)" />
      <MsDeploySourceProviderSetting Include="$(MSDeployPublishSourceType)">
        <Path>$(MSDeployPublishSourceRoot)</Path>
        <EncryptPassword>$(DeployEncryptKey)</EncryptPassword>
        <WebServerAppHostConfigDirectory>$(_MSDeploySourceWebServerAppHostConfigDirectory)</WebServerAppHostConfigDirectory>
        <WebServerManifest>$(_MSDeploySourceWebServerManifest)</WebServerManifest>
        <WebServerDirectory>$(_MSDeploySourceWebServerDirectory)</WebServerDirectory>
      </MsDeploySourceProviderSetting>
      <MsDeployDestinationProviderSetting Remove="@(MsDeployDestinationProviderSetting)" />
      <MsDeployDestinationProviderSetting Include="$(MSDeployPublishDestinationType)">
        <Path>$(MSDeployPublishDestinationRoot)</Path>
        <ComputerName>$(_PublishMsDeployServiceUrl)</ComputerName>
        <UserName>$(UserName)</UserName>
        <Password>$(Password)</Password>
        <EncryptPassword>$(DeployEncryptKey)</EncryptPassword>
        <IncludeAcls>False</IncludeAcls>
        <AuthType>$(AuthType)</AuthType>
        <WebServerAppHostConfigDirectory>$(_MSDeployDestinationWebServerAppHostConfigDirectory)</WebServerAppHostConfigDirectory>
        <WebServerManifest>$(_MSDeployDestinationWebServerManifest)</WebServerManifest>
        <WebServerDirectory>$(_MSDeployDestinationWebServerDirectory)</WebServerDirectory>
      </MsDeployDestinationProviderSetting>
    </ItemGroup>

    <!-- Additional destination provider settings -->
    <ItemGroup>
      <MsDeployAdditionalDestinationProviderSettings Include="$(MSDeployEncryptProviderName)">
        <Name>WebConfigEncryptProvider</Name>
        <Value>$(MSDeployWebConfigEncryptProvider)</Value>
      </MsDeployAdditionalDestinationProviderSettings>
    </ItemGroup>

    <!-- Debug/Diagnostic message is not localized-->
    <Message Text="MSDeployPublish MsDeploySourceProviderSetting is @(MsDeploySourceProviderSetting)" Condition="$(EnablePackageProcessLoggingAndAssert)" />
    <Message Text="MSDeployPublish MsDeployDestinationProviderSetting is @(MsDeployDestinationProviderSetting)" Condition="$(EnablePackageProcessLoggingAndAssert)"/>

    <PropertyGroup>
      <FilePreview Condition="'$(FilePreview)'==''">false</FilePreview>
    </PropertyGroup>
      
    <ExportParametersFile
      Condition="!$(UseDeclareParametersXMLInMsDeploy) And $(EnablePackageProcessLoggingAndAssert)"
      Parameters="@(MsDeployDeclareParameters)"
      DeclareSetParameterFile="$(PackageLogDir)\MSDeployPublish.parameters.xml"
      GenerateFileEvenIfEmpty="True"
      />

    <!-- First delete the ParameterFile-->
    <Delete Files="$(PublishParametersFile)"  Condition="Exists($(PublishParametersFile))" ContinueOnError="true"/>

    <!-- Create the Parameterfile if needed-->
    <ExportParametersFile
      Condition="$(UseDeclareParametersXMLInMsDeploy)"
      Parameters="@(MsDeployDeclareParameters)"
      DeclareSetParameterFile="$(PublishParametersFile)"
      IncludeDefaultValue="$(FilePreview)"
      GenerateFileEvenIfEmpty="True"
      />

    <PropertyGroup>
      <_VsPublishParametersFile></_VsPublishParametersFile>
      <_VsPublishParametersFile Condition="$(UseDeclareParametersXMLInMsDeploy) and '$(_VsPublishParametersFile)'==''">$(PublishParametersFile)</_VsPublishParametersFile>
    </PropertyGroup>

    <ItemGroup>
      <!-- We will include the parameter when $(UseDeclareParametersXMLInMsDeploy) is false-->
      <!-- We need to have those Parameters when it is direct publishing from manifest and not use -->
      <_VsPublish_MsDeployDeclareParameters Include="@(MsDeployDeclareParameters)" Condition="!$(UseDeclareParametersXMLInMsDeploy) And !$(CreatePackageOnPublish)" />
    </ItemGroup>
         
    <VSMSDeploy
      Condition="!$(UseMsdeployExe)"
      MSDeployVersionsToTry="$(_MSDeployVersionsToTry)"
      Source="@(MsDeploySourceProviderSetting)"
      Destination="@(MsDeployDestinationProviderSetting)"
      DisableLink="$(PublishDisableLinks)"
      EnableLink="$(PublishEnableLinks)"
      AllowUntrustedCertificate="$(AllowUntrustedCertificate)"
      BuildingInsideVisualStudio="$(BuildingInsideVisualStudio)"
      SkipExtraFilesOnServer="$(SkipExtraFilesOnServer)"
      SkipRuleItems="@(MsDeploySkipRules)"
      SetParameterItems="@(_VsPublish_MsDeployDeclareParameters)"
      OptimisticParameterDefaultValue="$(EnableOptimisticParameterDefaultValue)"
      ImportSetParametersItems="$(_VsPublishParametersFile)"
      WhatIf="$(FilePreview)"
      RetryAttempts="$(RetryAttemptsForDeployment)"
      EnableMSDeployBackup="$(EnableMSDeployBackup)"
      EnableMSDeployAppOffline="$(EnableMSDeployAppOffline)"
      EnableMSDeployWebConfigEncryptRule="$(MSDeployEnableWebConfigEncryptRule)"
      AdditionalDestinationProviderOptions="@(MsDeployAdditionalDestinationProviderSettings)"
      UseChecksum="$(MSDeployUseChecksum)"
      UserAgent="$(_MSDeployUserAgent)"
      InvokedByPublish="true"
    >
      <Output TaskParameter="Result" PropertyName="_PublishResult" />
    </VSMSDeploy>

    <PropertyGroup>
      <_EnableRuleList>$(MsDeployDoNotDeleteRule)</_EnableRuleList>
      <_EnableRuleList Condition="'$(_EnableRuleList)'=='' and '$(EnableMSDeployAppOffline)'=='true'">AppOffline</_EnableRuleList>
      <_EnableRuleList Condition="'$(_EnableRuleList)'!='' and '$(EnableMSDeployAppOffline)'=='true'">$(_EnableRuleList);AppOffline</_EnableRuleList>
      <_EnableRuleList Condition="'$(_EnableRuleList)'=='' and '$(MSDeployEnableWebConfigEncryptRule)'=='true'">EncryptWebConfig</_EnableRuleList>
      <_EnableRuleList Condition="'$(_EnableRuleList)'!='' and '$(MSDeployEnableWebConfigEncryptRule)'=='true'">$(_EnableRuleList);EncryptWebConfig</_EnableRuleList>
    </PropertyGroup>
    <MSdeploy
          Condition="$(UseMsdeployExe)"
          Verb="sync"
          Source="@(MsDeploySourceProviderSetting)"
          Destination="@(MsDeployDestinationProviderSetting)"
          DisableLink="$(PublishDisableLinks)"
          EnableLink="$(PublishEnableLinks)"
          EnableRule="$(_EnableRuleList)"
          AllowUntrusted="$(AllowUntrustedCertificate)"          
          SkipRuleItems="@(MsDeploySkipRules)"
          SetParameterItems="@(_VsPublish_MsDeployDeclareParameters)"
          OptimisticParameterDefaultValue="$(EnableOptimisticParameterDefaultValue)"
          ImportSetParametersItems="$(_VsPublishParametersFile)"
          RetryAttempts="$(RetryAttemptsForDeployment)"
          AdditionalDestinationProviderOptions="@(MsDeployAdditionalDestinationProviderSettings)"
          UseChecksum="$(MSDeployUseChecksum)"
          UserAgent="$(_MSDeployUserAgent)"
          ExePath="$(MSDeployPath)" />
  </Target>


  <!--********************************************************************-->
  <!--Target ValidatePublishProfileSettings -->
  <!--********************************************************************-->
  <PropertyGroup>
    <ValidatePublishProfileSettingsDependsOn>
      $(OnBeforeValidatePublishProfileSettings);
      $(ValidatePublishProfileSettingsDependsOn);
    </ValidatePublishProfileSettingsDependsOn>
  </PropertyGroup>

  <Target Name="ValidatePublishProfileSettings"
          DependsOnTargets="$(ValidatePublishProfileSettingsDependsOn)"
          Condition="'$(_PublishProfileSet)' == 'true' Or '$(WebPublishProfileFile)' != '' ">
    
    <!--This is a phase separation point-->
    <GetPublishingLocalizedString
       ID="PublishLocalizedString_WebPublishValidatePublishProfileSettings"
       ArgumentCount="1"
       Arguments="$(PublishProfile)"
       LogType="Message" />

    <GetPublishingLocalizedString
       ID="PublishLocalizedString_WebPublishInvalidatePublishProfileSettings"
       Condition="'$(WebPublishProfileFile)' == '' Or !Exists($(WebPublishProfileFile))"
       ArgumentCount="2"
       Arguments="$(PublishProfile);$(WebPublishProfileFile)"
       LogType="Error" />

   <GetPublishingLocalizedString
       ID="PublishLocalizedString_WebPublishProfileInvalidPropertyValue"
       Condition="'$(WebPublishMethod)' == '' Or '$(_WPPWebPublishMethodSupports.Contains(&quot;Web$(WebPublishMethod)Publish&quot;))' == 'false'"
       ArgumentCount="3"
       Arguments="$(PublishProfile);WebPublishMethod;$(WebPublishMethod)"
       LogType="Error" />

    <Error Text="Target ValidatePublishProfileSettings Failed"
           Condition="('$(WebPublishProfileFile)' == '' Or !Exists($(WebPublishProfileFile)))or 
           ('$(WebPublishMethod)' == '' Or '$(_WPPWebPublishMethodSupports.Contains(&quot;Web$(WebPublishMethod)Publish&quot;))' == 'false' )" />

    <CallTarget Targets="$(OnAfterValidatePublishProfileSettings)" RunEachTargetSeparately="False" />
  </Target>


  <!--********************************************************************-->
  <!--Target WebMSDeployPublish -->
  <!--********************************************************************-->
  <PropertyGroup>
    <WebMSDeployPublishDependsOn>
      MSDeployPublish;
    </WebMSDeployPublishDependsOn>
  </PropertyGroup>
  <Target Name="WebMSDeployPublish" DependsOnTargets="$(WebMSDeployPublishDependsOn)">
  </Target>


  <!--********************************************************************-->
  <!--Target WebPackagePublish -->
  <!--********************************************************************-->
  <PropertyGroup>
    <WebPackagePublishDependsOn>
      Package;
    </WebPackagePublishDependsOn>
  </PropertyGroup>
  <Target Name="WebPackagePublish" DependsOnTargets="$(WebPackagePublishDependsOn)">
  </Target>

  
  <!--********************************************************************-->
  <!--Target WebPublish -->
  <!--********************************************************************-->
  <PropertyGroup>
    <WebPublishDependsOn>
      $(WebPublishDependsOn);
      ValidatePublishProfileSettings;
    </WebPublishDependsOn>
    <WebPublishDependsOn Condition="'$(WebPublishMethod)' != ''">
      $(WebPublishDependsOn);
      Web$(WebPublishMethod)Publish;
    </WebPublishDependsOn>

  </PropertyGroup>
  <Target Name="WebPublish" DependsOnTargets="$(WebPublishDependsOn)">
  </Target>

  <!--********************************************************************-->
  <!--Target PipelineDeployPhase -->
  <!--********************************************************************-->
  <PropertyGroup>
    <PipelineDeployPhaseDependsOn>
      $(OnBeforePipelineDeployPhase);
      $(PipelineDeployPhaseDependsOn);
      $(DeployTarget);
    </PipelineDeployPhaseDependsOn>
    <PipelineDeployPhaseAfterTargets>
      $(PipelineDeployPhaseAfterTargets);
    </PipelineDeployPhaseAfterTargets>
  </PropertyGroup>

  <Target Name="PipelineDeployPhase"
          DependsOnTargets="$(PipelineDeployPhaseDependsOn)"
          AfterTargets="$(PipelineDeployPhaseAfterTargets)">
    <!--This is a phase separation point-->
    <GetPublishingLocalizedString
       ID="PublishLocalizedString_WebPublishPipelineDeployPhase"
       LogType="Message" />
    <!--<Message Text="Pipeline Deploy Phase" />-->
    
    <CallTarget Targets="$(OnAfterPipelineDeployPhase)" RunEachTargetSeparately="False" />
  </Target>


  <!--********************************************************************-->
  <!--Target DbDacFxPreview -->
  <!--********************************************************************-->
  <PropertyGroup>
    <DbDacFxPreviewDependsOn>
      $(DbDacFxPreviewDependsOn);
      GetMSDeployInstalledVersionPath;
    </DbDacFxPreviewDependsOn>
    <DbDacFxPreviewDependsOn Condition="('$(MSDeployPublishMethod)'=='INPROC')">
      $(DbDacFxPreviewDependsOn);
      LocalDbDacFxPreview;
    </DbDacFxPreviewDependsOn >
    <DbDacFxPreviewDependsOn Condition="('$(MSDeployPublishMethod)'!='INPROC')">
      $(DbDacFxPreviewDependsOn);
      RemoteDbDacFxPreview;
    </DbDacFxPreviewDependsOn >
  </PropertyGroup>
  <Target Name="DbDacFxPreview" DependsOnTargets="$(DbDacFxPreviewDependsOn)">
  </Target>
  

  <!--********************************************************************-->
  <!--Target DbFullSqlPreview -->
  <!--********************************************************************-->
  <PropertyGroup>
    <DbFullSqlPreviewDependsOn>
      $(DbFullSqlPreviewDependsOn);
      GetMSDeployInstalledVersionPath;
    </DbFullSqlPreviewDependsOn>
  </PropertyGroup>
  <Target Name="DbFullSqlPreview" DependsOnTargets="$(DbFullSqlPreviewDependsOn)">
    
    
    <ItemGroup>
      <_MsDeployDbFullSqlPreviewSourceNoPath Remove="@(_MsDeployDbFullSqlPreviewSourceNoPath)"  />
    </ItemGroup>

    <CreateItem Include="DbFullSql"
                      AdditionalMetadata="$(_MsDeployDbFullSqlPreviewProviderSettings)" >
      <Output TaskParameter="Include" ItemName="_MsDeployDbFullSqlPreviewSourceNoPath"/>
    </CreateItem>

  <ItemGroup>
      <_MsDeployDbFullSqlPreviewSource Remove="@(_MsDeployDbFullSqlPreviewSource)" />
      <_MsDeployDbFullSqlPreviewSource Include="@(_MsDeployDbFullSqlPreviewSourceNoPath)">
        <Path>$(_MsDeployDBFullSqlPreviewSourceConnectionString)</Path>
      </_MsDeployDbFullSqlPreviewSource>

      <_MsDeployDbFullSqlPreviewDestination Remove="@(_MsDeployDbFullSqlPreviewDestination)" />
      <_MsDeployDbFullSqlPreviewDestination Include="DbFullSql">
        <Path>$(_MsDeployDbFullSqlPreviewOutputPath)</Path>
      </_MsDeployDbFullSqlPreviewDestination>
    </ItemGroup>


    <VSMSDeploy Condition="!$(UseMsdeployExe)"
       MSDeployVersionsToTry="$(_MSDeployVersionsToTry)"
       Source="@(_MsDeployDbFullSqlPreviewSource)"
       Destination="@(_MsDeployDbFullSqlPreviewDestination)"
       DeploymentTraceLevel="$(PackageTraceLevel)"
       AllowUntrustedCertificate="$(AllowUntrustedCertificate)"
       BuildingInsideVisualStudio="$(BuildingInsideVisualStudio)"
       InvokedByPublish="true"
       RetryAttempts="$(RetryAttemptsForDeployment)">
    </VSMSDeploy>

    <MSdeploy
      Condition="$(UseMsdeployExe)"
      Verb="sync"
      Source="@(_MsDeployDbFullSqlPreviewSource)"
      Destination="@(_MsDeployDbFullSqlPreviewDestination)"
      RetryAttempts="$(RetryAttemptsForDeployment)"
      AllowUntrusted="$(AllowUntrustedCertificate)"
      ExePath="$(MSDeployPath)"
      />
  </Target>

  <!--********************************************************************-->
  <!--Target ValidateDbDacFxPreviewSettings -->
  <!--********************************************************************-->
  <PropertyGroup>
    <ValidateDbDacFxPreviewSettingsDependsOn>
      $(ValidateDbDacFxPreviewSettingsDependsOn);
      GetMSDeployInstalledVersionPath;
    </ValidateDbDacFxPreviewSettingsDependsOn>
  </PropertyGroup>
  <Target Name="ValidateDbDacFxPreviewSettings" DependsOnTargets="$(ValidateDbDacFxPreviewSettingsDependsOn)">
    <GetPublishingLocalizedString
    Condition="'$(_MsDeployDbDacFxPreviewDestinationConnectionString)' == ''"
    ID="PublishLocalizedString_MsBuildPropertySettingValue"
    ArgumentCount="2"
    Arguments="_MsDeployDbDacFxPreviewDestinationConnectionString;'$(_MsDeployDbDacFxPreviewDestinationConnectionString)'"
     >
      <Output TaskParameter="Result" PropertyName="_PublishLocalizedString_MSBuildTargetFailed_1" />
    </GetPublishingLocalizedString>

    <GetPublishingLocalizedString
     Condition="'$(_MsDeployDbDacFxPreviewSourceFile)' == ''"
     ID="PublishLocalizedString_MsBuildPropertySettingValue"
     ArgumentCount="2"
     Arguments="_MsDeployDbDacFxPreviewSourceFile;'$(_MsDeployDbDacFxPreviewSourceFile)'"
     >
      <Output TaskParameter="Result" PropertyName="_PublishLocalizedString_MSBuildTargetFailed_2" />
    </GetPublishingLocalizedString>

    <GetPublishingLocalizedString
       ID="PublishLocalizedString_MSBuildTargetFailed"
       ArgumentCount="1"
       Arguments="LocalDbDacFxPreview"
       >
      <Output TaskParameter="Result" PropertyName="_PublishLocalizedString_MSBuildTargetFailed_Preview" />
    </GetPublishingLocalizedString>

    <Error Condition="'$(_MsDeployDbDacFxPreviewDestinationConnectionString)' == '' Or '$(_MsDeployDbDacFxPreviewSourceFile)' == ''"
           Text="$(_PublishLocalizedString_MSBuildTargetFailed_Preview) $(_PublishLocalizedString_MSBuildTargetFailed_1) $(_PublishLocalizedString_MSBuildTargetFailed_2)" />
  </Target>


    <!--********************************************************************-->
  <!--Target LocalDbDacFxPreview -->
  <!--********************************************************************-->
  <PropertyGroup>
    <LocalDbDacFxPreviewDependsOn>
      $(LocalDbDacFxPreviewDependsOn);
      GetMSDeployInstalledVersionPath;
      ValidateDbDacFxPreviewSettings;
    </LocalDbDacFxPreviewDependsOn>
  </PropertyGroup>
  <Target Name="LocalDbDacFxPreview" DependsOnTargets="$(LocalDbDacFxPreviewDependsOn)">

    <!--msdeploy
        -verb:
            sync
        -source:
            DbDacFx="C:\input.dacpac"
        -dest:
            DbDacFx="[Destination Connection String]",
            dacpacAction=Script,
            OutputPath="C:\output.sql"
    -->
    <ItemGroup>
      <_MsDeployDbDacFxPreviewPreSourceOptions Remove="@(_MsDeployDbDacFxPreviewPreSourceOptions)" />
      <_MsDeployDbDacFxPreviewPreSourceOptions Include="DbDacFx">
        <includeData>False</includeData>
        <AdditionalProviderSettings>includeData</AdditionalProviderSettings>
      </_MsDeployDbDacFxPreviewPreSourceOptions>
    </ItemGroup>

    <CreateDacPac
      Source="$(_MsDeployDbDacFxPreviewSourceFile)"
      OutputDacPacPath="$(_MsDeployDbDacFxPreviewAutoScriptPath)"
      RelativeRootPath="$(WebPublishPipelineSourceRootDirectory)"
      MSDeployVersionsToTry="$(_MSDeployVersionsToTry)"
        UseMSDeployExe="$(UseMsdeployExe)"
        MSDeployExePath="$(MSDeployPath)"
        LocalDBVersionToUseForSqlExpress="$(_LocalDBVersionToUseForSqlExpress)"
      AdditionalSourceFlags="@(_MsDeployDbDacFxPreviewPreSourceOptions)"
      >
      <Output TaskParameter="Result" PropertyName="_MsDeployDbDacFxPreviewDacpacFile"  />
    </CreateDacPac>
      
   
    <ItemGroup>
      <_MsDeployDbDacFxPreviewSource Remove="@(_MsDeployDbDacFxPreviewSource)" />
      <_MsDeployDbDacFxPreviewSource Include="DbDacFx">
        <Path>$([System.IO.Path]::GetFullPath($(_MsDeployDbDacFxPreviewDacpacFile)))</Path>
      </_MsDeployDbDacFxPreviewSource>

      <_MsDeployDbDacFxPreviewDestination Remove="@(_MsDeployDbDacFxPreviewDestination)" />
      <_MsDeployDbDacFxPreviewDestination Include="DbDacFx">
        <Path>$(_MsDeployDbDacFxPreviewDestinationConnectionString)</Path>
        <dacpacAction>Script</dacpacAction>
      </_MsDeployDbDacFxPreviewDestination>
    </ItemGroup>

    <VSMSDeploy Condition="!$(UseMsdeployExe)"
       MSDeployVersionsToTry="$(_MSDeployVersionsToTry)"
       Source="@(_MsDeployDbDacFxPreviewSource)"
       Destination="@(_MsDeployDbDacFxPreviewDestination)"
       DeploymentTraceLevel="$(PackageTraceLevel)"
       AllowUntrustedCertificate="$(AllowUntrustedCertificate)"
       BuildingInsideVisualStudio="$(BuildingInsideVisualStudio)"
       InvokedByPublish="true"
       RetryAttempts="$(RetryAttemptsForDeployment)">
    </VSMSDeploy>

    <MSdeploy
      Condition="$(UseMsdeployExe)"
      Verb="sync"
      Source="@(_MsDeployDbDacFxPreviewSource)"
      Destination="@(_MsDeployDbDacFxPreviewDestination)"
      RetryAttempts="$(RetryAttemptsForDeployment)"
      AllowUntrusted="$(AllowUntrustedCertificate)"
      ExePath="$(MSDeployPath)"
      />
  </Target>


  <!--********************************************************************-->
  <!--Target RemoteDbDacFxPreview -->
  <!--********************************************************************-->
  <PropertyGroup>
    <RemoteDbDacFxPreviewDependsOn>
      $(RemoteDbDacFxPreviewDependsOn);
      GetMSDeployInstalledVersionPath;
      GenerateGlobalPublishSetting;
      ValidateDbDacFxPreviewSettings;
      _DetectDbDacFxProvider;
    </RemoteDbDacFxPreviewDependsOn>
  </PropertyGroup>
  <Target Name="RemoteDbDacFxPreview" DependsOnTargets="$(RemoteDbDacFxPreviewDependsOn)">
    <PropertyGroup>
      <_PublishMsDeployServiceUrl>$(MsDeployServiceUrl)</_PublishMsDeployServiceUrl>
      <_PublishMsDeployServiceUrl Condition="('$(MSDeployPublishMethod)'=='INPROC')"></_PublishMsDeployServiceUrl>
    </PropertyGroup>


    <ItemGroup>
      <_MsDeployDbDacFxPreviewPreSourceOptions Remove="@(_MsDeployDbDacFxPreviewPreSourceOptions)" />
      <_MsDeployDbDacFxPreviewPreSourceOptions Include="DbDacFx">
        <includeData>False</includeData>
        <AdditionalProviderSettings>includeData</AdditionalProviderSettings>
      </_MsDeployDbDacFxPreviewPreSourceOptions>
    </ItemGroup>
    
    <CreateDacPac
    Source="$(_MsDeployDbDacFxPreviewSourceFile)"
    OutputDacPacPath="$(_MsDeployDbDacFxPreviewAutoScriptPath)"
    RelativeRootPath="$(WebPublishPipelineSourceRootDirectory)"
    MSDeployVersionsToTry="$(_MSDeployVersionsToTry)"
      UseMSDeployExe="$(UseMsdeployExe)"
      MSDeployExePath="$(MSDeployPath)"
      LocalDBVersionToUseForSqlExpress="$(_LocalDBVersionToUseForSqlExpress)"
      AdditionalSourceFlags="@(_MsDeployDbDacFxPreviewPreSourceOptions)"
      >
      <Output TaskParameter="Result" PropertyName="_MsDeployDbDacFxPreviewDacpacFile"  />
    </CreateDacPac>

    <MapUriToIisWebServer Condition="('$(MSDeployPublishMethod)'=='INPROC') And ('$(MsDeployServiceUrl)' != '') "
                          ProjectIisUrl="$(MsDeployServiceUrl)"
                          UseIISExpressHintFlag="$(_DeploymentUseIISExpressHint)">
      <Output TaskParameter="WebServerAppHostConfigDirectory" PropertyName="_MSDeployDestinationWebServerAppHostConfigDirectory"  />
      <Output TaskParameter="WebServerDirectory" PropertyName="_MSDeployDestinationWebServerDirectory"  />
      <Output TaskParameter="WebServerManifest" PropertyName="_MSDeployDestinationWebServerManifest"  />
    </MapUriToIisWebServer>

    <ItemGroup>
      <_MsDeployRemoteDbDacFxPreviewSource Remove="@(_MsDeployRemoteDbDacFxPreviewSource)" />
      <_MsDeployRemoteDbDacFxPreviewSource Include="DbDacFx">
        <Path>$([System.IO.Path]::GetFullPath($(_MsDeployDbDacFxPreviewDacpacFile)))</Path>
      </_MsDeployRemoteDbDacFxPreviewSource>

      <_MsDeployRemoteDbDacFxPreviewDestination Remove="@(_MsDeployRemoteDbDacFxPreviewDestination)" />
      <_MsDeployRemoteDbDacFxPreviewDestination Include="DbDacFx">
        <Path>$(_MsDeployDbDacFxPreviewDestinationConnectionString)</Path>
        <ComputerName>$(_PublishMsDeployServiceUrl)</ComputerName>
        <UserName>$(UserName)</UserName>
        <Password>$(Password)</Password>
        <AuthType>$(AuthType)</AuthType>
        <WebServerAppHostConfigDirectory>$(_MSDeployDestinationWebServerAppHostConfigDirectory)</WebServerAppHostConfigDirectory>
        <WebServerManifest>$(_MSDeployDestinationWebServerManifest)</WebServerManifest>
        <WebServerDirectory>$(_MSDeployDestinationWebServerDirectory)</WebServerDirectory>
        <dacpacAction>Script</dacpacAction>
      </_MsDeployRemoteDbDacFxPreviewDestination>
    </ItemGroup>

    <VSMSDeploy
          Condition="!$(UseMsdeployExe)"
          HighImportanceEventTypes="DbDacFxTraceEventArgs"
          MSDeployVersionsToTry="$(_MSDeployVersionsToTry)"
          Source="@(_MsDeployRemoteDbDacFxPreviewSource)"
          Destination="@(_MsDeployRemoteDbDacFxPreviewDestination)"
          AllowUntrustedCertificate="$(AllowUntrustedCertificate)"
          BuildingInsideVisualStudio="$(BuildingInsideVisualStudio)"
          InvokedByPublish="true"
          RetryAttempts="$(RetryAttemptsForDeployment)"
    >
      <Output TaskParameter="Result" PropertyName="_PublishResult" />
    </VSMSDeploy>
    <MSdeploy
          Condition="$(UseMsdeployExe)"
          Verb="sync"
          Verbose="true"
          Source="@(_MsDeployRemoteDbDacFxPreviewSource)"
          Destination="@(_MsDeployRemoteDbDacFxPreviewDestination)"
          AllowUntrusted="$(AllowUntrustedCertificate)"
          RetryAttempts="$(RetryAttemptsForDeployment)"
          ExePath="$(MSDeployPath)" />
  </Target>

    <!--********************************************************************-->
    <!-- Target _CheckAzureNet46Support-->
    <!--********************************************************************-->
    <Target Name="_CheckAzureNet46Support" Condition="'$(TargetFrameworkVersion)' == 'v4.6' and $(_Enable46Check) != 'false'">
      <VerifyAzureNet46Support Condition="'$(MsDeployServiceUrl)' != ''"
        ServiceUrl="$(MsDeployServiceUrl)">
       </VerifyAzureNet46Support>
    </Target>
  

    <!--********************************************************************-->
    <!-- Target _DetectDbDacFxProvider-->
    <!--********************************************************************-->
    <Target Name="_DetectDbDacFxProvider">
      <PropertyGroup>
        <_PublishMsDeployServiceUrl>$(MsDeployServiceUrl)</_PublishMsDeployServiceUrl>
        <_PublishMsDeployServiceUrl Condition="('$(MSDeployPublishMethod)'=='INPROC')"></_PublishMsDeployServiceUrl>
        <_MSDeployPublishSourceType>dbDacFx</_MSDeployPublishSourceType>
      </PropertyGroup>

      <ItemGroup>
        <_MsDeploySourceProviderSetting Remove="@(_MsDeploySourceProviderSetting)" />
        <_MsDeploySourceProviderSetting Include="$(_MSDeployPublishSourceType)">
          <ComputerName>$(_PublishMsDeployServiceUrl)</ComputerName>
          <UserName>$(UserName)</UserName>
          <Password>$(Password)</Password>
          <AuthType>$(AuthType)</AuthType>
        </_MsDeploySourceProviderSetting>
        <_DbDacFxDestinationPathesToPublish Include="$([MSBuild]::Escape($(_MsDeployDbDacFxPreviewDestinationConnectionString)))" />
      </ItemGroup>

      <DetectRemoteProvider Condition="'$(_PublishMsDeployServiceUrl)' != ''"
        MSDeployVersionsToTry="$(_MSDeployVersionsToTry)"
        Source="@(_MsDeploySourceProviderSetting)"
        ProviderName="dbDacFx"
        ProviderPathes="@(_DbDacFxDestinationPathesToPublish)">
      </DetectRemoteProvider>

    </Target>


  <!--***********************************************************************-->
  <!--Custom Publish-->
  <!--***********************************************************************-->

  <Target Name="CustomPublish" >
    <!-- MicroservicePublish-->
    <CallTarget Targets="$(PublishTarget)" Condition=" '$(PublishTarget)' != ''"/>
  
  </Target>

  <Target Name="Microservice">
    <PropertyGroup>
    <MicroserviceRootFolder Condition="'$(MicroserviceRootFolder)' == '' ">$(WebPublishPipelineProjectDirectory)\apiapp.json</MicroserviceRootFolder>
    </PropertyGroup>

    <PublishNuGetPackage
      ManifestFileFullPath="$(MicroserviceRootFolder)"
      ProjectFileFullPath="$(WebPublishPipeLineProjectFullPath)"
      IntermediateOutputPath="$(_WPPDefaultIntermediateOutputPath)"
      ProfileUserFile="$(PublishProfileRootFolder)\$(PublishProfileName).pubxml.user"
      ApiKey="$(MicroserviceApiKey)"
      />
</Target>
  
  <!-- The build process can be extended in a similar way as MS.Common.targets -->
  <PropertyGroup>
    <ImportByWildcardAfterMicrosoftWebPublishingTargets Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingTargets)'==''">true</ImportByWildcardAfterMicrosoftWebPublishingTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter\*" Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingTargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter')"/>
</Project>