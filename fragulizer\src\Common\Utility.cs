﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Fragulizer.Models.Partials;
using System.Text;
using ToolsFramework;
using System.IO;

namespace Fragulizer.Common
{
    public class Utility
    {
        public static string StreamToBase64(byte[] arrInput)
        {
            return string.Format("data:image/{0};base64,{1}",
                   "jpeg",
                   Convert.ToBase64String(arrInput, Base64FormattingOptions.None)
               );
        }

        /// <summary>
        /// get date base on period
        /// </summary>
        /// <param name="period">possible values: Live, 1M, 3M, 6M, 1Y</param>
        /// <param name="startDate">datetime, can be null</param>
        /// <param name="endDate">datetime, can be null</param>
        public static void GetPeriodDate(string period, ref Nullable<DateTime> startDate, ref Nullable<DateTime> endDate)
        {
            switch (period.ToUpper())
            {
                case "LIVE":
                    startDate = DateTime.UtcNow.Date;
                    endDate = DateTime.UtcNow;
                    break;
                case "1M":
                    startDate = DateTime.UtcNow.AddMonths(-1);
                    endDate = DateTime.UtcNow;
                    break;
                case "3M":
                    startDate = DateTime.UtcNow.AddMonths(-3);
                    endDate = DateTime.UtcNow;
                    break;
                case "6M":
                    startDate = DateTime.UtcNow.AddMonths(-6);
                    endDate = DateTime.UtcNow;
                    break;
                case "1Y":
                    startDate = DateTime.UtcNow.AddYears(-1);
                    endDate = DateTime.UtcNow;
                    break;
                case "CUSTOM":
                    startDate = startDate.Value.ToUniversalTime();
                    endDate = endDate.Value.ToUniversalTime();
                    break;
                default:
                    if (startDate == null || endDate == null)
                        GetPeriodDate(Tool.Settings.defaultperiod.Trim(), ref startDate, ref endDate);
                    break;

            }
        }

        public static string GetSupplyHyperlink()
        {
            string languageName = Services.Get<ILanguageService>().GetCurrentLanguageName();
            string pattern = "http://www.euroland.com/";//Tool.Settings["supplied"].Value;
            if (pattern.IndexOf("?") != -1)
            {
                return pattern + "&SelectLanguage=" + languageName;
            }
            else
            {
                return pattern + "?SelectLanguage=" + languageName;
            }
        }
        /// <summary>
        /// Display number 
        /// </summary>
        /// <param name="numbertext"></param>
        /// <returns></returns>
        public static string DisplayNumberByLanguage(string numbertext)
        {
            if (Tool.Culture.Name.ToLower().StartsWith("ar") && Tool.Settings.uselatinnumbers == false)
            {
                return ToolsFramework.Mvc.FormatUtility.ToArabicNumber(numbertext);
            }
            else
            {
                return numbertext;
            }
        }

        /// <summary>
        /// Convert timespan from source TimeZone to Destination Timezone
        /// </summary>
        /// <param name="input"></param>
        /// <param name="FromTimezoneId"></param>
        /// <param name="DestinationTimezoneId"></param>
        /// <returns></returns>
        public static TimeSpan ConvertTimeSpan(TimeSpan input, string FromTimezoneId, string DestinationTimezoneId)
        {
            //Make source timezone from SourceTimeZoneId
            TimeZoneInfo sourceTimeZone = TimeZoneInfo.FindSystemTimeZoneById(FromTimezoneId);
            //Make destination timezone from DestinationTimeZoneId
            TimeZoneInfo destinationTimeZone = TimeZoneInfo.FindSystemTimeZoneById(DestinationTimezoneId);
            //Get offset from Destination TimeZone and Source TimeZone
            TimeSpan offset = destinationTimeZone.GetUtcOffset(DateTime.Now)
                                    .Subtract(sourceTimeZone.GetUtcOffset(DateTime.Now));
            //Calculate timespan with caculated offset above
            return input.Add(offset);
        }

        public static string GetCurrentTimezone()
        {
            string zone = string.Empty;
            float timezoneOffset = GetPageTimeZoneOffSet();
            if (timezoneOffset > 0) zone = "GMT+" + timezoneOffset.ToString();
            else if (timezoneOffset < 0) zone = "GMT-" + Math.Abs(timezoneOffset).ToString();
            else zone = "GMT";
            return zone;
        }

        public static float GetPageTimeZoneOffSet()
        {
            float timeZoneOffset = 0;
            string pageTimeZoneID = Tool.Settings.timezone;
            try
            {
                TimeZoneInfo pageTimezone = TimeZoneInfo.FindSystemTimeZoneById(pageTimeZoneID);
                int hour = pageTimezone.BaseUtcOffset.Hours;
                if (pageTimezone.IsDaylightSavingTime(DateTime.UtcNow)) hour++;
                int minute = pageTimezone.BaseUtcOffset.Minutes;

                timeZoneOffset = hour + (float)(minute / 60);
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return timeZoneOffset;
        }

        public static string GetPageTimeZoneID()
        {
            string pageTimeZoneID = "Central Europe Standard Time";
            if (Tool.Settings.timezone != string.Empty)
            {
                pageTimeZoneID = Tool.Settings.timezone;
            }

            return pageTimeZoneID;
        }

        public static DateTime CETToAny(DateTime timeFromConvert, string timeZoneID)
        {
            DateTime convertedDate = timeFromConvert;

            try
            {
                TimeZoneInfo toTimeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneID);
                TimeZoneInfo cestTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Central Europe Standard Time");
                convertedDate = TimeZoneInfo.ConvertTime(timeFromConvert, cestTimeZone, toTimeZone);
            }
            catch (InvalidTimeZoneException ex)
            {
                throw ex;
            }

            return convertedDate;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="marketId"></param>
        /// <returns></returns>
        public static string GetCustomMarketName(int marketId)
        {
            //Combine key
            string marketKey = "_" + marketId;
            string cultureKey = Tool.Culture.Name.ToLower();
            //check exist custom market name
            if (Tool.Settings["custommarketname"].Contains(marketKey))
            {
                return Tool.Settings["custommarketname"][marketKey][cultureKey].Value;
            }
            return string.Empty;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="currency"></param>
        /// <returns></returns>
        public static string GetCurrencySymbol(string currency)
        {
            var currencySign = string.Empty;
            currency = currency.Trim();
            if (Tool.Settings["customcurrencysign"].Contains(currency))
            {
                currencySign = Tool.Settings["customcurrencysign"][currency][Tool.Culture.Name.ToLower()].Value;
            }
            if (string.IsNullOrEmpty(currencySign))
            {
                currencySign = currency;
            }
            return currencySign.Trim();
        }

        /// <summary>
        ///  Get data delay string
        /// </summary>
        /// <param name="listShare"></param>
        /// <returns></returns>
        public static String GetFooterString(List<MarketInfo> listShare)
        {
            //Return if listShare is empty or not existed
            if (listShare == null || listShare.Count == 0)
                return string.Empty;
            //Get translation text for EOD, RealTime and DataDelayed
            string strEODForAll = Translations.ALL_DATA_END_OF_DAY.ToString();
            string strRealTimeForAll = Translations.ALL_DATA_REAL_TIME.ToString();
            string strDataDelayedForAll = Translations.ALL_DATA_DELAYED.ToString();

            string strEOD = Translations.DATA_END_OF_DAY.ToString();
            string strRealTime = Translations.DATA_REAL_TIME.ToString();
            string strDataDelayed = Translations.DATA_DELAYED.ToString();

            string provider = Translations.DATA_PROVIDED.ToString();

            StringBuilder footerBuilder = new StringBuilder();
            List<string> Messages = new List<string>();
            int total = listShare.Count;
            //Get total of market have marketdatadelay is EOD.
            int totalEOD = listShare.Where(l => l.Delay.ToLower().Contains("eod")).Count();
            //Get tatol of market have marketdatadelay is Real time
            int totalRealTime = listShare.Where(l => l.Delay.ToLower().Contains("real-time")).Count();
            //if all markets are end-of-day then add text EOD to message list
            if (totalEOD == total)
            {
                Messages.Add(strEODForAll);
            }
            //else if all markets are realtime then add Realtime to message list
            else if (totalRealTime == total)
            {
                Messages.Add(strRealTimeForAll);
            }
            // else not are end-of-day and realtime 
            // and same delayed-time
            else if (totalRealTime == 0 && totalEOD == 0
                && listShare.Select(l => l.Delay).Distinct().Count() == 1)
            {
                //Default is 15 minutes.
                string delay = "15";
                if (!String.IsNullOrEmpty(listShare[0].Delay.Trim()))
                {
                    //Get other number from database.
                    delay = listShare[0].Delay.Trim();
                }
                //Add to messages.
                Messages.Add(string.Format(strDataDelayedForAll, DisplayNumberByLanguage(delay)));
            }
            // if all market isn't like some specify case above
            else
            {
                //Get only one item for each MarketDataDelay value.
                var listDataDelay = listShare.Select(s => s.Delay).Distinct().ToList();
                foreach (var item in listDataDelay)
                {
                    //With each item, we make message.
                    var listMarketSameDataDelay = listShare.Where(p => p.Delay == item).ToList();
                    var m = listMarketSameDataDelay.First();
                    //var delayValue = "15";
                    if (listMarketSameDataDelay.Count == 1)
                    {
                        var marketName = GetCustomMarketName(m.MarketNumber);
                        if (string.IsNullOrEmpty(marketName))
                            marketName = m.MarketName;

                        if (string.IsNullOrEmpty(m.Delay.Trim()))
                        {
                            Messages.Add(string.Format(strDataDelayed, DisplayNumberByLanguage("15"), marketName));

                        }
                        else if (m.Delay.ToLower().Contains("eod"))
                        {
                            Messages.Add(string.Format(strEOD, marketName));
                        }
                        else if (m.Delay.ToLower().Contains("real-time"))
                        {
                            Messages.Add(string.Format(strRealTime, marketName));
                        }
                        else
                        {
                            int d = 15;
                            if (int.TryParse(m.Delay.Trim(), out d))
                            {
                                Messages.Add(string.Format(strDataDelayed, DisplayNumberByLanguage(d.ToString()), marketName));

                            }
                        }
                    }
                    else
                    {
                        List<string> markets = new List<string>();
                        listMarketSameDataDelay = listMarketSameDataDelay.Distinct().ToList();
                        foreach (var l in listMarketSameDataDelay)
                        {
                            var marketName = GetCustomMarketName(l.MarketNumber);
                            if (string.IsNullOrEmpty(marketName))
                                marketName = l.MarketName;
                            //Add market name to list
                            if (!markets.Contains(marketName))
                                markets.Add(marketName);
                        }
                        var market = string.Join(",", markets);
                        int d = 15;
                        if (string.IsNullOrEmpty(m.Delay))
                        {
                            Messages.Add(string.Format(strDataDelayed, DisplayNumberByLanguage("15"), market));
                        }
                        else if (int.TryParse(m.Delay.Trim(), out d))
                        {
                            Messages.Add(string.Format(strDataDelayed, DisplayNumberByLanguage(d.ToString()), market));
                        }
                        else if (m.Delay.ToLower().Contains("eod"))
                        {
                            Messages.Add(string.Format(strEOD, market));
                        }
                        else if (m.Delay.ToLower().Contains("real-time"))
                        {
                            Messages.Add(string.Format(strRealTime, market));
                        }
                    }
                }
            }
            //get data provider
            var lstProvider = listShare.Select(p => p.DataSource).Distinct();
            //Build result
            if (Tool.Settings.showdatadelayinfo)
            {
                footerBuilder.Append("<div class=\"data-delay-box\">");
                foreach (var m in Messages)
                {
                    footerBuilder.Append("<div>");
                    footerBuilder.Append(m);
                    footerBuilder.Append("</div>");
                }
                footerBuilder.Append("</div>");
            }

            //For data provider
            if (Tool.Settings.showdataproviderinfo)
            {
                footerBuilder.Append("<div class=\"data-provider-box\">");
                //footerBuilder.Append(provider);
                string strProvider = string.Empty;
                foreach (var p in lstProvider)
                {
                    strProvider += p + ", ";
                }
                footerBuilder.Append(provider.Replace("@", strProvider.Trim().TrimEnd(',')));
                footerBuilder.Append("</div>");
            }

            return footerBuilder.ToString();
        }

        public static MemoryStream RenderSvg(string exportDocument, string contentType)
        {
            try
            {
                MemoryStream ms = new MemoryStream();
                Exporter imgExportDocument = new Exporter("ExportDocument", contentType, -1, exportDocument);
                ms = imgExportDocument.WriteToStream();
                return ms;
            }
            catch
            {
                return null;
            }
        }

        public static string GetExcelDecimalFormat()
        {
            string s = "";
            for (int i = 0; i < Tool.Culture.NumberFormat.CurrencyDecimalDigits; i++)
            {
                s += "0";
            }
            return "#" + Tool.Culture.NumberFormat.CurrencyGroupSeparator + "##0" + Tool.Culture.NumberFormat.CurrencyDecimalSeparator + s;
        }

        public static string GetExcelIntFormat()
        {
            return "#" + Tool.Culture.NumberFormat.CurrencyGroupSeparator + "##0";
        }
    }
}