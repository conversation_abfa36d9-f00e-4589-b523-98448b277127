stages:
  - build
  - deploy
  - verify
  - release

variables:
  NUGET_PACKAGES_DIR: './nuget'
  BINARIES_DIR: 'bin'
  BUILD_CONFIG: 'Release'
  NUGET_EXE: 'C:\Nuget\nuget.exe'
  CURL: 'C:\curl\curl.exe'
  MSBUILD_EXE: 'C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe'
  MSBUILD_PKG_NAME: 'Microsoft.Data.Tools.Msbuild'
  MSBUILD_PKG_VERSION: '10.0.61804.210'
  MSBUILD_PKG_OUTPUT_DIR: '.\nuget'
  SOLUTION_FILE_NAME: 'fragulizer.sln'
  NET4SDK: 'C:\Windows\Microsoft.NET\Framework\v4.0.30319'


.nuget-restore: &nuget-restore |
  "%NUGET_EXE%" restore "%CI_PROJECT_DIR%\%SOLUTION_FILE_NAME%"

.retry_on_failed:
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
      - api_failure

.install-msbuild: &install-msbuild |
  ECHO Installing "%MSBUILD_PKG_NAME%" package from nuget...
  IF NOT EXIST "%CI_PROJECT_DIR%\%MSBUILD_PKG_OUTPUT_DIR%\%MSBUILD_PKG_NAME%.%MSBUILD_PKG_VERSION%\lib\net46" (
      "%NUGET_EXE%" install "%MSBUILD_PKG_NAME%" -Version "%MSBUILD_PKG_VERSION%" -OutputDirectory "%CI_PROJECT_DIR%\%MSBUILD_PKG_OUTPUT_DIR%"
  )

.deploy-db: &deploy-db |
  'CALL "%CI_PROJECT_DIR%\.gitlab\db\table.cmd" "%DB_USER%" "%DB_PSW%" "%SQL_SERVER%" "%DB_NAME%" "%CI_PROJECT_DIR%\src\SC2.Database\SubscriptionCentre2\Tables"'
  'CALL "%CI_PROJECT_DIR%\.gitlab\db\create.cmd" "%DB_USER%" "%DB_PSW%" "%SQL_SERVER%" "%DB_NAME%" "%CI_PROJECT_DIR%\src\SC2.Database\SubscriptionCentre2\Tables"'
  'CALL "%CI_PROJECT_DIR%\.gitlab\db\initial_data.cmd" "%DB_USER%" "%DB_PSW%"'

.deploy_template: &deploy_template
  stage: deploy
  script:
    - |
      $deployParams = @{
          source    = "$env:CI_PROJECT_DIR\$env:SOLUTION_FILE_NAME"
          siteName   = "$env:SITE_NAME"
          appPath    = "$env:APP_PATH"
          user       = "$DEPLOY_USER"
          passwd     = "$DEPLOY_PWD"
          server     = "$env:DEPLOY_SERVER1"
          port       = $env:DEPLOY_SERVER_PORT1
      }

      & ".\ms_deploy.ps1" @deployParams

      if($env:DEPLOY_SERVER2 -and $env:DEPLOY_SERVER_PORT2) {
        $deployParams.server = $env:DEPLOY_SERVER2
        $deployParams.port = $env:DEPLOY_SERVER_PORT2

        & ".\ms_deploy.ps1" @deployParams
      }

      if ($LASTEXITCODE -ne 0) {
        Write-Host "Deployment failed with exit code $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
      }

build_merge_request:
  tags:
    - vietnam-dev-shell
  stage: build
  only:
    refs:
      - merge_requests
    variables:
      - '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
  script:
    - '"%MSBUILD_EXE%" "%CI_PROJECT_DIR%\%SOLUTION_FILE_NAME%" /p:Configuration=Release'

build_qa:
  tags:
    - vietnam-dev-shell
  stage: build
  only:
    refs:
      - master
  script:
    - '"%MSBUILD_EXE%" "%CI_PROJECT_DIR%\%SOLUTION_FILE_NAME%" /p:Configuration=Release'

build_gamma:
  tags:
    - ee-buildtest-shell
  stage: build
  only:
    refs:
      - master
  when: manual
  script:
    - '"%MSBUILD_EXE%" "%CI_PROJECT_DIR%\%SOLUTION_FILE_NAME%" /p:Configuration=Release'

build_production:
  tags:
    - ee-buildtest-shell
  stage: build
  only:
    refs:
      - tags
  script:
    - '"%MSBUILD_EXE%" "%CI_PROJECT_DIR%\%SOLUTION_FILE_NAME%" /p:Configuration=Release'

deploy_qa:
  <<: *deploy_template
  tags:
    - vietnam-buildtest-powershell
  dependencies:
    - build_qa
  variables:
    BUILD_ENV: 'QA'
    SQL_SERVER: 'ee-v-sqltest'
    DEPLOY_SERVER1: 'BINGO'
    DEPLOY_SERVER_PORT1: 8172
    SITE_NAME: 'tools site'
    APP_PATH: '/tools/fragulizer'
  only:
    refs:
      - master
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:DEV_VN_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:DEV_VN_DEPLOY_PSW"

deploy_gamma:
  <<: *deploy_template
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build_gamma
  variables:
    BUILD_ENV: 'Gamma'
    SQL_SERVER: 'ee-v-sqltest'
    DEPLOY_SERVER1: 'ee-v-gamma1.euroland.com'
    DEPLOY_SERVER_PORT1: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/fragulizer'
  only:
    refs:
      - master
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:GAMMA_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:GAMMA_DEPLOY_PSW"
  when: manual

"deploy_staging":
  <<: *deploy_template
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build_gamma
  variables:
    BUILD_ENV: 'Production'
    SQL_SERVER: 'ee-v-sqltest'
    DEPLOY_SERVER1: 'ee-v-webcat161.euroland.com'
    DEPLOY_SERVER_PORT1: 8172
    DEPLOY_SERVER2: 'ee-v-webcat151.euroland.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'staging-site'
    APP_PATH: '/tools/fragulizer'
  only:
    refs:
      - master
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
  when: manual

"deploy_ee-v-webcat151,161":
  <<: *deploy_template
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build_production
  variables:
    BUILD_ENV: 'Production'
    SQL_SERVER: 'ee-v-sqltest'
    DEPLOY_SERVER1: 'ee-v-webcat161.euroland.com'
    DEPLOY_SERVER_PORT1: 8172
    DEPLOY_SERVER2: 'ee-v-webcat151.euroland.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'production-site'
    APP_PATH: '/tools/fragulizer'
  only:
    refs:
      - tags
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"

"deploy_ne-web-ws1,2":
  <<: *deploy_template
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build_production
  variables:
    BUILD_ENV: 'Production'
    SQL_SERVER: 'ee-v-sqltest'
    DEPLOY_SERVER1: 'https://ne-web-haproxy.northeurope.cloudapp.azure.com'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: 'https://ne-web-haproxy.northeurope.cloudapp.azure.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/fragulizer'
  only:
    refs:
      - tags
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"

"ea-web-ws1,2":
  <<: *deploy_template
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build_production
  variables:
    BUILD_ENV: 'Production'
    SQL_SERVER: 'ee-v-sqltest'
    DEPLOY_SERVER1: 'https://ea-web-haproxy.eastasia.cloudapp.azure.com'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: 'https://ea-web-haproxy.eastasia.cloudapp.azure.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/fragulizer'
  only:
    refs:
      - tags
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"

"uae-web-ws1,2":
  <<: *deploy_template
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build_production
  variables:
    BUILD_ENV: 'Production'
    SQL_SERVER: 'ee-v-sqltest'
    DEPLOY_SERVER1: 'https://uaewebhaproxy1.uaenorth.cloudapp.azure.com'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: 'https://uaewebhaproxy1.uaenorth.cloudapp.azure.com'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/fragulizer'
  only:
    refs:
      - tags
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"

"ksa-web-ws1,2":
  <<: *deploy_template
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build_production
  variables:
    BUILD_ENV: 'Production'
    SQL_SERVER: 'ee-v-sqltest'
    DEPLOY_SERVER1: 'https://**************'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: 'https://**************'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/fragulizer'
  only:
    refs:
      - tags
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"

"cn-web-ws1,2":
  <<: *deploy_template
  tags:
    - ee-buildtest-powershell
  dependencies:
    - build_production
  variables:
    BUILD_ENV: 'Production'
    SQL_SERVER: 'ee-v-sqltest'
    DEPLOY_SERVER1: 'https://cn-web-haproxy.chinanorth.cloudapp.chinacloudapi.cn'
    DEPLOY_SERVER_PORT1: 8173
    DEPLOY_SERVER2: 'https://cn-web-haproxy.chinanorth.cloudapp.chinacloudapi.cn'
    DEPLOY_SERVER_PORT2: 8172
    SITE_NAME: 'Default Web Site'
    APP_PATH: '/tools/fragulizer'
  only:
    refs:
      - tags
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:STAGING_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:STAGING_DEPLOY_PWD"
