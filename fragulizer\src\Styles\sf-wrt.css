body{
	margin: 0px;
    color: #000000;
    background: transparent;
}
.hyperlink, .disclaimer-box a, .cookies-box a
{
    color:#ee6114;
    text-decoration: none;
}
.hyperlink:hover, .disclaimer-box a:hover, .cookies-box a:hover
{
    color:#ee6114;
    text-decoration: underline;
}
td.table-header{
  height: 25px !important;
  line-height: 25px !important;
  font-weight: bold;
  color:#000000;
  vertical-align: middle;
  border-top: 1px solid #ddd;
  background: #fafafa top left !important;
}
td.td-exchange-header{
    border-left: 1px solid #ddd;
}  
td.td-time-header, td.td-changepro-header{
  
}  
td.table-row{
    line-height: 25px !important;
    vertical-align: middle;
}
td.td-time, td.td-changepro {
} 
.wrapper{
    width: 680px;
}
.second-heading
{
    font-weight: bold;
}

#columnChartArea{
    width: 680px;
}
.EUCalendar-day-selected
{
    background-color:#ee6114 !important;
}
.custom-range-calendar
{
    border: 1px solid #ccc !important;
}

@media (max-width: 690px)
{
    /* market share */
    .market-share
    {
        width: 30%;
        float: left;
        margin-right: 0;
    }

    .pie-chart-container
    {
        padding-left: 0px;
        float: left;
        height: 200px;
    }

    #pie-chart
    {
        width: 100%;
    }
}