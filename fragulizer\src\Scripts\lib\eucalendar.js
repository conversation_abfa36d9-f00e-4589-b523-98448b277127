﻿
/*! EUCalendar version 2.2 - a calendar plugin heavily based on JSCalendar at http://www.dynarch.com/jscal */
/******************************************************************* 
 * CHANGELOG:
 *   March 01, 2013 
 *    		FIXED BUG set focus when calling yearInput.focus(), this will cause un-editable on IOS 
 *    		Touch devices supported
 *   
 *   July 16, 2013
 *   		FIXED BUG incorrect position on IPhone
 *   
 *   November 25, 2016
 *      - version 2.0
 *      - Better support WCAG 2.0
 *      - Support touch devices by swipe left/right to change months, swipe up/down to change years
 *      - @TODO: write a wrapper class to support jQuery widget to make it a simpler setup 
 *              a calendar. 
 *	 July 07, 2020
 *		- internalParseDate(): Add rule %o for parsing into Date object as well. Also validate the valid range of day (1-31), month (0-11), year (0-9999);
 *		- 
********************************************************************/

(function(){
	/**
	 * Polyfill querySelector
	 * ref: https://github.com/inexorabletash/polyfill/blob/master/polyfill.js
	 */
	if (!document.querySelectorAll) {
	  document.querySelectorAll = function (selectors) {
		var style = document.createElement('style'), elements = [], element;
		document.documentElement.firstChild.appendChild(style);
		document._qsa = [];

		style.styleSheet.cssText = selectors + '{x-qsa:expression(document._qsa && document._qsa.push(this))}';
		window.scrollBy(0, 0);
		style.parentNode.removeChild(style);

		while (document._qsa.length) {
		  element = document._qsa.shift();
		  element.style.removeAttribute('x-qsa');
		  elements.push(element);
		}
		document._qsa = null;
		return elements;
	  };
	}

	if (!document.querySelector) {
	  document.querySelector = function (selectors) {
		var elements = document.querySelectorAll(selectors);
		return (elements.length) ? elements[0] : null;
	  };
	}
})();

(function($){

var win = window,
	VERSION = '2.2',
    doc = document,
    docElm = document.documentElement,
    body = document.body,
    toString = Object.prototype.toString,
    isObject = function (obj) {
        return toString.call(obj) === '[object Object]'
    },

    isDOM = function (el) {
        return typeof HTMLElement === "object"
            ? el instanceof HTMLElement
            : el && typeof el === "object" && el !== null && el.nodeType === 1 && typeof el.nodeName === "string";
    },

    isArray = function (arr) {
        return toString.call(arr) === '[object Array]'
    },

    isFunction = function (func) {
        return toString.call(func) === '[object Function]';
    },

    isString = function (str) {
        return toString.call(str) === '[object String]';
    },

    isIE = navigator.userAgent.match(/Trident/);

/**
    EUCalendar
*/

function dotNet2CalendarFormat(dotNetDateFormat) {
    if(!dotNetDateFormat)
            return '%d/%m/%Y';
        
    return (dotNetDateFormat+'').replace(/(MMMM|MMM|MM|M|dddd|ddd|dd|d|yyyyy|yyyy|yyy|yy|HH|H|hh|h|mm|m|ss|s|tt|t)/g, function(match, capture){
        switch(capture){
            case 'MMMM': return '%B'; 
            case 'MMM': return '%b';
            case 'MM': return '%m'; 
            case 'M': return '%o'; 
            case 'dddd': return '%A'; 
            case 'ddd': return '%a';
            case 'dd': return '%d'; 
            case 'd': return '%e'; 
            case 'yyyyy':
            case 'yyyy': 
            case 'yyy':
                return '%Y'; 
            case 'yy': return '%y'; 
            case 'HH': return '%H'; 
            case 'H': return '%k'; 
            case 'hh': return '%I'; 
            case 'h': return '%l'; 
            case 'mm':
            case 'm': 
                return '%M';
            case 'tt': return '%p'; 
            case 't': return '%P'; 
			case 'ss': 
			case 's':
				return '%S';
        }
    }); 
}

win.Calendar = function () {
    var touchIsSupported = ('ontouchstart' in document),
		isIOS = /iP(ad|hone|od)/.test(navigator.userAgent),
		isIOSWithBadTarget = isIOS && (/OS ([6-9]|\d{2})_\d/).test(navigator.userAgent),
        FAST_CLICK = 'EUCalendarFastClick',
        SWIPE_LEFT = 'EUCalendarSwipeLeft',
        SWIPE_RIGHT = 'EUCalendarSwipeRight',
        SWIPE_UP = 'EUCalendarSwipeUp',
        SWIPE_DOWN = 'EUCalendarSwipeDown',
        _calendarObjects = [],
        currentActiveCalendar; // Global cache contains the calendar
	
	/**
	 * Convert .NET datetime format to JSCalendar format
	 * @param {string} dotNetDateFormat The datetime format in .NET format
	 * @return {string} The format of JSCalendar
	 */

    var KeyCode = {
		BACKSPACE: 8,
		TAB: 9,
		ENTER: 13,
		ESC: 27,
		SPACE: 32,
		PAGE_UP: 33,
		PAGE_DOWN: 34,
		END: 35,
		HOME: 36,
		LEFT: 37,
		UP: 38,
		RIGHT: 39,
		DOWN: 40,
		DELETE: 46
	};

    function debounce(func, wait, immediate) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            var later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    };

    function extend(a, b) {
		var n;
		if (!a) {
			a = {};
		}
		for (n in b) {
			a[n] = b[n];
		}
		return a;
	};
    
    function getCss(element, prop) {
        if (element.currentStyle) {
            // convert style name to camelcase
            prop = prop.replace(/[-](\w)/g, function (str, capture) { return capture.toUpperCase(); });

            return isString(prop) ? element.currentStyle[prop] : element.currentStyle;
        } else if (window.getComputedStyle) {
            return isString(prop) ? window.getComputedStyle(element, null).getPropertyValue(prop) : window.getComputedStyle(element);
        } else {
            return isString(prop) ? element.style[prop] : element.style;
        }
    }

    function srcElement(evt) {
        if (isIOSWithBadTarget) {
            /*touch = event.changedTouches[0];
            targetElement = document.elementFromPoint(touch.pageX - window.pageXOffset, touch.pageY - window.pageYOffset);*/
        }
        return evt.target || evt.srcElement;
    }
    function defaultNumberConverter(num) { return num; };

    function getElement(elementId) {
        typeof elementId == "string" && (elementId = document.getElementById(elementId));
        return elementId;
    }
    function bk(a, b, c) {
        for (c = 0; c < a.length; ++c) b(a[c])
    }
    function getDocumentDimension() {
        var a = document.documentElement, b = document.body;
        return {
            x: a.scrollLeft || b.scrollLeft,
            y: a.scrollTop || b.scrollTop,
            w: a.clientWidth || window.innerWidth || b.clientWidth,
            h: a.clientHeight || window.innerHeight || b.clientHeight
        }
    }

    // Old function position() to get offset position relative to document
    function noConflictPostion(a) {
            var b = 0, c = 0, d = /^div$/i.test(a.tagName), e, f;
            d && a.scrollLeft && (b = a.scrollLeft),
            d && a.scrollTop && (c = a.scrollTop),
            e = { x: a.offsetLeft - b, y: a.offsetTop - c },
            a.offsetParent && (f = position(a.offsetParent), e.x += f.x, e.y += f.y);
            return e;   
        }

    function offsetPosition(element) {
        var docElem, win = window, box = { top: 0, left: 0 },
		elem = element,
		doc = elem && elem.ownerDocument;

        docElem = doc.documentElement;

        // If we don't have gBCR, just use 0,0 rather than error
        // BlackBerry 5, iOS 3 (original iPhone)
        if (typeof elem.getBoundingClientRect !== 'undefined') {
            box = elem.getBoundingClientRect();
        }
        return {
            top: box.top + (win.pageYOffset || docElem.scrollTop) - (docElem.clientTop || 0),
            left: box.left + (win.pageXOffset || docElem.scrollLeft) - (docElem.clientLeft || 0)
        };
    }

    var getOffsetParent = function(el) {
        var offsetParent = el.offsetParent || docElm;

        while (offsetParent && offsetParent.nodeName.toLowerCase() != "html" && getCss(offsetParent, "position") == "static") {
            offsetParent = offsetParent.offsetParent;
        }
        return offsetParent || docElm;
    }

    /**
     * Get element's position relative to parent
     */
    function position(element) {
        var offsetParent, offset, left, top,
			parentOffset = { top: 0, left: 0 };

        // fixed elements are offset from window (parentOffset = {top:0, left: 0}, because it is it's only offset parent
        if (getCss(element, "position") === "fixed") {
            // we assume that getBoundingClientRect is available when computed position is fixed
            offset = element.getBoundingClientRect();
        } else {
            // Get *real* offsetParent
            offsetParent = getOffsetParent(element);

            // Get correct offsets
            offset = offsetPosition(element) ;//this.offset();
            if (offsetParent && offsetParent.nodeName.toLowerCase() !== "html") {
                parentOffset = offsetPosition(offsetParent);
            }

            top = parseFloat(getCss(offsetParent, "border-top-width"));
            left = parseFloat( getCss(offsetParent, "border-left-width"));
            // Add offsetParent borders
            parentOffset.top += (!isNaN(top) ? top : 0);
            parentOffset.left += (!isNaN(left) ? left : 0);
        }

        // Subtract parent offsets and element margins
        // note: when an element has margin: auto the offsetLeft and marginLeft
        // are the same in Safari causing offset.left to incorrectly be 0
        top = parseFloat(getCss(element, "margin-top"));
        left = parseFloat(getCss(element, "margin-left"))
        return {
            y: offset.top - parentOffset.top - (!isNaN(top) ? top : 0),
            x: offset.left - parentOffset.left - (!isNaN(left) ? left : 0)
        };
    }

    /**
     * Contats the input strings
     * @param {String Array}
     * @return {String}
     */
    function concat() {
        return arguments.length ? Array.prototype.slice.call(arguments, 0).join('') : '';
    }

    function bh(a, b) {
        var c = e ? a.clientX + document.body.scrollLeft : a.pageX,
            d = e ? a.clientY + document.body.scrollTop : a.pageY;

        b && (c -= b.x, d -= b.y);
        return { x: c, y: d }
    }
    function setDisplayStyle(a, b) {
        var c = a.style; 
        if(b != null)
            c.display = b ? "" : "none";
        
        if(c.display == 'none') {
            a.setAttribute('hidden', true);
            return false;
        } else {
            a.removeAttribute('hidden');
            return true;
        }
    }

    function setOpacity(a, b) {
        b === "" ? e 
            ? a.style.filter = "" 
            : a.style.opacity = "" 
            : b != null 
            ? e 
            ? a.style.filter = "alpha(opacity=" + b * 100 + ")" 
            : a.style.opacity = b 
            : e 
            ? /alpha\(opacity=([0-9.])+\)/.test(a.style.opacity) && (b = parseFloat(RegExp.$1) / 100) 
            : b = parseFloat(a.style.opacity);
        return b;
    }

    function animate(a, b, c) {
        function h() {
            var b = a.len; a.onUpdate(c / b, d), c == b && g(), ++c
        }
        function g() {
            b && (clearInterval(b), b = null), a.onStop(c / a.len, d)
        }
        function f() {
            b && g(), c = 0, b = setInterval(h, 1e3 / a.fps)
        }
        function d(a, b, c, d) {
            return d ? c + a * (b - c) : b + a * (c - b)
        }
        a = U(a, { fps: 50, len: 15, onUpdate: bl, onStop: bl }), e && (a.len = Math.round(a.len / 2)), f();
        return {
            start: f, stop: g, update: h, args: a, map: d
        }
    }
    function bc(a, b) {
        if (!b(a)) for (var c = a.firstChild; c; c = c.nextSibling) c.nodeType == 1 && bc(c, b)
    }

    function proxy(a, b) {
        var c = ba(arguments, 2);
        return b == undefined ? function () {
            return a.apply(this, c.concat(ba(arguments)))
        } : function () {
            return a.apply(b, c.concat(ba(arguments)))
        }
    }
    function ba(a, b) {
        b == null && (b = 0); var c, d, e; try {
            c = Array.prototype.slice.call(a, b)
        } catch (f) {
            c = Array(a.length - b); for (d = b, e = 0; d < a.length; ++d, ++e) c[e] = a[d]
        }
        return c
    }
    function insertAfter(newNode, referenceNode) {
        referenceNode.parentNode.insertBefore(newNode, referenceNode.nextSibling);
    }
    function createElement(tagName, cssClass, parent) {
        var el = null;
        document.createElementNS ? el = document.createElementNS("http://www.w3.org/1999/xhtml", tagName) : el = document.createElement(tagName),
        cssClass && (el.className = cssClass), parent && parent.appendChild(el);
        return el;
    }
    function $(a, b, c) {
        if (b instanceof Array) for (var d = b.length; --d >= 0; ) $(a, b[d], c); else addClasses(b, c, a ? c : null); return a
    }
    function addClass(a, b) {
        return addClasses(a, b, b)
    }

    function isElementFocusable(elm) {
        if(!elm)
            return false;
        var tabindex = parseInt(elm.getAttribute('tabindex'));
        if(/input|select|textarea|button|object/i.test(elm.nodeName || elm.tagName)) {
            if(elm.disabled)
                return false;
        } else if(isNaN(tabindex)) {
            return false;
        }

        return true;
    }

    function addClasses(a, b, c) {
        if (a) {
            var d = a.className.replace(/^\s+|\s+$/, "").split(/\x20/),
                e = [],
                f;
            for (f = d.length; f > 0; )
                d[--f] != b && e.push(d[f]);
            c && e.push(c), a.className = e.join(" ")
        }

        return c
    }

    function createDefaultEvent(evt, eventType) {
        return evt 
            ? evt
            : document.createEventObject 
            ? document.createEventObject() 
            : document.createEvent('HTMLEvents');
    }

    function preventDefault(evt) {
        evt = createDefaultEvent(evt);
        evt.preventDefault ? evt.preventDefault() : (evt.returnValue = false, evt.defaultPrevented = true);
    }

    function cancelEvent(a) {
        a = createDefaultEvent(a);
        
		if(typeof a.cancelBubble != 'undefined') // IE
            a.cancelBubble = true; 
        if(typeof a.returnValue != 'undefined')
            a.returnValue = false;
        if(typeof a.preventDefault == 'function')
            a.preventDefault();
        if(typeof a.stopPropagation == 'function')
            a.stopPropagation();
        return false;
    }
    function removeEvent(a, b, c, d) {
        if (a instanceof Array)
            for (var f = a.length; --f >= 0; )
                removeEvent(a[f], b, c);
        else if (typeof b == "object")
            for (var f in b)
                b.hasOwnProperty(f) && removeEvent(a, f, b[f], c);
        else
            a.removeEventListener
				? a.removeEventListener(b, c, e ? !0 : !!d)
				: a.detachEvent
				? a.detachEvent("on" + b, c)
				: a["on" + b] = null;
    }
    function addEvent(a, b, c, d) {
        if (a instanceof Array)
            for (var f = a.length; --f >= 0; )
                addEvent(a[f], b, c, d);
        else if (typeof b == "object")
            for (var f in b)
                b.hasOwnProperty(f) && addEvent(a, f, b[f], c);
        else
            a.addEventListener
			? a.addEventListener(b, c, e ? !0 : !!d)
			: a.attachEvent
			? a.attachEvent("on" + b, c)
			: a["on" + b] = c;
    }

    function U(a, b, c, d) {
        d = {};
        for (c in b)
            b.hasOwnProperty(c) && (d[c] = b[c]);
        for (c in a)
            a.hasOwnProperty(c) && (d[c] = a[c]);
        return d
    }
    function T(a) {
        if (/\S/.test(a)) {
            a = a.toLowerCase();
            function b(b) {
                for (var c = b.length; --c >= 0; )
                    if (b[c].toLowerCase().indexOf(a) == 0)
                        return c + 1
                }
                return b(L("smn")) || b(L("mn"))
            }
        }
        function S(a) {
            if (a) {
                if (typeof a == "number")
                    return intToDate(a);
                if (!(a instanceof Date)) {
                    var b = a.split(/-/);
                    return new Date(parseInt(b[0], 10), parseInt(b[1], 10) - 1, parseInt(b[2], 10), 12, 0, 0, 0)
                }
            }

            return a
        }
		
		function outOfRange(value, low, high) {
			return value < low || value > high;
		}
		
		function internalParseDate(str, format) {
            //str = str.toEnglish();
            var today = new Date();
            var result = new Date();
            var y = null;
            var m = null;
            var d = null;
            var hr = 0;
            var min = 0;
            var sec = 0;
            var msec = 0;

            var a = format.match(/%.|[^%]+/g);
            for (var i = 0; i < a.length; i++) {
                if (a[i].charAt(0) == '%') {
                    switch (a[i]) {
                        case '%%':

                        case '%t':
                        case '%n':

                        case '%u':
                        case '%w':
                            str = str.substr(1);
                            break;


                            str = str.substr(1);
                            break;

                        case '%U':
                        case '%W':
                        case '%V':
                            var wn
                            if (wn = str.match(/^[0-5]?\d/)) {
                                str = str.substr(wn[0].length);
                            }
                            break;

                        case '%C':
                            var century;
                            if (century = str.match(/^\d{1,2}/)) {
                                str = str.substr(century[0].length);
                            }
                            break;

                        case '%A':
                        case '%a':
                            var weekdayNames = (a[i] == '%a') ? L('sdn') : L('dn');
                            for (j = 0; j < 7; ++j) {
                                if (str.substr(0, weekdayNames[j].length).toLowerCase() == weekdayNames[j].toLowerCase()) {
                                    str = str.substr(weekdayNames[j].length);
                                    break;
                                }
                            }
                            break;

                        case "%d":
                        case "%e":
                            if (d = str.match(/^[0-3]?\d/)) {
                                str = str.substr(d[0].length);
                                d = parseInt(d[0], 10);
								if (outOfRange(d, 1, 31)) d = null;
                            }
                            break;
						
						case "%o":
                        case "%m":
                            if (m = str.match(/^[01]?\d/)) {
                                str = str.substr(m[0].length);
                                m = parseInt(m[0], 10) - 1;
								if (outOfRange(m, 0, 11)) m = null;
                            }
                            break;

                        case "%Y":
                        case "%y":
                            if (y = str.match(/^\d{2,4}/)) {
                                str = str.substr(y[0].length);
                                y = parseInt(y[0], 10);
                                if (y < 100) {
                                    y += (y > 29) ? 1900 : 2000;
                                }
								
								if (outOfRange(y, 0, 9999)) y = null;
                            }
                            break;

                        case "%b":
                        case "%B":
                            var monthNames = (a[i] == '%b') ? L('smn') : L('mn');

                            for (j = 0; j < 12; ++j) {
                                if (str.substr(0, monthNames[j].length).toLowerCase() == monthNames[j].toLowerCase()) {
                                    str = str.substr(monthNames[j].length);
                                    m = j;
                                    break;
                                }
                            }
							if (outOfRange(m, 0, 11)) m = null;
                            break;

                        case "%H":
                        case "%I":
                        case "%k":
                        case "%l":
                            if (hr = str.match(/^[0-2]?\d/)) {
                                str = str.substr(hr[0].length);
                                hr = parseInt(hr[0], 10);
                            }

                            break;

                        case "%P":
                        case "%p":
                            if (/^pm/i.test(str)) {
                                if (h < 12)
                                    h += 12;
                                str = str.substr(2);
                            }

                            if (/^am/i.test(str)) {
                                if (h >= 12)
                                    h -= 12;
                                str = str.substr(2);
                            }

                            break;

                        case "%M":
                            if (min = str.match(/^[0-5]?\d/)) {
                                str = str.substr(min[0].length);
                                min = parseInt(min[0], 10);
                            }
                            break;

                        case "%S":
                            if (sec = str.match(/^[0-5]?\d/)) {
                                str = str.substr(sec[0].length);
                                sec = parseInt(sec[0], 10);
                            }
                            break;

                        case "%s":
                            var time;
                            if (time = str.match(/^-?\d+/)) {
                                return new Date(parseInt(time[0], 10) * 1000);
                            }
                            break;

                        default:
                            str = str.substr(2);
                            break;
                    }
                } else {
                    str = str.substr(a[i].length);
                }
            }

            /*if (y == null || isNaN(y)) y = today.getFullYear();
            if (m == null || isNaN(m)) m = today.getMonth();
            if (d == null || isNaN(d)) d = today.getDate();
            if (hr == null || isNaN(hr)) hr = today.getHours();
            if (min == null || isNaN(min)) min = today.getMinutes();
            if (sec == null || isNaN(sec)) sec = today.getSeconds();*/
            
            if (y==null || isNaN(y) || m==null || isNaN(m) || d==null || isNaN(d))
                return null;
            
            result.setFullYear(y, m, d);

            if(hr && !isNaN(hr))
                result.setHours(hr, min||0, sec||0, msec||0); 
			else 
				result.setHours(0, 0, 0, 0);
            return result;
            
        }
		
        function printDate(a, b) {
            var c = a.getMonth(),
                d = a.getDate(),
                e = a.getFullYear(),
                f = M(a),
                g = a.getDay(),
                h = a.getHours(),
                i = h >= 12,
                j = i ? h - 12 : h,
                k = N(a),
                l = a.getMinutes(),
                m = a.getSeconds(),
                n = /%./g,
                o;

            j === 0 && (j = 12),
            
            o = {
                "%a": L("sdn")[g],
                "%A": L("dn")[g],
                "%b": L("smn")[c],
                "%B": L("mn")[c],
                "%C": 1 + Math.floor(e / 100),
                "%d": d < 10 ? "0" + d : d,
                "%e": d,
                "%H": h < 10 ? "0" + h : h,
                "%I": j < 10 ? "0" + j : j,
                "%j": k < 10 ? "00" + k : k < 100 ? "0" + k : k,
                "%k": h,
                "%l": j,
                "%m": c < 9 ? "0" + (1 + c) : 1 + c,
                "%o": 1 + c,
                "%M": l < 10 ? "0" + l : l,
                "%n": "\n",
                "%p": i ? "PM" : "AM",
                "%P": i ? "pm" : "am",
                "%s": Math.floor(a.getTime() / 1e3),
                "%S": m < 10 ? "0" + m : m,
                "%t": "\t",
                "%U": f < 10 ? "0" + f : f,
                "%W": f < 10 ? "0" + f : f,
                "%V": f < 10 ? "0" + f : f,
                "%u": g + 1,
                "%w": g,
                "%y": ("" + e).substr(2, 2),
                "%Y": e,
                "%%": "%"
            };
            return toLocalDigit()(b.replace(n, function (a) { return o.hasOwnProperty(a) ? o[a] : a }))
        }
        function Q(a, b, c) {
            var d = a.getFullYear(),
                e = a.getMonth(),
                f = a.getDate(),
                g = b.getFullYear(),
                h = b.getMonth(),
                i = b.getDate();
            return d < g ? -3 : d > g ? 3 : e < h ? -2 : e > h ? 2 : c ? 0 : f < i ? -1 : f > i ? 1 : 0
        }

        // Convert a number to Date object
        // @param {Number} dateNum The value to (in format YYYYMMDD) to convert
        // @param {Number} hour The hour of the day
        // @param {Number} minute The minute of a hour
        // @param {Number} second The second of a minute
        // @param {Number} milisecond The milisecond of a second
        function intToDate(dateNum, hour, minute, second, milisecond) {
            if (!(dateNum instanceof Date)) { 
                dateNum = parseInt(dateNum, 10); 
                var f = Math.floor(dateNum / 1e4); 
                dateNum = dateNum % 1e4; 
                
                var g = Math.floor(dateNum / 100); 
                dateNum = dateNum % 100, 
                dateNum = new Date(f, g - 1, dateNum, hour == null ? 12 : hour, minute == null ? 0 : minute, second == null ? 0 : second, milisecond == null ? 0 : milisecond);
            } 
            return dateNum;
        }

        // Convert a date to number (in YYYYMMDD format)
        // @param {Date Object} dateObj The date to convert
        // @return the number 
        function dateToInt(dateObj) {
            if (dateObj instanceof Date)
                return 1e4 * dateObj.getFullYear() + 100 * (dateObj.getMonth() + 1) + dateObj.getDate();
            if (typeof dateObj == "string")
                return parseInt(dateObj, 10);
            return dateObj;
        }
        function N(a) {
            a = new Date(a.getFullYear(), a.getMonth(), a.getDate(), 12, 0, 0);
            var b = new Date(a.getFullYear(), 0, 1, 12, 0, 0), c = a - b; return Math.floor(c / 864e5)
        }
        function M(a) {
            a = new Date(a.getFullYear(), a.getMonth(), a.getDate(), 12, 0, 0); 
            var b = a.getDay(); 
            a.setDate(a.getDate() - (b + 6) % 7 + 3); 
            var c = a.valueOf(); 
            a.setMonth(0);
            a.setDate(4); 
            return Math.round((c - a.valueOf()) / 6048e5) + 1
        }
        function L(a, b) {
            var c = i.__.data[a]; 
            b && typeof c == "string" && (c = formatString(c, b)); 
            return c;
        }

        function toLocalDigit() {
            return typeof (L('toLocalDigit')) == 'function' ? L('toLocalDigit') : function (num) { return num; };
        }

        function fromLocalDigit() {
            return typeof (L('fromLocalDigit')) == 'function' ? L('fromLocalDigit') : function (num) { return num; };
        }

        function formatString(a, b) {
            return a.replace(/\$\{([^:\}]+)(:[^\}]+)?\}/g, function (a, c, d) { 
                var e = b[c]; f; 
                if(d) {
                    f = d.substr(1).split(/\s*\|\s*/), e = (e >= f.length ? f[f.length - 1] : f[e]).replace(/##?/g, function (a) { return a.length == 2 ? "#" : e })
                }; 
                return e;
            });
        }

        (function registerTouchEvents() {
            // Custom events for touch devices
            var fastClick,
                touchStartPosition = { x: 0, y: 0 },   // The position of the start touch
                touchEndPosition = { x: 0, y: 0 }, // The position of the end touch
                docEvents,  // Global events for document object
                elementEvents, // To define the flag supports event handlers of "docEvents" recognize it's self events
                maxDistance = 50, // allow movement if < 1000 ms (1 sec)
                maxTimestamp = 100, // allow a tab is clickable if touch is < 200ms
                startTimestamp = 0,
                endTimestamp = 0;

            // Create a custom event
            // @param { Event object } evt Original event
            // @param { String } eventType The type of custom event
            function customEvent(evt, eventType) {
                var customEvent = document.createEvent("CustomEvent");
                customEvent.initCustomEvent(eventType, true, true, evt.target);
                evt.target.dispatchEvent(customEvent);
                customEvent = null;
                return false;
            }

            function getTouchPosition(evt) {

                if(evt.changedTouches) {
                    // Skip multiple touches
                    if(evt.changedTouches.length > 1)
                        return false;

                    return { x: evt.changedTouches[0].pageX, y: evt.changedTouches[0].pageY };
                }
                else {
                    return { x: evt.pageX, y: evt.pageY };
                }
            }

            function getTargetElementFromEvent(evt) {
                var touchPos = getTouchPosition(evt),
                    //eventTarget = document.elementFromPoint(touchPos.x - window.pageXOffset, touchPos.y - window.pageYOffset) || evt.target;
                    eventTarget = evt.target;
                // On some older browsers (notably Safari on iOS 4.1 - see issue #56) the event target may be a text node.
                if (eventTarget.nodeType === Node.TEXT_NODE) {
                    return eventTarget.parentNode;
                }

                return eventTarget;
            } 

            events = {
                touchstart: function(evt) {
                    var pos = getTouchPosition(evt);

                    touchStartPosition.x = pos.x;
                    touchStartPosition.y = pos.y;
                    startTimestamp = evt.timeStamp;
                    isFastClick = true;
                },
                touchmove: function(evt) {
                    var pos = getTouchPosition(evt);
                    touchEndPosition.x = pos.x;
                    touchEndPosition.y = pos.y;
                    isFastClick = false;
                },
                touchend: function(evt) {
                    endTimestamp = evt.timeStamp;
                    var x = touchEndPosition.x - touchStartPosition.x,
                            xr = Math.abs(x),
                            y = touchEndPosition.y - touchStartPosition.y,
                            yr = Math.abs(y);
                        

                    if(isFastClick || Math.max(xr, yr) < maxDistance) {
                        var targetElement = getTargetElementFromEvent(event);
                        if(document.activeElement && document.activeElement !== targetElement){
                            document.activeElement.blur();
                        }
                        customEvent(evt, FAST_CLICK);
                    } else {
                        customEvent(
                            evt, 
                            (
                                xr > yr 
                                ? (x < 0 ? SWIPE_LEFT : SWIPE_RIGHT)
                                : (y < 0 ? SWIPE_UP : SWIPE_DOWN)
                            )
                        );
                    }

                    isFastClick = false;
                    startTimestamp = 0;
                    endTimestamp = 0;
                    touchStartPosition.x = 0;
                    touchStartPosition.y = 0;
                    touchEndPosition.x = 0;
                    touchEndPosition.y = 0;
                },
                touchcancel: function(evt) {
                    isFastClick = false;
                    isFastClick = false;
                    startTimestamp = 0;
                    endTimestamp = 0;
                    touchStartPosition.x = 0;
                    touchStartPosition.y = 0;
                    touchEndPosition.x = 0;
                    touchEndPosition.y = 0;
                }
            };

            addEvent(document, events, true);
        })();

        function keypress(b) {
            b = createDefaultEvent(b);
            var c = srcElement(b), 
                d = c.getAttribute("dyc-btn"), 
                e = b.keyCode || b.which, 
                f = b.charCode || e, 
                g = H[e];

            if(e == KeyCode.TAB)
                return true;

            if (this._menuVisible) {
                if (e == KeyCode.ESC) { // Hide menu on escape
                    if(this._menuYearVisible)
                        showYearMenu(this, false);
                    else
                        showhideMenu(this, false);
                    return cancelEvent(b)
                } else if(e == KeyCode.ENTER || e == KeyCode.SPACE) {
                    A.call(this, false, b);
                } else if (e == KeyCode.LEFT) {
                    if(this._menuYearVisible) {

                    } else {

                    }
                }
            } else {
                if (e == KeyCode.ESC) { // Hide menu on escape
                    startBluring.call(this, b);
                    return false;
                }
                if(e == KeyCode.ENTER) {
                    if(/^[+-][MY]$/.test(d) || 'menu' == d) {
                        b.isKeyPressedOnNavBtn = true;
                        A.call(this, true, b);
                        return false;
                    }
                    if('today' == d) {
                        A.call(this, false, b);
                        return false;
                    }
                }
                

                b.ctrlKey || (g = null), g == null && !b.ctrlKey && (g = I[e]), e == 36 && (g = 0);
                if (g != null) {
                    y(this, g); return cancelEvent(b)
                }

                f = String.fromCharCode(f).toLowerCase();
                var i = this.els.yearInput, j = this.selection;

                if (f == " ") {
                    showhideMenu(this, true);
                    return cancelEvent(b)
                }
                if (f >= "0" && f <= "9") {
                    showhideMenu(this, true);
                    return cancelEvent(b)
                }

                var k = L("mn"), l = b.shiftKey ? -1 : this.date.getMonth(), m = 0, n;
                while (++m < 12) {
                    n = k[(l + m) % 12].toLowerCase();
                    if (n.indexOf(f) == 0) {
                        var h = new Date(0);
                        h.setMilliseconds(this.date.getTime());
                        h.setDate(1);
                        h.setMonth((l + m) % 12);
                        this.moveTo(h, !0); 
                        return cancelEvent(b);
                    }
                }

                if (e >= KeyCode.LEFT && e <= KeyCode.DOWN) {
                    var h = this._lastHoverDate;
                    if (!h && !j.isEmpty()) {
                        h = e < 39 ? j.getFirstDate() : j.getLastDate();
                        if (h < this._firstDateVisible || h > this._lastDateVisible) 
                            h = null
                    }

                    if(!h) {
                        var dycDate = parseInt(c.getAttribute('dyc-date'));
                        if(!isNaN(dycDate) && dycDate > 0)
                            h = intToDate(dycDate);
                    }

                    if (!h){
                        h = e < 39 ? this._lastDateVisible : this._firstDateVisible;
                    }
                    else {
                        var o = h; 
                        h = intToDate(h);
                        var l = 100;
                        while (l-- > 0) {
                            switch (e) {
                                case KeyCode.LEFT: h.setDate(h.getDate() - 1); break;
                                case KeyCode.UP: h.setDate(h.getDate() - 7); break;
                                case KeyCode.RIGHT: h.setDate(h.getDate() + 1); break;
                                case KeyCode.DOWN: h.setDate(h.getDate() + 7)
                            }
                            if (!this.isDisabled(h)) break
                        }

                        h = dateToInt(h);
                        if(!this.isDisabled(intToDate(h)) && (h < this._firstDateVisible || h > this._lastDateVisible))
                            this.moveTo(h);
                    }

                    if(!this.isDisabled(intToDate(h))) {
                        var prevHoverDateDiv = this._getDateDiv(o),
                            hoverDateDiv = this._getDateDiv(h);
                        if(!prevHoverDateDiv)
                            prevHoverDateDiv = this.els.body.querySelector('[tabindex="0"]');

                        if(prevHoverDateDiv)
                            prevHoverDateDiv.setAttribute('tabindex', -1);

                        addClasses(prevHoverDateDiv, addClass(hoverDateDiv, "EUCalendar-hover-date"));
                        if(hoverDateDiv){
                            hoverDateDiv.setAttribute('tabindex', 0);
                            setTimeout(function() {
                                hoverDateDiv.focus();
                            }, 10);
                        }
                        this._lastHoverDate = h;
                    }
                    else {
                        // Make animation at once
                        this.moveTo(h, true, function(el) {
                            if(el)
                                el.focus();
                        });
                    }

                    return cancelEvent(b);
                }
                
                if (e == KeyCode.ENTER && this._lastHoverDate) {
                    j.type == a.SEL_MULTIPLE && (b.shiftKey || b.ctrlKey) ? (b.shiftKey && this._selRangeStart && (j.clear(!0), j.selectRange(this._selRangeStart, this._lastHoverDate)), b.ctrlKey && j.set(this._selRangeStart = this._lastHoverDate, !0)) : j.reset(this._selRangeStart = this._lastHoverDate);
                    return cancelEvent(b);
                }
                if(e == KeyCode.ESC && !this.args.cont) 
                    this.hide();
            }
        }
        function G() {
            this.refresh();
            var a = this.inputField,
        b = this.selection;
            if (a) {
                var c = b.print(this.dateFormat);
                /input|textarea/i.test(a.tagName) ? a.value = c : a.innerHTML = c
            }
            this.callHooks("onSelect", this, b)
        }
        function F(a) {
            a =  createDefaultEvent(a);
            var b = C(a);
            if (b) {
                var c = b.getAttribute("dyc-btn"),
            d = b.getAttribute("dyc-type"),
            e = a.wheelDelta ? a.wheelDelta / 120 : -a.detail / 3;
                e = e < 0 ? -1 : e > 0 ? 1 : 0, this.args.reverseWheel && (e = -e);
                if (/^(time-(hour|min))/.test(d)) {
                    switch (RegExp.$1) {
                        case "time-hour": this.setHours(this.getHours() + e);
                            break;
                        case "time-min":
                            this.setMinutes(this.getMinutes() + this.args.minuteStep * e)
                    }
                    cancelEvent(a)
                }
                else
                    /Y/i.test(c) && (e *= 2), y(this, -e), cancelEvent(a)
            }
        }
        function E(a, b) {
            b = createDefaultEvent(b);
            var c = C(b);
            if (c) {
                var d = c.getAttribute("dyc-type");
                if (d && !c.getAttribute("disabled"))
                    if (!a || !this._bodyAnim || d != "date") {
                        var e = c.getAttribute("dyc-cls");
                        e = e ? D(e, 0) : "EUCalendar-hover-" + d;

                        if(d != "date" || this.selection.type) 
                            $(a, c, e);
                        
                        if(d == "date"){
                            $(a, c.parentNode.parentNode, "EUCalendar-hover-week");
                            this._showTooltip(c.getAttribute("dyc-date"));
                        };

                        if(/^time-hour/.test(d)) 
                            $(a, this.els.timeHour, "EUCalendar-hover-time");
                        
                        if(/^time-min/.test(d))
                             $(a, this.els.timeMinute, "EUCalendar-hover-time");
                        
                        addClasses(this._getDateDiv(this._lastHoverDate), "EUCalendar-hover-date");
                        
                        this._lastHoverDate = null;
                    }
            }
            a || this._showTooltip()
        }
        function D(a, b) {
            return "EUCalendar-" + a.split(/,/)[b]
        }
        function C(a) {
            var b = srcElement(a);
            c = b; 
            while (b && b.getAttribute && !b.getAttribute("dyc-type")) 
                b = b.parentNode; 
            //return b.getAttribute && b || c;
            if(b && b.getAttribute)
                return b;
            else 
                return c;
        }
        function B(a) {
            a = createDefaultEvent(a); 
            var b = this.els.topCont.style; 
            c = bh(a, this._mouseDiff); 
            b.left = c.x + "px";
            b.top = c.y + "px";
        }

        function moveToday() {
            var today = new Date(),
                argMin = this.args.min,
                argMax = this.args.max,
                min = argMin ? new Date(argMin.getFullYear(), argMin.getMonth(), argMin.getDate(), 0,0,0,0) : null,
                max = argMax ? new Date(argMax.getFullYear(), argMax.getMonth(), argMax.getDate(), 0,0,0,0) : null;
            
            today.setHours(0,0,0,0);

            if(min && today < min)
                today = min;
            if(max && today > max)
                today = max;
            if(!this._menuVisible && i.type == a.SEL_SINGLE) {
                i.set(today)
            }
            this.moveTo(today, true);
            showhideMenu(this, false);
        }

        function A(b, c) {
            c = createDefaultEvent(c);
            
            if(c.type && !/^touch.+/i.test(c.type)){
                if('buttons' in c && c.buttons != 1){
                    return;
                }
                else if ('which' in c && c.which < 4 && c.which != 1) {
                    return;
                }
                else if (c.button && c.button != 1) {
                    return;
                }
            }

            var d = C(c), self = this;
            if (d && !d.getAttribute("disabled")) {
                var f = d.getAttribute("dyc-btn"),
            g = d.getAttribute("dyc-type"),
            h = d.getAttribute("dyc-date"),
            year = d.getAttribute('dyc-year'),
            i = this.selection,
            j,
            k = {
                mouseover: cancelEvent,
                mousemove: cancelEvent,
                mouseup: function (a) {
                    var b = d.getAttribute("dyc-cls");
                    b && addClasses(d, D(b, 1)), clearTimeout(j), removeEvent(self.els.main, k, !0), k = null
                }
            };

            if(touchIsSupported) {
                k['touchend'] = function (a) {
                    var b = d.getAttribute("dyc-cls");
                    b && addClasses(d, D(b, 1)), clearTimeout(j), removeEvent(self.els.main, k, !0), k = null
                }
            }

            // Handle for keypress event as same as mouseup
            if(c.isKeyPressedOnNavBtn){
                k.keyup = function (a) {
                    var b = d.getAttribute("dyc-cls");
                    b && addClasses(d, D(b, 1)), clearTimeout(j), removeEvent(self.els.main, k, !0), k = null
                }
            }
               
                if (b && !this._menuVisible) {
                    //setTimeout(proxy(this.focus, this), 1);
                    var l = d.getAttribute("dyc-cls");
                    l && addClass(d, D(l, 1));
                    if ("menu" == f) {
                        this.toggleMenu();
                    }
                    else if (d && /^[+-][MY]$/.test(f)){
                        if (y(this, f)) {
                            var m = proxy(function () {
                                if(y(this, f, !0)) {
                                    j = setTimeout(m, 40);
                                } else {
                                    k[touchIsSupported ? 'touchend' : 'mouseup'](); 
                                    y(this, f);
                                }
                            }, this);
                            j = setTimeout(m, 350);
                            addEvent(this.els.main, k, !0);

                        }
                        else{
                            k[touchIsSupported ? 'touchend' : 'mouseup']();
                        }
                    }
                    else if (g == "time-am")
                        addEvent(this.els.main, k, !0);
                    else if (/^time/.test(g)) {
                        var m = proxy(function (a) {
                            w.call(this, a);
                            j = setTimeout(m, 100);
                        }, this, g);
                        w.call(this, g);

                        j = setTimeout(m, 350);
                        addEvent(this.els.main, k, !0);
                    }
                    else if(f == 'today') {
                        moveToday.call(this);
                    }
                    else
                        h && i.type && (
                            i.type == a.SEL_MULTIPLE ? c.shiftKey && this._selRangeStart ? i.selectRange(this._selRangeStart, h) : (!c.ctrlKey && !i.isSelected(h) && i.clear(!0),
                            i.set(h, !0),
                            this._selRangeStart = h) : (i.set(h),
                            this.moveTo(intToDate(h), 2)),
                            d = this._getDateDiv(h),
                            E.call(this, !0, { target: d })
                        );
                
                addEvent(this.els.main, k, !0);
                //e && k && /dbl/i.test(c.type) && k[touchIsSupported ? FAST_CLICK : 'mouseup'](),
            !this.args.fixed && /^(EUCalendar-(topBar|bottomBar|weekend|weekNumber|menu(-sep)?))?$/.test(d.className) && !this.args.cont && (k[touchIsSupported ? 'touchmove' : 'mousemove'] = proxy(B, this),
            this._mouseDiff = bh(c, position(this.els.topCont)), addEvent(this.els.main, k, !0));
                }
                else if ("year" == f) {
                    //this.els.yearInput.focus(), this.els.yearInput.select();
                    showYearMenu(this, true);
                }
                else if ("today" == f){
                    moveToday.call(this);
                }
                else if(this._menuVisible && f=='close') {
                    showhideMenu(this, false);
                }
                else if(this._menuVisible && f=='year-close') {
                    showYearMenu(this, false);
                }
                else if(/^y([0-9]+)$/.test(f)) {
                    //b.yearInput.value = toLocalDigit()(this.date.getFullYear())
                    var yearName = f.replace(/^y([0-9]+)$/, '$1'),
                        localYearName = toLocalDigit()(yearName);

                    this.els.yearInput.value = yearName;
                    this.els.yearLabel.firstChild.innerHTML = localYearName;
                    this.els.yearLabel.setAttribute('aria-label', (L('year') || 'year') + localYearName);

                    showYearMenu(this, false);
                }
                else if(/^[-+]MM$/.test(f)) {
                    year = parseInt(year);
                    var min, max;
                    if(f == '-MM') {
                        max = year - 1;
                        min = max - 11;
                    } else {
                        min = year + 1;
                        max = min + 11;
                    }
                    //showYearMenu(this, true, min, max);
                    moveYearMenuTo(this, min, max);
                }
                else if (/^m([0-9]+)/.test(f)) {
                    var h = new Date(0);
                        h.setMilliseconds(this.date.getTime());
                        h.setDate(1);
                        h.setMonth(RegExp.$1);
                        h.setFullYear(this._getInputYear());
                        this.moveTo(h, !0); 
                    showhideMenu(this, false);
                } else
                    g == "time-am" && this.setHours(this.getHours() + 12);
                
                if(d.getAttribute('tabindex'))
                    d.focus();

                return cancelEvent(c);
            }
        }

        function showhideMenu(a, visible) {
            a._menuVisible = visible,
            $(visible, a.els.title, "EUCalendar-pressed-title");

            if(typeof(visible) == 'undefined')
                visible = false;

            var c = a.els.menu,
                currentYear = a.date.getFullYear();

            a.els.title.setAttribute('aria-expanded', visible);
            
            //a.els.title.setAttribute('aria-label', printDate(a.date, a.args.ariaDateFormat.fullDate));
            //a.els.title.innerHTML = n(a);
            a.els.yearLabel.setAttribute('aria-label', (L('year') || 'year') + ' ' + toLocalDigit()(currentYear));
            a.els.yearLabel.innerHTML = '<div role="presentation">' + toLocalDigit()(currentYear) + '</div>';

            c.setAttribute('aria-expanded', visible);
            if(visible){
				c.removeAttribute('aria-hidden');
			} else {
				c.setAttribute('aria-hidden', true);
			}
            
            f && (c.style.height = a.els.main.offsetHeight + "px");

            function focusOnFirstDate() {
                var dateDiv = a.els.body.querySelector('[tabindex="0"]');
                if(dateDiv)
                    dateDiv.focus();
                else 
                    a.focus();
            }

            if (!a.args.animation) {
                setDisplayStyle(c, visible); 
                if(!visible) {
                    focusOnFirstDate();
                }
            }
            else {
                a._menuAnim && a._menuAnim.stop();
                var d = a.els.main.offsetHeight;
                
                f && (c.style.width = a.els.topBar.offsetWidth + "px");

                visible && (c.firstChild.style.marginTop = -d + "px", a.args.opacity > 0 && setOpacity(c, 0), setDisplayStyle(c, !0));

                a._menuAnim = animate({
                    onUpdate: function (e, f) {
                        c.firstChild.style.marginTop = f(be.accel_b(e), -d, 0, !visible) + "px"; 
                        a.args.opacity > 0 && setOpacity(c, f(be.accel_b(e), 0, 1, !visible));
                    },
                    onStop: function () {
                        a.args.opacity > 0 && setOpacity(c, 1);
                        c.firstChild.style.marginTop = "";
                        a._menuAnim = null; 
                        if(!visible){
                            setDisplayStyle(c, !1);
                            focusOnFirstDate();
                        } else {
                            a.els.yearLabel.focus();
                        }
                    }
                });
            }
        }

        function showYearMenu(cal, visible) {
            var year = cal._getInputYear();
                cal._menuYearVisible = visible,
                menuYearElm = cal.els.menuYear,
                mainHeight = cal.els.main.offsetHeight,
                opacityVal = 1;
            
            if(visible)
                moveYearMenuTo(cal, year-5, year + 6, true);

            if(f)
                menuYearElm.style.width = mainHeight + 'px';
            
            menuYearElm.setAttribute('aria-expanded', visible);
            //menuYearElm.setAttribute('aria-hidden', !visible);
			if(visible){
				menuYearElm.removeAttribute('aria-hidden');
			} else {
				menuYearElm.setAttribute('aria-hidden', true);
			}

            if(!cal.args.animation) {
                setDisplayStyle(menuYearElm, visible);
                if(visible) {
                    cal.els.decadeCont.parentNode.setAttribute('tabindex', '-1');
                    cal.els.decadeCont.parentNode.focus();
                    cal.els.decadeCont.parentNode.removeAttribute('tabindex', '-1');
                }
            } else {
                if(cal._menuYearAnimation)
                    cal._menuYearAnimation.stop();
                
                if(f)
                    menuYearElm.style.width = cal.els.topBar.offsetWidth + 'px';
                if(visible) {
                    menuYearElm.firstChild.style.marginTop = -mainHeight + 'px';
                    if(cal.args.opacity > 0)
                        setOpacity(menuYearElm, 0);
                    setDisplayStyle(menuYearElm, true);
                }

                // Begin animation
                cal._menuYearAnimation = animate({
                    onUpdate: function(e, f) {
                        menuYearElm.firstChild.style.marginTop = f(be.accel_b(e), -mainHeight, 0, !visible) + 'px';
                        if(cal.args.opacity > 0)
                            setOpacity(menuYearElm, f(be.accel_b(e), 0, opacityVal, !visible));
                    },
                    onStop: function(){
                        if(cal.args.opacity > 0)
                            setOpacity(menuYearElm, opacityVal);

                        menuYearElm.firstChild.style.marginTop = "";

                        cal._menuYearAnimation = null;
                        if(!visible) {
                            setDisplayStyle(menuYearElm, false);
                            focusFirstElement(cal.els.menu);
                        } else {
                            focusFirstElement(menuYearElm);
                        }
                    }
                });
            }

            // Set focus back to lower element
            if(!visible) {
                // Focus back to menu
                focusFirstElement(cal.els.menu);
            }
        }

        function y(a, b, c) {
            this._bodyAnim && this._bodyAnim.stop();
            var d;
            if (b != 0) {
                d = new Date(0);
                d.setMilliseconds(a.date.getTime());
                d.setDate(1);
                switch (b) {
                    case "-Y": case -2: d.setFullYear(d.getFullYear() - 1); break;
                    case "+Y": case 2: d.setFullYear(d.getFullYear() + 1); break;
                    case "-M": case -1: d.setMonth(d.getMonth() - 1); break;
                    case "+M": case 1: d.setMonth(d.getMonth() + 1)
                }
            }
            else
                d = new Date();

            return a.moveTo(d, !c);
        }
        function w(a) {
            switch (a) { case "time-hour+": this.setHours(this.getHours() + 1); break; case "time-hour-": this.setHours(this.getHours() - 1); break; case "time-min+": this.setMinutes(this.getMinutes() + this.args.minuteStep); break; case "time-min-": this.setMinutes(this.getMinutes() - this.args.minuteStep); break; default: return }
        }

        /** 
         * Check if an element is a node, which is a part of calendar (including trigger, inputField elements) 
         */
        function isCalendarStaff(node) {
            var trigger = this.trigger,
                inputField = this.inputField,
                main = this.els.main,
                check = false;
            
            check = trigger === node;

            if(!check)
                check = inputField === node;
            
            if(!check) {
                var checkNode = node.parentNode;
                while (checkNode != null) {
                    if (checkNode === main) {
                        check = true;
                        break;
                    }
                    checkNode = checkNode.parentNode;
                }
            }

            return check;
        }

        /**
         *  Hide the calendar if user click outside of calendar 
         */
        function hideOnClickOutsite(evt) {
            evt = createDefaultEvent(evt);
            var target = srcElement(evt);
            
            if((evt.defaultPrevented || evt.returnValue === false) && isCalendarStaff.call(this, target))
                return;

            startBluring.call(this);
        }

        function startBluring() {
            // Do nothing if calendar is inline
            if(this.args.cont || !this.isVisible)
                return;

            this._bluringTimeout && clearTimeout(this._bluringTimeout);
            this._bluringTimeout = setTimeout(proxy(startInternalBluring, this), 50);
        }
        function startInternalBluring() {
             this.focused = false;
            addClasses(this.els.main, "EUCalendar-focused");
            if(this._menuYearVisible) {
                showYearMenu(this, false);
            }
            if(this._menuVisible){
            	showhideMenu(this, false);
            }
            this.hide();
			this.triggerClicked = false;
            this.callHooks("onBlur", this);
        }

        function toggleCalendar(evt, target) {
            if(this.args.cont)
                return;
            evt = createDefaultEvent(evt);

            if(!this.isVisible){
                if (this.selection.type == a.SEL_SINGLE) {
                    var f, g, h, i;
                    f = /input|textarea/i.test(this.inputField.tagName) ? this.inputField.value : this.inputField.innerText || this.inputField.textContent,
                    f && (g = /(^|[^%])%[bBmo]/.exec(this.dateFormat),
                    h = /(^|[^%])%[de]/.exec(this.dateFormat), g && h && (i = g.index < h.index),

                    // check if calendar language is Arabic, 
                    // Convert number with arabic digits into latin digits
                    f = fromLocalDigit()(f),

                    //f = Calendar.parseDate(f, i),
                    f = internalParseDate(f, this.dateFormat) || new Date(),
                    f && (this.selection.set(f, !1, !0),
                    this.args.showTime && (this.setHours(f.getHours()),
                    this.setMinutes(f.getMinutes())),
                    this.moveTo(f)));
                }
                evt.preventDefault ? evt.preventDefault() : (evt.returnValue = false);
                this.popup(target || this.trigger || this.inputField);
            }
            else {
                cancelEvent(evt);
                startBluring.call(this);
            }
        }

        /**
         * Tries to set attribute tabindex="0" to first element
         */
        function setFocusableForFistElement(container) {
            // Try to find selected date first most
            //EUCalendar-day-selected
            var focusableElm = container.querySelector('.EUCalendar-day-selected');
            
            if(!focusableElm) {
                focusableElm = container.querySelector('[tabindex="0"]');
            }
            if(!focusableElm) {
                focusableElm = container.querySelector('[tabindex]');
            }

            if(focusableElm) {
                focusableElm.setAttribute('tabindex', '0');
            }

            return focusableElm;
        }

        /**
         * Tries to focus on the first element which is tabable (with attriute tabindex="0")
         */
        function focusFirstElement(container) {
            var focusableElm = container.querySelector('[tabindex="0"]');
            if(!focusableElm) {
                focusableElm = container.querySelector('[tabindex]');
            }

            if(focusableElm) {
                setTimeout(function() {
                    focusableElm.focus();
                }, 100);
            }

            return focusableElm;
    }

        /* 
         * guard against infinite focus loop
         */
        function infiniteFocusLoop(evt) {
            var container = this.els.topCont;
            if (this.isVisible && document !== evt.target && container !== evt.target && !container.contains(evt.target)) {
                this.focus();
            }
        }

        function startInternalFocusing() {
            this._bluringTimeout && clearTimeout(this._bluringTimeout);
            this.focused = true;
            
            addClass(this.els.main, "EUCalendar-focused");
            focusFirstElement(this.els.main);
            setFocusableForFistElement(this.els.body);

            this.callHooks("onFocus", this);
        }

        // Support on touch devices in order to swipe to left/right to switch between months or up/down to switch between years
        function swipe(direction, evt) {
            cancelEvent(evt);
            switch (direction.toLowerCase()) {
                case 'swipe-left':
                    if(this._menuVisible) {
                        if(this._menuYearVisible) {
                            var minYear = parseInt(this.els.navNextDecade.getAttribute('dyc-year')) + 1,
                                maxYear = minYear + 11;
                            
                            var customEvent = document.createEvent("CustomEvent");
                                customEvent.initCustomEvent('mousedown', true, true, this.els.navNextDecade);
                                this.els.navNextDecade.dispatchEvent(customEvent);
                            //moveYearMenuTo(this, minYear, maxYear);
                        }
                    } else {
                        y(this, '+M', false);
                    }
                    break;
                case 'swipe-right':
                    if(this._menuVisible) {
                        if(this._menuYearVisible) {
                            var maxYear = parseInt(this.els.navPrevDecade.getAttribute('dyc-year')) - 1,
                                minYear = maxYear - 11;
                            //moveYearMenuTo(this, minYear, maxYear);
                            var customEvent = document.createEvent("CustomEvent");
                                customEvent.initCustomEvent('mousedown', true, true, this.els.navPrevDecade);
                                this.els.navPrevDecade.dispatchEvent(customEvent);
                        }
                    } else {
                        y(this, '-M', false);
                    }
                    break;
                case 'swipe-up':
                    y(this, '+Y', false);
                    break;
                case 'swipe-down':
                    y(this, '-Y', false);
                    break;
            }
        }

        // Init Event
        function s(a) {
            var b = createElement("div"), c = a.els = {}, d;
            
            d = {
                mousedown: proxy(A, a, true),
                //mouseup: proxy(A, a, false),
                mouseover: proxy(E, a, true),
                mouseout: proxy(E, a, false),
                keypress: proxy(keypress, a),
                keydown: proxy(keypress, a),
                click: proxy(function(evt){ 
                    return cancelEvent(evt);
                }, a)
            };
            if(touchIsSupported) {
                d = extend(d, {
                    touchstart: proxy(function(value, evt){
                        // Add class '*-hover' to the button
                        evt.preventDefault();
                        var calendar = this;
                        E.apply(calendar, arguments);
                    }, a, true),
                    touchend: proxy(function(value, evt){
                        // Remove class '*-hover' to the button
                        evt.preventDefault();
                        var calendar = this;
                        E.apply(calendar, arguments);
                    }, a, false),
                    touchmove: proxy(function(value, evt){
                        // Cancel page scrolled when swipe the calendar
                        evt.preventDefault();
                    }, a, false)
                });
                
                // Bind simulation "click" event to the calendar buttons  
                d[FAST_CLICK] = proxy( A, a, true);
            }

            a.args.noScroll || (d[g ? "DOMMouseScroll" : "mousewheel"] = proxy(F, a));
            //e && (d.dblclick = d[touchIsSupported ? FAST_CLICK : 'mousedown'], d.keydown = d.keypress);
            b.innerHTML = m(a);
            bc(b.firstChild, function (a) {
                var b = r[a.className]; b && (c[b] = a), e && a.setAttribute("unselectable", "on")
            });
            addEvent(c.main, d, true);
            
            // Add "swipe" event to main body, swipe left/right to change months, swipe up/down to change years
            var swipeEvent = {};
            swipeEvent[SWIPE_LEFT] = proxy(swipe, a, 'swipe-left');
            swipeEvent[SWIPE_RIGHT] = proxy(swipe, a, 'swipe-right');
            swipeEvent[SWIPE_UP] = proxy(swipe, a, 'swipe-up');
            swipeEvent[SWIPE_DOWN] = proxy(swipe, a, 'swipe-down');
            addEvent([c.body, c.decadeCont], swipeEvent, true);
            
            if(!a.args.cont && a.inputField && !a.args.manualTrigger && !a.trigger) {
                addEvent(a.inputField, 'click', proxy(toggleCalendar, a));
            }

            addEvent(document, {click: proxy(hideOnClickOutsite, a)}, false);

            a.moveTo(a.date, !1);
            a.setTime(null, !0);

            // Walkaround to make 'click' event works on mobile Safari
            if(isIOS)
                document.body.style.cursor = 'pointer';

            return c.topCont;
        }

        function q(a) {
            function d() {
                c.showTime && (b.push("<td>"), p(a, b), b.push("</td>"))
            }
            var b = [], c = a.args;
            b.push("<table", j, " style='width:100%'><tr>"),
            c.timePos == "left" && d(),
            c.bottomBar && (b.push("<td>"),
            b.push(
                "<table", j,
                "><tr><td>",
                "<button tabindex='0' aria-hidden='true' role='presentation' dyc-btn='today' dyc-cls='bottomBar-today-hover,bottomBar-today-pressed' dyc-type='bottomBar-today' ",
                "class='EUCalendar-bottomBar-today'>",
                L("today"),
                "</button>",
                "</td></tr></table>"),
            b.push("</td>")),
            c.timePos == "right" && d(),
            b.push("</tr></table>");

            return b.join("")
        }
        function p(a, b) {
            b.push("<table class='EUCalendar-time'" + j + "><tr>",
                "<td rowspan='2'><div dyc-type='time-hour' dyc-cls='hover-time,pressed-time' class='EUCalendar-time-hour'></div></td>",
                "<td dyc-type='time-hour+' dyc-cls='hover-time,pressed-time' class='EUCalendar-time-up'></td>",
                "<td rowspan='2' class='EUCalendar-time-sep'></td>",
                "<td rowspan='2'><div dyc-type='time-min' dyc-cls='hover-time,pressed-time' class='EUCalendar-time-minute'></div></td>",
                "<td dyc-type='time-min+' dyc-cls='hover-time,pressed-time' class='EUCalendar-time-up'></td>"),
        a.args.showTime == 12 && b.push("<td rowspan='2' class='EUCalendar-time-sep'></td>", "<td rowspan='2'><div class='EUCalendar-time-am' dyc-type='time-am' dyc-cls='hover-time,pressed-time'></div></td>"),
        b.push("</tr><tr>", "<td dyc-type='time-hour-' dyc-cls='hover-time,pressed-time' class='EUCalendar-time-down'></td>", "<td dyc-type='time-min-' dyc-cls='hover-time,pressed-time' class='EUCalendar-time-down'></td>", "</tr></table>")
        }

        function createRandomIdString(portion) {
            return formatString('EUCalendar_menuExpanded_${portion}_${time}', {portion: portion, time: (new Date()).getTime()});
        }

        function getYearMenuBody(calendar, minYear, maxYear) {
            var yearTable = '<table role="presentation" class="EUCalendar-menu-table" width="90%" height="100%" cellpadding="0" cellspacing="0">', 
                start, 
                disabled = false,
                currentYear = calendar._getInputYear(),
                yearLabel,
                cssClass;
            if(maxYear - minYear > 11)
                maxYear = minYear + 11;
            start = minYear;
            while (start <= maxYear) {
                yearTable = concat(yearTable, '<tr>');
                for(var col = 3; col > 0; col--) {
                    disabled = (calendar.args.min && start < calendar.args.min.getFullYear() || calendar.args.max && start > calendar.args.max.getFullYear());
                    cssClass = 'EUCalendar-menu-month';
                    if(disabled)
                        cssClass += ' EUCalendar-menu-month-disabled';
                    
                    if(start == currentYear)
                        cssClass += ' EUCalendar-menu-month-selected'
                    yearLabel = toLocalDigit()(start);
                    yearTable = concat(
                        yearTable, 
                        '<td align="center" valign="middle">',
                        '<div role="button" tabindex="' + (disabled ? -1 : 0) + '" class="' + cssClass + '"' + (disabled ? 'disabled="1" aria-disabled="true"' : '') + ' dyc-cls="hover-navBtn,pressed-navBtn" dyc-type="menubtn" dyc-btn="' + ('y' + start++) + '"',
                        'aria-label="' + yearLabel  + '">',
                            yearLabel,
                        '</div></td>'
                    );
                }
                yearTable = concat(yearTable, '</tr>');
            }

            yearTable = concat(yearTable, '</table>');
            
            return yearTable;
        }

        function moveYearMenuTo(calendar, minYear, maxYear, isOpening) {
            var yearTable = '<table role="presentation" class="EUCalendar-menu-table" width="90%" height="100%" cellpadding="0" cellspacing="0">', 
                start, 
                isMovingForward = false,
                disabled = false,
                els = calendar.els,
                disabledLeft,
                disabledRight,
                currentMinYear = null;
            
            if(calendar._menuYearBodyAnimation)
                calendar._menuYearBodyAnimation.stop();

            disabledLeft = calendar.args.min && minYear < calendar.args.min.getFullYear();
            disabledRight = calendar.args.max && maxYear > calendar.args.max.getFullYear();

            try {
                currentMinYear = parseInt(els.navPrevDecade.getAttribute('dyc-year'));
                if(isNaN(currentMinYear))
                    currentMinYear = null;
            }catch(e) {}
            
            isMovingForward = currentMinYear && currentMinYear < minYear;
            
            els.navPrevDecade.setAttribute('dyc-year', minYear);
            $(disabledLeft, [els.navPrevDecade], "EUCalendar-navDisabled")
            if(disabledLeft) {
                els.navPrevDecade.setAttribute('disabled', '1');
                els.navPrevDecade.setAttribute('tabindex', '-1');
            } else {
                els.navPrevDecade.removeAttribute('disabled');
                els.navPrevDecade.setAttribute('tabindex', '0');
            }
            
            els.navNextDecade.setAttribute('dyc-year', maxYear);
            $(disabledRight, [els.navNextDecade], "EUCalendar-navDisabled")
            if(disabledRight) {
                els.navNextDecade.setAttribute('disabled', '1');
                els.navNextDecade.setAttribute('tabindex', '-1');

            } else {
                els.navNextDecade.removeAttribute('disabled');
                els.navNextDecade.setAttribute('tabindex', '0');
            }

            var minYearLabel = toLocalDigit()(minYear),
                maxYearLabel = toLocalDigit()(maxYear),
                label = formatString(L('yearPeriod') || 'period of 12 years from ${minYear} to ${maxYear}', {minYear: minYearLabel, maxYear: maxYearLabel});

            els.decadeTitle.innerHTML = '<div aria-label="' + label + '"><span aria-hidden="true">' + minYearLabel + '</span>&nbsp;-&nbsp;<span aria-hidden="true">' + maxYearLabel + '</span></div>';
            
            if(!calendar.args.animation || isOpening === true){
                els.decadeCont.innerHTML = getYearMenuBody(calendar, minYear, maxYear);
            } else {
                var body = calendar.els.decadeCont,
                    animClass = isMovingForward ? 'fwd' : 'back',
                    animElm = createElement('div', 'EUCalendar-animBody-' + animClass, body),
                    table = body.firstChild,
                    offsetLeft = table.offsetLeft,
                    bodyWidth = body.offsetWidth,
                    opacity = setOpacity(table) || 0.7,
                    swing = be.accel_ab2,
                    cloneAnimElm,
                    htmlContent = getYearMenuBody(calendar, minYear, maxYear);
                
                if(isMovingForward)
                    bodyWidth = -bodyWidth;

                cloneAnimElm = animElm.cloneNode(true);
                cloneAnimElm.appendChild(table.cloneNode(true));
                cloneAnimElm.style.marginLeft = bodyWidth + 'px';
                cloneAnimElm.style.width = '100%';
                body.appendChild(cloneAnimElm);

                table.style.visibility = 'hidden';

                animElm.style.width = '100%';
                animElm.innerHTML = htmlContent;

                
                calendar._menuYearBodyAnimation = animate({
                    onUpdate: function (a, b) {
                        var f = swing(a);
                        var g = b(f, bodyWidth, bodyWidth * 2) + 'px';
                        
                        animElm.style.marginLeft = b(f, 0, bodyWidth) + "px";
                        cloneAnimElm.style.marginLeft = g;
                        if(calendar.args.opacity > 2) {
                            setOpacity(cloneAnimElm, 1 - f);
                            setOpacity(animElm, f);
                        }
                    },
                    onStop: function (b) {
                        body.innerHTML = htmlContent;
                        calendar._menuYearBodyAnimation = null;
                    }
                });
            }
        }

        function renderYearMenu(calendar) {
            var html = '';
            html = concat(html, 
				'<div role="presentation" class="EUCalendar-menu-cont">',
					'<div role="presentation" style="padding-top: 10px;">', 
						'<table role="presentation" style="margin: 0 auto;" width="80%" cellpadding="0" cellspacing="0">',
                            '<tr>',
                                '<td align="center" valign="middle"><div tabindex="0" role="button" class="EUCalendar-menu-prevDecade" dyc-cls="hover-navBtn,pressed-navBtn" dyc-type="menubtn" dyc-btn="-MM" dyc-year=""><button aria-hidden="true" tabindex="-1">‹</button></div></td>',
                                '<td align="center" valign="middle"><div class="EUCalendar-menu-decade-title" aria-live="polite" dyc-cls="hover-decade-year-title,pressed-decade-year-title" dyc-type="menubtn"></div></td>',
                                '<td align="center" valign="middle"><div tabindex="0" role="button" class="EUCalendar-menu-nextDecade" dyc-cls="hover-navBtn,pressed-navBtn" dyc-type="menubtn" dyc-btn="+MM" dyc-year=""><button aria-hidden="true" tabindex="-1">›</button></div></td>',
                            '</tr>',
                        '</table>',
					'</div>',
					'<p aria-hidden="true" class="EUCalendar-menu-sep">&nbsp;</p>',
					'<div class="EUCalendar-menu-decade-table-cont">',
                     '</div>',
				    '<button class="EUCalendar-menu-close" aria-label="' + (L('closeBtn') || 'Close') + '" role="presentation" dyc-type="menubtn" dyc-btn="year-close" dyc-cls="menu-close-hover,menu-close-pressed">×</button>',
				'</div>'
            ); 
            return html;
        }

        function renderMenu(calendar, controlId) {
            var currentYear = calendar.date.getFullYear();
            var b = [
                "<div role='presentation' class='EUCalendar-menu-cont'>",
                    "<div style='padding-top: 10px;'>",
                        "<table width='100%'",j," role='presentation'>",
                            "<tr>",
                                "<td align='center'>",
                                    "<input tabindex='-1' style='display:none;' aria-hidden='true' role='presentation' type='text' class='EUCalendar-menu-year' size='6' value='",
                                        currentYear,"'/>",
                                    "<div tabindex='0' dyc-btn='year' aria-expanded='false' aria-controls='" + controlId + "' dyc-type='menubtn' class='EUCalendar-menu-yearLabel' dyc-cls='menu-hover-yearLabel,menu-pressed-yearLabel' aria-label='" + (L('year') || "year") +  " " + toLocalDigit()(calendar.date.getFullYear()) + "'><div>" + toLocalDigit()(currentYear) + "</div></div>", 
                                "</td>",
                            "</tr>",
                            "<tr><td align='center'><button class='EUCalendar-menu-today' tabindex='0' aria-hidden='true' role='presentation' dyc-type='menubtn' dyc-cls='menu-today-hover,menu-today-pressed' dyc-btn='today'>",
                                L("goToday"),
                            "</button></td></tr>",
                        "</table>",
                    "</div>",
                    "<p class='EUCalendar-menu-sep'>&nbsp;</p>",
                    "<div class='EUCalendar-menu-year-table-cont'>",
                        "<table width='90%' role='presentation' class='EUCalendar-menu-mtable'",j,">"],
                c = L("smn"),
                fullMonths = L("mn"),
                date = new Date(0),
                monthYearFormat = calendar.args.ariaDateFormat.monthYear,
                d = 0,
                e = b.length,
                f;
                //b[e++] = '<caption style="display:none;">' + (L('selectMonthOfYear', {year: currentYear}) || formatString('Select months of ${year}', {year: currentYear})) + '</caption>';

                date.setMilliseconds(calendar.date.getTime());

            while (d < 12) {
                

                b[e++] = "<tr>";
                for (f = 4; --f > 0; ){
                    date.setMonth(d);
                    b[e++] = "<td><div role='button' tabindex='0' aria-label='" 
                            + ((L('select') || 'Select') 
                            + ' ' + toLocalDigit()(printDate(date, monthYearFormat)))
                            + "' role='presentation' dyc-type='menubtn' dyc-cls='hover-navBtn,pressed-navBtn' dyc-btn='m"
                            + d + "' class='EUCalendar-menu-month'>"
                            + c[d++] + "</div></td>";
                }
                b[e++] = "</tr>";
            }
            b[e++] = "</table></div>";
            b[e++] ="<button class='EUCalendar-menu-close'  aria-label='" + (L("closeBtn") || "Close") + "'  role='presentation' dyc-type='menubtn' dyc-btn='close' dyc-cls='menu-close-hover,menu-close-pressed'>×</button>"
            b[e++] = "</div>";

            return b.join("")
        }
        function n(a) {
            return "<div aria-hidden='true' role='presentation' unselectable='on'>" + printDate(a.date, a.args.titleFormat) + "</div>";
        }

        /**
        * Get calendar's header body
        */
        function m(a) {
            // Focus problem: http://snook.ca/archives/accessibility_and_usability/elements_focusable_with_tabindex
			var ariaDateFormat = a.args.ariaDateFormat.fullDate,
                ariaMonthYearFormat = a.args.ariaDateFormat.monthYear;
            var menuIds = [createRandomIdString('month'), createRandomIdString('decade')];
            var prevMonth = new Date(0),
                nextMonth = new Date(0),
                currentYear = a.date.getFullYear();
            prevMonth.setMilliseconds(a.date.getTime());
            prevMonth.setMonth(prevMonth.getMonth() - 1);

            nextMonth.setMilliseconds(a.date.getTime());
            nextMonth.setMonth(nextMonth.getMonth() + 1);

            var b = [
            "<div class='EUCalendar-topCont' aria-label='" + (L('description') || 'Calendar view date-picker') + "' id='" + a.idString + "' ", (a.args.isRtl ? "style='direction:rtl;'": "") , j, ">",
                "<div class='EUCalendar' role='presentation'>",
                    "<div class='EUCalendar-topBar'>",
                        "<table class='EUCalendar-table-topControl' role='presentation'>",
                            "<tr>",
                                "<td>",
                                    "<div role='button' tabindex='0' aria-label='" + ((L("prevYear") || "Previous year") + ", " + toLocalDigit()((currentYear - 1))) + "' dyc-type='nav' dyc-btn='-Y' dyc-cls='hover-navBtn,pressed-navBtn' class='EUCalendar-navBtn EUCalendar-prevYear'><button tabindex='-1' aria-hidden='true' role='presentation'>«</button></div>",
                                "</td>",
                                "<td>",
                                    "<div role='button' tabindex='0' aria-label='" + ((L("prevMonth") || "Previous month") + ", " + toLocalDigit()((printDate(prevMonth, ariaMonthYearFormat)))) + "' dyc-type='nav' dyc-btn='-M' dyc-cls='hover-navBtn,pressed-navBtn' class='EUCalendar-navBtn EUCalendar-prevMonth'><button tabindex='-1' aria-hidden='true' role='presentation'>‹</button></div>",
                                "</td>",
                                "<td>",
                                    "<table role='presentation' class='EUCalendar-titleCont'",j,
                                        "><tr><td>",
                                            "<div role='button' aria-expanded='false' aria-controls='" + menuIds[0] + "' tabindex='0' aria-label='" + printDate(a.date, ariaDateFormat) + "' aria-live='assertive' dyc-type='title' dyc-btn='menu' dyc-cls='hover-title,pressed-title' class='EUCalendar-title'>",
                                            n(a),
                                            "</div>",
                                        "</td></tr>",
                                    "</table>",
                                "</td>",
                                "<td>",
                                    "<div role='button' tabindex='0' aria-label='" + ((L("nextMonth") || "Next month") + "," + toLocalDigit()(printDate(nextMonth, ariaMonthYearFormat))) + "' dyc-type='nav' dyc-btn='+M' dyc-cls='hover-navBtn,pressed-navBtn' class='EUCalendar-navBtn EUCalendar-nextMonth'><button tabindex='-1' aria-hidden='true' role='presentation'>›</button></div>",
                                "</td>",
                                "<td>",
                                    "<div role='button' tabindex='0' aria-label='" + ((L("nextYear") || "Next year") + "," + toLocalDigit()((currentYear + 1))) + "' dyc-type='nav' dyc-btn='+Y' dyc-cls='hover-navBtn,pressed-navBtn' class='EUCalendar-navBtn EUCalendar-nextYear'><button tabindex='-1' aria-hidden='true' role='presentation'>»</button></div>",
                                "</td>",
                            "</tr>",
                        "</table>",
                        "<div class='EUCalendar-dayNames' role='presentation' aria-hidden='true'>", week(a), "</div>",
                    "</div>",
                    "<div class='EUCalendar-body' role='presentation'></div>"];
            (a.args.bottomBar || a.args.showTime) && b.push("<div class='EUCalendar-bottomBar'>", q(a), "</div>"),
        b.push("<div  role='presentation' aria-label='" + (L('selectMonthOfYear', {year: currentYear}) || formatString('Select months of ${year}', {year: currentYear})) +"'  aria-hidden='true' aria-expanded='false' class='EUCalendar-menu' id='" + menuIds[0] + "' style='display:none'>",
            renderMenu(a, menuIds[1]),
            "</div>",
            "<div role='presentation' aria-hidden='true' aria-expanded='false' class='EUCalendar-menuYear' id='" + menuIds[1] + "'  style='display:none'>",
                renderYearMenu(a),
            "</div>",
            "<div class='EUCalendar-tooltip' role='presentation' aria-hidden='true'></div>",
            "</div>",
            "</div>");

            return b.join("")
        }
        function body(a, b, c) {
            //printDate(a.date, a.args.titleFormat)
            b = b || a.date;
            c = c || a.fdow;
            b = new Date(b.getFullYear(), b.getMonth(), b.getDate(), 12, 0, 0, 0);

            var d = b.getMonth(), 
                e = [], 
                f = 0, 
                g = a.args.weekNumbers; 
                b.setDate(1),
                h = (b.getDay() - c) % 7,
                ariaDateFormat = a.args.ariaDateFormat.fullDate || (g ? "%A, " : "") + "%B %e, %Y",
                currentDateStr = printDate(a.date, ariaDateFormat),
                dayIndex = 0;

            if(h < 0) h += 7;
            b.setDate(0 - h);
            b.setDate(b.getDate() + 1);

            var i = new Date, k = i.getDate(), l = i.getMonth(), m = i.getFullYear();
            e[f++] = "<table role='presentation' class='EUCalendar-bodyTable'" + j + " summary='" + (L('summary') || "Table containing all dates for the currently selected month") + "'>";
            e[f++] = "<caption><span style='display:none'>" + currentDateStr + "</span></caption>";
            for (var n = 0; n < 6; ++n) {
                e[f++] = "<tr role='row' class='EUCalendar-week";

                if(n == 0)
                    e[f++] = " EUCalendar-first-row";
                
                n == 5 && (e[f++] = " EUCalendar-last-row");
                
                e[f++] = "'>";
                
                g && (e[f++] = "<th role='presentation' scope='row' class='EUCalendar-first-col'><div class='EUCalendar-weekNumber'>" + M(b) + "</div></th>");
                
                for (var o = 0; o < 7; ++o) {
                    var date = b.getDate(),
                        month = b.getMonth(),
                        year = b.getFullYear(),
                        s = 1e4 * year + 100 * (month + 1) + date,
                        selected = a.selection.isSelected(s),
                        disabled = a.isDisabled(b),
                        dateInfo;

                        e[f++] = "<td role='presentation' class='";
                        
                        if(o == 0 && !g)
                            e[f++] = " EUCalendar-first-col";

                        if(o == 0 && n == 0)
                            a._firstDateVisible = s;

                        if(o == 6)
                            e[f++] = " EUCalendar-last-col", n == 5 && (a._lastDateVisible = s);

                        if(selected)
                            e[f++] = " EUCalendar-td-selected";

                        e[f++] = "'><div role='button' dyc-type='date' unselectable='on' dyc-date='" + s + "'";

                        if(disabled)
                            e[f++] = " disabled='1' aria-hidden='true' ";
                        else {
                            e[f++] = " aria-label='" + printDate(b, ariaDateFormat) + "'";
                            if(dayIndex++ == 0) {
                                //e[f++] = 'tabindex="0" ';
                                if(!a._lastHoverDate){
                                    //a._lastHoverDate = dateToInt(b);
                                }
                            }
                        }
                        e[f++] = " class='EUCalendar-day";

                        if(L("weekend").indexOf(b.getDay()) >= 0)
                            e[f++] = " EUCalendar-weekend";

                        if(month != d)
                            e[f++] = " EUCalendar-day-othermonth";

                        if(date == k && month == l && year == m)
                            e[f++] = " EUCalendar-day-today";

                        if(disabled)
                            e[f++] = " EUCalendar-day-disabled";

                        if(selected)
                            e[f++] = " EUCalendar-day-selected";

                        dateInfo = a.args.dateInfo(b);

                        if(dateInfo && dateInfo.klass)
                            e[f++] = " " + dateInfo.klass;
                        e[f++] = !disabled ? "' tabindex='-1'" : "'";
                        e[f++] = ">" + toLocalDigit()(date) + "</div></td>";
                        b = new Date(year, month, date + 1, 12, 0, 0, 0);
                } 
                e[f++] = "</tr>";
            }

            e[f++] = "</table>";
            return e.join("")
        }

        /**
        * Generate HTML of week
        */
        function week(a) {
            var b = ["<table ", j, "><tr>"],
            c = 0; a.args.weekNumbers && b.push("<td><div class='EUCalendar-weekNumber'>", L("wk"), "</div></td>");
            while (c < 7) {
                var d = (c++ + a.fdow) % 7;
                b.push("<td><div",
                L("weekend").indexOf(d) >= 0 ? " class='EUCalendar-weekend'>" : ">",
                L("sdn")[d],
                "</div></td>")
            }
            b.push("</tr></table>");

            return b.join("")
        }

        // Init() ??
        function a(b) {
            b = b || {},
            this.args = b = U(b, { 
                animation: !f,
                cont: null,
                bottomBar: !0,
                date: !0,
                fdow: L("fdow"),
                min: null,
                max: null,
                reverseWheel: !1,
                selection: [],
                selectionType: a.SEL_SINGLE,
                weekNumbers: !1,
                align: "Bl/ / /T/r",
                inputField: null,
                trigger: null,
                manualTrigger: false, // Show manually calendar
                dateFormat: "%Y-%m-%d",
                fixed: !1,
                opacity: e ? 1 : 3,
                titleFormat: "%b %Y",
                showTime: !1,
                timePos: "right",
                time: !0,
                minuteStep: 5,
                noScroll: !1,
                disabled: bl,
                checkRange: !1,
                dateInfo: bl,
                onChange: bl,
                onSelect: bl,
                onTimeChange: bl,
                onFocus: bl,
                onBlur: bl,
                isRtl: false
            }),

            this.args.ariaDateFormat = U(this.args.ariaDateFormat || {}, {
                    fullDate: "%B %e %Y %A",
                    monthYear: "%B %Y",
                    monthDay: "%B %e"
                })

            this.handlers = {};
                var c = this,
                d = new Date;

                b.min = S(b.min),
            b.max = S(b.max),
            b.date === !0 && (b.date = d),
            b.time === !0 && (b.time = d.getHours() * 100 + Math.floor(d.getMinutes() / b.minuteStep) * b.minuteStep),
            this.date = S(b.date),
            this.time = b.time,
            this.fdow = b.fdow,
            this.isVisible = false;
            
            this.trigger = getElement(b.trigger);
            this.inputField = getElement(b.inputField);
            this.dateFormat = b.dateFormat;
            this.idString = createRandomIdString('main');

            if(this.inputField && !this.inputField.getAttribute('aria-controls')) {
                this.inputField.setAttribute('aria-controls', this.idString);
                this.inputField.setAttribute('aria-expanded', false);
            }
			
			if(this.trigger && !this.trigger.getAttribute('aria-controls')) {
                this.trigger.setAttribute('aria-controls', this.idString);
                this.trigger.setAttribute('aria-expanded', false);
            }

            bk("onChange onSelect onTimeChange onFocus onBlur".split(/\s+/),
            function (a) {
                var d = b[a]; d instanceof Array || (d = [d]), c.handlers[a] = d
            }),
            this.selection = new a.Selection(b.selection, b.selectionType, G, this);

            var g = s(this);

            if(b.cont)
                getElement(b.cont).appendChild(g);

            if(this.trigger)
                this.manageFields();
            
            _calendarObjects.push(this);
        }


        var b = navigator.userAgent,
        c = /opera/i.test(b),
        d = /Konqueror|Safari|KHTML/i.test(b),
        e = /msie/i.test(b) && !c && !/mac_powerpc/i.test(b),
        f = e && /msie 6/i.test(b),
        g = /gecko/i.test(b) && !d && !c && !e,
        h = a.prototype,
        i = a.I18N = {};

    a.SEL_NONE = 0,
        a.SEL_SINGLE = 1,
        a.SEL_MULTIPLE = 2,
        a.SEL_WEEK = 3,
        a.VERSION = VERSION,
        a.dateToInt = dateToInt,
        a.intToDate = intToDate,
        a.printDate = printDate,
        a.parseDateExact = internalParseDate,
        a.formatString = formatString,
        a.dotNet2CalendarFormat = dotNet2CalendarFormat,
        a.i18n = L,
        a.LANG = function (a, b, c) {
            i.__ = i[a] = {
                name: b,
                data: c
            }
        },
        a.setup = function (b) {
            return new a(b);
        },
    h._enforceFocus = function () {
        removeEvent(document, 'focusin', proxy(infiniteFocusLoop, this));
        addEvent(document, 'focusin', proxy(infiniteFocusLoop, this));
    },
    h.moveTo = function (a, b, onStop) {
        var c = this;

        a = S(a);

        var d = Q(a, c.date, !0),
            e,
            f = c.args,
            g = f.min && Q(a, f.min),
            h = f.max && Q(a, f.max),
            nextMonth = new Date(0),
            prevMonth = new Date(0),
            currentYear = c.date.getFullYear(),
            monthYearFormat = c.args.ariaDateFormat.monthYear;

        f.animation || (b = !1);
        var disabledPrevNav = (g != null && g <= 1),
			disabledNextNav = (h != null && h >= -1);
		$(disabledPrevNav, [c.els.navPrevMonth, c.els.navPrevYear], "EUCalendar-navDisabled");
        $(disabledNextNav, [c.els.navNextMonth, c.els.navNextYear], "EUCalendar-navDisabled");
        if(disabledPrevNav) {
			c.els.navPrevMonth.setAttribute('tabindex', -1);
            c.els.navPrevMonth.setAttribute('disabled', 1);

			c.els.navPrevYear.setAttribute('tabindex', -1);
            c.els.navPrevYear.setAttribute('disabled', 1);
		} else {
			c.els.navPrevMonth.setAttribute('tabindex', 0);
            c.els.navPrevMonth.removeAttribute('disabled');

			c.els.navPrevYear.setAttribute('tabindex', 0);
            c.els.navPrevYear.removeAttribute('disabled');
		}
		
		if(disabledNextNav) {
			c.els.navNextMonth.setAttribute('tabindex', -1);
            c.els.navNextMonth.setAttribute('disabled', 1);

			c.els.navNextYear.setAttribute('tabindex', -1);
            c.els.navNextYear.setAttribute('disabled', 1);
		} else {
			c.els.navNextMonth.setAttribute('tabindex', 0);
            c.els.navNextMonth.removeAttribute('disabled');

			c.els.navNextYear.setAttribute('tabindex', 0);
            c.els.navNextYear.removeAttribute('disabled');
		}
		
        nextMonth.setMilliseconds(c.date.getTime());
        nextMonth.setMonth(nextMonth.getMonth() + 1);
        prevMonth.setMilliseconds(c.date.getTime());
        prevMonth.setMonth(nextMonth.getMonth() + 1);

        c.els.navNextMonth.setAttribute('aria-label', (L('nextMonth') || 'Next month ') + ' ' + printDate(nextMonth, monthYearFormat));
        c.els.navPrevMonth.setAttribute('aria-label', (L('prevMonth') || 'Previous month ') + ' ' + printDate(prevMonth, monthYearFormat));
        c.els.navNextYear.setAttribute('aria-label', (L('nextYear') || 'Next year') + ' ' + (currentYear + 1));
        c.els.navPrevYear.setAttribute('aria-label', (L('prevYear') || 'Previous year') + ' ' + (currentYear - 1));
		
		g < -1 && (a = f.min, e = 1, d = 0);
        h > 1 && (a = f.max, e = 2, d = 0);
        c.date = a;
		c.refresh(!!b);
        c.callHooks("onChange", c, a, b);

        if (b && (d != 0 || b != 2)) {
            c._bodyAnim && c._bodyAnim.stop();

            var i = c.els.body,
                j = createElement("div", "EUCalendar-animBody-" + x[d], i),
                k = i.firstChild, m = setOpacity(k) || .7,
                n = e ? be.brakes : d == 0 ? be.shake : be.accel_ab2, o = d * d > 4,
                p = o ? k.offsetTop : k.offsetLeft,
                q = j.style,
                r = o ? i.offsetHeight : i.offsetWidth;

            d < 0 ? r += p : d > 0 ? r = p - r : (r = Math.round(r / 7),
            e == 2 && (r = -r));

            if (!e && d != 0) {
                var s = j.cloneNode(!0),
                    t = s.style,
                    u = 2 * r;
                s.appendChild(k.cloneNode(!0)),
                t[o ? "marginTop" : "marginLeft"] = r + "px", i.appendChild(s)
            }

            k.style.visibility = "hidden";
            j.innerHTML = body(c);
            
            c._bodyAnim = animate({
                onUpdate: function (a, b) {
                    var f = n(a);
                    if (s)
                        var g = b(f, r, u) + "px";
                    if (e)
                        q[o ? "marginTop" : "marginLeft"] = b(f, r, 0) + "px";
                    else {
                        if (o || d == 0)
                            q.marginTop = b(d == 0 ? n(a * a) : f, 0, r) + "px",
                            d != 0 && (t.marginTop = g);
                        if (!o || d == 0)
                            q.marginLeft = b(f, 0, r) + "px", d != 0 && (t.marginLeft = g)
                    }

                    c.args.opacity > 2 && s && (setOpacity(s, 1 - f), setOpacity(j, f))
                },
                onStop: function (b) {
                    i.innerHTML = body(c, a);
                    c._bodyAnim = null;
                    var focusableElm = setFocusableForFistElement(c.els.body);
                    if(isFunction(onStop))
                        onStop(focusableElm);
                }
            })
        } 
        
        c._lastHoverDate = null; 
        return g >= -1 && h <= 1;
    },
    h.isDisabled = function (a) {
        var b = this.args; return b.min && Q(a, b.min) < 0 || b.max && Q(a, b.max) > 0 || b.disabled(a)
    },
    h.toggleMenu = function () {
        showhideMenu(this, !this._menuVisible)
    },
    h.refresh = function (a) {
        var b = this.els; 
        a || (b.body.innerHTML = body(this));
        b.title.innerHTML = n(this);
        b.yearInput.value = this.date.getFullYear();
    },
    h.redraw = function () {
        var a = this, b = a.els;
        a.refresh(),
        b.dayNames.innerHTML = week(a),
        b.menu.innerHTML = renderMenu(a),
        b.bottomBar && (b.bottomBar.innerHTML = q(a)),
        bc(b.topCont, function (c) {
            var d = r[c.className];
            d && (b[d] = c);
            //c.className == "EUCalendar-menu-year" ? (addEvent(c, a._focusEvents), b.yearInput = c) : e && c.setAttribute("unselectable", "on")
            if(c.className == "EUCalendar-menu-year"){
                b.yearInput = c;
            }
            else if(e.className == 'EUCalendar-menu-yearLabel') {
                b.yearLabel = c;
            }
            else if(e) {
                c.setAttribute("unselectable", "on");
            }
        }),
        a.setTime(null, !0)
    },
    h.setLanguage = function (b) {
        var c = a.setLanguage(b); c && (this.fdow = c.data.fdow, this.redraw())
    },
    a.setLanguage = function (a) {
        var b = i[a]; b && (i.__ = b); return b
    },
    h.focus = function () {
        startInternalFocusing.call(this)
    },
    h.blur = function () {
        //this.els.focusLink.blur();
        //this.els.yearInput.blur();
        this.els.main.blur();
        startInternalBluring.call(this);
    },
    h.showAt = function (a, b, c) {
        this._showAnim && this._showAnim.stop(); 
        c = c && this.args.animation; 
        
        var d = this.els.topCont, 
            e = this, 
            f = this.els.body.firstChild, 
            g = f.offsetHeight, 
            h = d.style; 
        h.position = "absolute";
        h.left = a + "px";
        h.top = b + "px";
        h.display = "";
        //d.setAttribute('aria-expanded', true);
		if(this.trigger) {
			this.trigger.setAttribute('aria-expanded', true);
		}
		if(this.inputField) {
			this.inputField.setAttribute('aria-expanded', true);
        }
        this._enforceFocus();
        //d.setAttribute('aria-hidden', false);
        if(c) {
            f.style.marginTop = -g + "px";
            this.args.opacity > 1 && setOpacity(d, 0);
            this._showAnim = animate({ 
                onUpdate: function (a, b) { 
                    f.style.marginTop = -b(be.accel_b(a), g, 0) + "px";
                    
                    if(e.args.opacity > 1) setOpacity(d, a);
                }, 
                onStop: function () { 
                    e.args.opacity > 1 && setOpacity(d, "");
                    e._showAnim = null;
                    e.focus();
                } 
            });
        }
    },
    h.hide = function () {
        var a = this.els.topCont,
            b = this,
            c = this.els.body.firstChild,
            d = c.offsetHeight,
            e = position(a).y;
        
        function focusBack() {
            if(b.trigger && isElementFocusable(b.trigger)) {
                b.trigger.focus();
            } else if(b.inputField && isElementFocusable(b.inputField)) {
                b.inputField.focus();
            }
        }
		
        if(this.args.animation) {
            if(this._showAnim)
                this._showAnim.stop();

            this._showAnim = animate({ 
                onUpdate: function (f, g) { 
                    if(b.args.opacity > 1) 
                        setOpacity(a, 1 - f);
                    c.style.marginTop = -g(be.accel_b(f), 0, d) + "px";
                    a.style.top = g(be.accel_ab(f), e, e - 10) + "px" 
                }, 
                onStop: function () {
                    a.style.display = "none";
                    c.style.marginTop = "";
                    b.args.opacity > 1 && setOpacity(a, ""); 
                    b._showAnim = null; 
                    //a.setAttribute('aria-expanded', false);
                    //a.setAttribute('aria-hidden', true);
                    //focusBack();
                } 
            });
            
        } else {
            a.style.display = "none";
            //a.setAttribute('aria-expanded', false);
            //a.setAttribute('aria-hidden', true);
            //focusBack();
        }
        
        if (this.trigger) {
            addClasses(this.trigger, 'EUCalendar-trigger-focus');
			this.trigger.setAttribute('aria-expanded', false);
        }
        if(this.inputField) {
			addClasses(this.inputField, 'EUCalendar-trigger-focus');
			this.inputField.setAttribute('aria-expanded', true);
        }
        removeEvent(document, proxy(infiniteFocusLoop, this));
        this.isVisible = false;
		focusBack();
    },
	h.toggle = function(target) {
        toggleCalendar.apply(this, [createDefaultEvent(), target]);
	},
    h.popup = function (anchor, align) {
        function getPosition(b) {
            var c = { x: i.x, y: i.y };
            if (!b) return c;
            /B/.test(b) && (c.y += anchor.offsetHeight);
            /b/.test(b) && (c.y += anchor.offsetHeight - f.y);
            /T/.test(b) && (c.y -= f.y);
            /l/.test(b) && (c.x -= f.x - anchor.offsetWidth);
            /L/.test(b) && (c.x -= f.x);
            /R/.test(b) && (c.x += anchor.offsetWidth);
            /c/i.test(b) && (c.x += (anchor.offsetWidth - f.x) / 2);
            /m/i.test(b) && (c.y += (anchor.offsetHeight - f.y) / 2);
            return c
        }

        anchor = getElement(anchor);
        align || (align = this.args.align);
        align = align.split(/\x2f/);
        var c = position(anchor), d = this.els.topCont, e = d.style, f, g = getDocumentDimension();
        e.visibility = "hidden";
        e.display = "";
        this.showAt(0, 0);
        
        // Keep tab index right after input element
        if(this.inputField){
            insertAfter(d, this.inputField);
        } else {
            document.body.appendChild(d);
        }
        f = { x: d.offsetWidth, y: d.offsetHeight };

        var i = c;
        i = getPosition(align[0]);
        i.y < g.y && (i.y = c.y, i = getPosition(align[1]));
        i.x + f.x > g.x + g.w && (i.x = c.x, i = getPosition(align[2]));
        i.y + f.y > g.y + g.h && (i.y = c.y, i = getPosition(align[3]));
        i.x < g.x && (i.x = c.x, i = getPosition(align[4]));

        i.x + f.x > g.x + g.w && (i.x = g.x);
        this.showAt(i.x, i.y, !0);
        e.visibility = "";
        //this.focus();
        
        this.isVisible = true;

        if (anchor) {
            addClass(anchor, 'EUCalendar-trigger-focus');
        }
    },
    h.manageFields = function (trigger, inputField, dateFormat) {
        if(this.args.manualTrigger || !this.trigger)
            return;

        var e = this;
        if(inputField)
            this.inputField = getElement(inputField);
        if(trigger)
            this.trigger = getElement(trigger);
        if(dateFormat)
            this.dateFormat = dateFormat;
        
        this.trigger && /^button$/i.test(this.trigger.tagName) && this.trigger.setAttribute("type", "button");
		
		var triggerFunc = (function(_context) {
			var ctx = _context;
			return function(evt) {
				ctx.triggerClicked = true; /*Trick to fix problem of blur event on IOS*/
				var keyCode = evt.keyCode || evt.which;
				if (keyCode == 13 || evt.type == 'click') {
					toggleCalendar.call(ctx, evt);
					return false;
				}
				return true;
			};
		})(e);
		
		this.trigger && addEvent(this.trigger, {
			'click': triggerFunc,
			'keydown': triggerFunc
		}, true);
		
        //this.trigger && addEvent(this.trigger, 'click', function (evt) {
            /*if (e.selection.type == a.SEL_SINGLE) {
                var f, g, h, i;
                f = /input|textarea/i.test(inputField.tagName) ? inputField.value : inputField.innerText || inputField.textContent,
                f && (g = /(^|[^%])%[bBmo]/.exec(dateFormat),
                h = /(^|[^%])%[de]/.exec(dateFormat), g && h && (i = g.index < h.index),

                // check if calendar language is Arabic, 
                // Convert number with arabic digits into latin digits
                f = fromLocalDigit()(f),

                f = Calendar.parseDate(f, i),
                f && (e.selection.set(f, !1, !0),
                e.args.showTime && (e.setHours(f.getHours()),
                e.setMinutes(f.getMinutes())),
                e.moveTo(f)));
            }*/

            //e.triggerClicked = true; /*Trick to fix problem of blur event on IOS*/

            //toggleCalendar.call(e, evt);

            // if (!/\s*EUCalendar-trigger-focus\s*/ig.test(trigger.className)) {
            //     evt.preventDefault ? evt.preventDefault() : (evt.returnValue = false);
            //     e.popup(trigger);
            // } else {
            //     cancelEvent(evt);
            //     startBluring.call(e, evt);
            // }
            //return false;
        //}, true);
    },
    h.callHooks = function (a) {
        var b = ba(arguments, 1), c = this.handlers[a], d = 0; for (; d < c.length; ++d) c[d].apply(this, b)
    },
    h.addEventListener = function (a, b) {
        this.handlers[a].push(b)
    },
    h.removeEventListener = function (a, b) {
        var c = this.handlers[a], d = c.length; while (--d >= 0) c[d] === b && c.splice(d, 1)
    },
    h.getTime = function () {
        return this.time
    },
    h.setTime = function (a, b) {
        if (this.args.showTime) { a = a != null ? a : this.time, this.time = a; var c = this.getHours(), d = this.getMinutes(), e = c < 12; this.args.showTime == 12 && (c == 0 && (c = 12), c > 12 && (c -= 12), this.els.timeAM.innerHTML = L(e ? "AM" : "PM")), c < 10 && (c = "0" + c), d < 10 && (d = "0" + d), this.els.timeHour.innerHTML = c, this.els.timeMinute.innerHTML = d, b || this.callHooks("onTimeChange", this, a) }
    },
    h.getHours = function () {
        return Math.floor(this.time / 100)
    },
    h.getMinutes = function () {
        return this.time % 100
    },
    h.setHours = function (a) {
        a < 0 && (a += 24), this.setTime(100 * (a % 24) + this.time % 100)
    },
    h.setMinutes = function (a) {
        a < 0 && (a += 60), a = Math.floor(a / this.args.minuteStep) * this.args.minuteStep, this.setTime(100 * this.getHours() + a % 60)
    },
    h._getInputYear = function () {
        var a = parseInt(this.els.yearInput.value, 10);
        var currentYear = new Date().getFullYear(); 
        if(isNaN(a)) {
            a = this.date.getFullYear();
        } else if(a > currentYear + 99 || a < 1970) {
            a = currentYear;
        }
        return a;
    },
    h._showTooltip = function (a) {
        var b = "", c, d = this.els.tooltip; a && (a = intToDate(a), c = this.args.dateInfo(a), c && c.tooltip && (b = "<div class='EUCalendar-tooltipCont'>" + printDate(a, c.tooltip) + "</div>")), d.innerHTML = b
    };
        var j = " align='center' cellspacing='0' cellpadding='0'",
    r = {
        "EUCalendar-topCont": "topCont",
        "EUCalendar": "main",
        "EUCalendar-topBar": "topBar",
        "EUCalendar-title": "title",
        "EUCalendar-dayNames": "dayNames",
        "EUCalendar-body": "body",
        "EUCalendar-menu": "menu",
        "EUCalendar-menuYear": "menuYear",
        "EUCalendar-menu-year": "yearInput",
        "EUCalendar-menu-yearLabel": "yearLabel",
        "EUCalendar-menu-prevDecade": "navPrevDecade",
        "EUCalendar-menu-nextDecade": "navNextDecade",
        "EUCalendar-menu-decade-title": "decadeTitle",
        "EUCalendar-menu-decade-table-cont": "decadeCont",
        "EUCalendar-bottomBar": "bottomBar",
        "EUCalendar-tooltip": "tooltip",
        "EUCalendar-time-hour": "timeHour",
        "EUCalendar-time-minute": "timeMinute",
        "EUCalendar-time-am": "timeAM",
        "EUCalendar-navBtn EUCalendar-prevYear": "navPrevYear",
        "EUCalendar-navBtn EUCalendar-nextYear": "navNextYear",
        "EUCalendar-navBtn EUCalendar-prevMonth": "navPrevMonth",
        "EUCalendar-navBtn EUCalendar-nextMonth": "navNextMonth"
    },
    x = { "-3": "backYear", "-2": "back", 0: "now", 2: "fwd", 3: "fwdYear" },
    H = { 37: -1, 38: -2, 39: 1, 40: 2 }, I = { 33: -1, 34: 1 };
        h._getDateDiv = function (a) {
            var b = null;
            if (a)
                try {
                    bc(this.els.body, function (c) {
                        if (c.getAttribute("dyc-date") == a)
                            throw b = c
                    })
                } catch (c) { }

            return b
        },
    (a.Selection = function (a, b, c, d) {
        this.type = b, this.sel = a instanceof Array ? a : [a], this.onChange = proxy(c, d), this.cal = d
    }).prototype = {
        get: function () {
            return this.type == a.SEL_SINGLE ? this.sel[0] : this.sel
        },
        isEmpty: function () {
            return this.sel.length == 0
        },
        set: function (b, c, d) {
            var e = this.type == a.SEL_SINGLE; b instanceof Array ? (this.sel = b, this.normalize(), d || this.onChange(this)) : (b = dateToInt(b), e || !this.isSelected(b) ? (e ? this.sel = [b] : this.sel.splice(this.findInsertPos(b), 0, b), this.normalize(), d || this.onChange(this)) : c && this.unselect(b, d))
        },
        reset: function () {
            this.sel = [], this.set.apply(this, arguments)
        },
        countDays: function () {
            var a = 0, b = this.sel, c = b.length, d, e, f;
            while (--c >= 0)
                d = b[c],
            d instanceof Array && (e = intToDate(d[0]),
            f = intToDate(d[1]),
            a += Math.round(Math.abs(f.getTime() - e.getTime()) / 864e5)),
            ++a;
            return a;
        },
        unselect: function (a, b) {
            a = dateToInt(a);
            var c = !1;
            for (var d = this.sel, e = d.length, f; --e >= 0; ) {
                f = d[e];
                if (f instanceof Array) {
                    if (a >= f[0] && a <= f[1]) {
                        var g = intToDate(a),
                            h = g.getDate();
                        if (a == f[0])
                            g.setDate(h + 1),
                            f[0] = dateToInt(g),
                            c = !0;
                        else if (a == f[1])
                            g.setDate(h - 1),
                            f[1] = dateToInt(g),
                            c = !0;
                        else {
                            var i = new Date(g);
                            i.setDate(h + 1),
                            g.setDate(h - 1),
                            d.splice(e + 1, 0, [dateToInt(i), f[1]]),
                            f[1] = dateToInt(g),
                            c = !0
                        }
                    }
                }
                else
                    a == f && (d.splice(e, 1), c = !0)
            }

            c && (this.normalize(), b || this.onChange(this))
        },
        normalize: function () {
            this.sel = this.sel.sort(function (a, b) {
                a instanceof Array && (a = a[0]), b instanceof Array && (b = b[0]); return a - b
            });
            for (var a = this.sel, b = a.length, c, d; --b >= 0; ) {
                c = a[b];
                if (c instanceof Array) {
                    if (c[0] > c[1]) {
                        a.splice(b, 1); continue
                    }

                    c[0] == c[1] && (c = a[b] = c[0])
                }
                if (d) {
                    var e = d,
                        f = c instanceof Array ? c[1] : c;
                    f = intToDate(f),
                    f.setDate(f.getDate() + 1),
                    f = dateToInt(f);
                    if (f >= e) {
                        var g = a[b + 1];
                        c instanceof Array && g instanceof Array ? (c[1] = g[1], a.splice(b + 1, 1)) : c instanceof Array ? (c[1] = d, a.splice(b + 1, 1)) : g instanceof Array ? (g[0] = c, a.splice(b, 1)) : (a[b] = [c, g], a.splice(b + 1, 1))
                    }
                }

                d = c instanceof Array ? c[0] : c
            }
        },
        findInsertPos: function (a) {
            for (var b = this.sel, c = b.length, d; --c >= 0; ) { d = b[c], d instanceof Array && (d = d[0]); if (d <= a) break } return c + 1
        },
        clear: function (a) {
            this.sel = [], a || this.onChange(this)
        },
        selectRange: function (b, c) {
            b = dateToInt(b),
            c = dateToInt(c);
            if (b > c) {
                var d = b;

                b = c, c = d
            }
            var e = this.cal.args.checkRange;
            if (!e) return this._do_selectRange(b, c); try {
                bk((new a.Selection([[b, c]], a.SEL_MULTIPLE, bl)).getDates(), proxy(function (a) {
                    if (this.isDisabled(a)) {
                        e instanceof Function && e(a, this); throw "OUT"
                    }
                }, this.cal)),

                this._do_selectRange(b, c)
            } catch (f) { }
        },
        _do_selectRange: function (a, b) {
            this.sel.push([a, b]), this.normalize(), this.onChange(this)
        },
        isSelected: function (a) {
            for (var b = this.sel.length, c; --b >= 0; ) { c = this.sel[b]; if (c instanceof Array && a >= c[0] && a <= c[1] || a == c) return !0 } return !1
        },
        getFirstDate: function () {
            var a = this.sel[0]; a && a instanceof Array && (a = a[0]); return a
        },
        getLastDate: function () {
            if (this.sel.length > 0) { var a = this.sel[this.sel.length - 1]; a && a instanceof Array && (a = a[1]); return a }
        },
        print: function (a, b) {
            var c = [],
                d = 0,
                e,
                f = this.cal.getHours(),
                g = this.cal.getMinutes();

            b || (b = " -> ");

            while (d < this.sel.length)
                e = this.sel[d++],
                e instanceof Array ? c.push(printDate(intToDate(e[0], f, g), a) + b + printDate(intToDate(e[1], f, g), a)) : c.push(printDate(intToDate(e, f, g), a));

            return c
        },
        getDates: function (a) {
            var b = [], c = 0, d, e; while (c < this.sel.length) { e = this.sel[c++]; if (e instanceof Array) { d = intToDate(e[0]), e = e[1]; while (dateToInt(d) < e) b.push(a ? printDate(d, a) : new Date(d)), d.setDate(d.getDate() + 1) } else d = intToDate(e); b.push(a ? printDate(d, a) : d) } return b
        }
    },
    a.isUnicodeLetter = function (a) {
        return a.toUpperCase() != a.toLowerCase()
    },
    a.parseDate = function (b, c, d) {
        if (!/\S/.test(b))
            return "";
        b = b.replace(/^\s+/, "").replace(/\s+$/, ""),
        d = d || new Date;

        var e = null,
            f = null,
            g = null,
            h = null,
            i = null,
            j = null,
            k = b.match(/([0-9]{1,2}):([0-9]{1,2})(:[0-9]{1,2})?\s*(am|pm)?/i);
        k && (h = parseInt(k[1], 10),
        i = parseInt(k[2], 10),
        j = k[3] ? parseInt(k[3].substr(1), 10) : 0,
        b = b.substring(0, k.index) + b.substr(k.index + k[0].length),
        k[4] && (k[4].toLowerCase() == "pm" && h < 12 ? h += 12 : k[4].toLowerCase() == "am" && h >= 12 && (h -= 12)));

        var l = function () {
            function k(a) {
                d.push(a)
            }
            function j() {
                var a = "";
                while (g() && /[0-9]/.test(g())) a += f(); if (h(g())) return i(a); return parseInt(a, 10)
            }
            function i(a) {
                while (g() && h(g())) a += f(); return a
            }
            function g() {
                return b.charAt(c)
            }
            function f() {
                return b.charAt(c++)
            }

            var c = 0, d = [], e, h = a.isUnicodeLetter;
            while (c < b.length) e = g(), h(e) ? k(i("")) : /[0-9]/.test(e) ? k(j()) : f();

            return d
        } (),
        m = [];
        for (var n = 0; n < l.length; ++n) {
            var o = l[n];
            
            ///^[0-9]{4}$/.test(o) ? (e = parseInt(o, 10), f == null && g == null && c == null && (c = !0)) : /^[0-9]{1,2}$/.test(o) ? (o = parseInt(o, 10), o < 60 ? o < 0 || o > 12 ? o >= 1 && o <= 31 && (g = o) : m.push(o) : e = o) : f == null && (f = T(o))

            if (/^[0-9]{4}$/.test(o)) {
                e = parseInt(o, 10);
                f == null;
                g == null;
                c == null;
                c = true;
            } else if (/^[0-9]{1,2}$/.test(o)) {
                o = parseInt(o, 10);
                o < 60 ? o < 0 || o > 12 ? o >= 1 && o <= 31 && (g = o) : m.push(o) : e = o;
            } else {
                f == null;
                f = T(o);
            }
        }

        //m.length < 2 ? m.length == 1 && (g == null ? g = m.shift() : f == null && (f = m.shift())) : c ? (f == null && (f = m.shift()), g == null && (g = m.shift())) : (g == null && (g = m.shift()), f == null && (f = m.shift())), e == null && (e = m.length > 0 ? m.shift() : d.getFullYear()), e < 30 ? e += 2e3 : e < 99 && (e += 1900), f == null && (f = d.getMonth() + 1); return e != null && f != null && g != null ? new Date(e, f - 1, g, h, i, j) : null

        if (m.length < 2) {
            if (m.length == 1) {
                if (g == null) {
                    g = m.shift();
                }
                else if (f == null) {
                    f = m.shift();
                }
            }
        } else {
            if (c) {
                if (f == null) {
                    f = m.shift();
                }

                if (g == null) {
                    g = m.shift();
                }
            } else {
                if (g == null)
                    g = m.shift();
                if (f == null)
                    f = m.shift();
            }
        }

        if (e == null) {
            e = (m.length > 0) ? m.shift() : d.getFullYear();
        }

        if (e < 30) {
            e += 2000;
        } else if (e < 99) {
            e += 1900;
        }

        if (f == null)
            f = d.getMonth() + 1;
        if (e && f && g)
            return new Date(e, f - 1, g, h, i, j);

        return null;
    };
        var be = {
            elastic_b: function (a) { 
                return 1 - Math.cos(-a * 5.5 * Math.PI) / Math.pow(2, 7 * a);
            }, 
            magnetic: function (a) { 
                return 1 - Math.cos(a * a * a * 10.5 * Math.PI) / Math.exp(4 * a);
            }, 
            accel_b: function (a) { 
                a = 1 - a; return 1 - a * a * a * a;
            }, 
            accel_a: function (a) { 
                return a * a * a; 
            }, 
            accel_ab: function (a) { 
                a = 1 - a; 
                return 1 - Math.sin(a * a * Math.PI / 2);
            }, 
            accel_ab2: function (a) { 
                return (a /= .5) < 1 ? .5 * a * a : -0.5 * (--a * (a - 2) - 1); 
            }, 
            brakes: function (a) { 
                a = 1 - a; 
                return 1 - Math.sin(a * a * Math.PI);
            }, 
            shake: function (a) { 
                return a < .5 ? -Math.cos(a * 11 * Math.PI) * a * a : (a = 1 - a, Math.cos(a * 11 * Math.PI) * a * a);
            }
        },
    bl = new Function;
        return a
    } ();

    
    var emptyFunc = new Function();

    /** 
     * A wrapper class for JSCalendar to supprt better  
     */
    var EUCalendarWrapper = function() {
        EUCalendarWrapper.prototype._init.apply(this, arguments);
    }

    EUCalendarWrapper.prototype = {
        _default: {
            cont: null,
            bottomBar: true,
            date: true,
            min: null,
            max: null,
            selection: [],
            weekNumbers: false,
            align: "Bl/ / /T/r",
            inputField: null,
            trigger: null,
            manualTrigger: false, // Show manually calendar
            dateFormat: "yyyy-MM-dd",
            fixed: true,
            titleFormat: "MMM yyyy",
            showTime: false,
            timePos: "right",
            minuteStep: 5,
            disabled: emptyFunc,
            dateInfo: emptyFunc,
            onChange: emptyFunc,
            onSelect: emptyFunc,
            onTimeChange: emptyFunc,
            onFocus: emptyFunc,
            onBlur: emptyFunc
        },
        _init: function(element, options){
            // Input element or container element 
            this.$el = jQuery(element);

            // Original JSCalendar object
            this.originalCalendar = null;

            var opts = this.option = jQuery.extend({}, this._default, (options|| {}));

            if(!this.$el.is(':input')) {
                opts = jQuery.extend(opts, {cont: element});
            } else {
                opts = jQuery.extend(opts, {inputField: element});
            }
            
            opts.dateFormat = dotNet2CalendarFormat(opts.dateFormat);
            opts.titleFormat = dotNet2CalendarFormat(opts.titleFormat);

            this.originalCalendar = new Calendar(opts);
        },
        /**
         * Gets current select date of calendar
         */
        getDate: function() {
            var date = new Date(0);
            date.setMilliseconds(this.originalCalendar.date.getTime());
            return date;
        },
        /**
         * Gets current dateformat which is applied to calendar
         */
        getDateFormat: function() {
            return this.option.dateFormat;
        },
        /** 
         * Moves the calendar to the given date's year/month (this doesn't change the selection!). 
         * If you pass true for animated (default is false) and if animation wasn't disabled through 
         * the constructor, then it will animate the switch
         * 
         * @param {Date} date The date value to move to 
         */
        moveTo: function(date, animated) {
            this.originalCalendar.moveTo(date, animated);
        },
        /** 
         * Check whether a specified date is disabled
         * 
         * @param {Date} date The date value to check
         * @returns true/false  
         */
        isDisabled: function(date) {
            return this.originalCalendar.isDisabled(date);
        },
        /** 
         * Shows or hides the menu.
         */
        toggleMenu: function() {
            this.originalCalendar.toggleMenu();
        },
        /** 
         * Sets a new language for the calendar. Pass the language code
         * @param {String} code The code of language to set 
         */
        setLanguage: function(code) {
            this.originalCalendar.setLanguage(code);
        },
        /** 
         * Moves focus to the calendar. After you call this, the calendar handles keyboard. 
         */
        focus: function() {
            this.originalCalendar.focus();
        },
        /** 
         * Removes focus from the calendar  
         */
        blur: function() {
            this.originalCalendar.blur();
        },
        /** 
         * Only for popup calendars. Shows the calendar at the given (x, y) position, 
         * optionally animated if animated is true then calendar will show up with animation
         * 
         * @param {Number} x The x-coordinate
         * @param {Number} y The y-coordinate
         * @param {Boolean} animated Enable animation when calendar showing up
         */
        showAt: function(x, y, animated) {
            this.originalCalendar.showAt(x, y. animated);
        },
		toggle: function(target) {
			this.originalCalendar.toggle(target);
		},
        /** 
         * Hides the calendar (Only for popup calendars) 
         */
        hide: function() {
            this.originalCalendar.hide();
        },
        /**
         * Display the calendar near a certain element
         * 
         * @param {DOM element, String} element The reference to a DOM element, or the ID of an element where the calendar is anchored
         * @param {String} align The align decides where is the calendar displayed in relation to the given anchor element. Defaults to "Bl/ / /T/r".
         * It is formed of 5 parts joined by "/", in the following order:
         *      1. preferred alignment if there is enough room
         *      2. fallback alignment if it fails on the top
         *      3. fallback alignment if it fails on the right
         *      4. fallback alignment if it fails on the bottom
         *      5. fallback alignment if it fails on the left
         * 
         */
        popup: function(element, align) {
            this.originalCalendar.popup(element, align);
        },
        /**
         * Setup a single (popup) calendar instance for multiple input fields.
         */
        manageFields: function(trigger, inputField, dateFormat) {
            this.originalCalendar.manageFields(trigger, inputField, dateFormat);
        },
        /**
         * For a calendar configured to display the time selector, this function returns the currently selected time 
         * as an integer (hours * 100 + minutes). For example, if 9:45 pm is selected, this method returns 2145
         */
        getTime: function() {
            return this.originalCalendar.getTime();
        },
        /**
         * Sets the currently selected time. The time argument is in the same integer format. nohooks is optional (default false). 
         * If specified true then this method will not call the onTimeChange hooks. 
         * If unspecified or false the onTimeChange hooks will be called two arguments: a reference to the calendar object, and the new time as integer.
         */
        setTime: function(time, nohooks /* [optional] */) {
            this.originalCalendar.setTime(time, nohooks);
        },
        /**
         * Returns the currently selected hour as an integer (0 .. 23). Note that this method will return hours in 24h format 
         * regardless if the calendar is currently set in "12h" mode (i.e. when showTime == 12).
         */
        getHours: function() {
            return this.originalCalendar.getHours();
        },
        /**
         * Returns the currently selected minute as an integer (0 .. 59).
         */
        getMinutes: function() {
            return this.originalCalendar.getMinutes();
        },
        /**
         * Sets the hour in the time selector. The argument must be an integer (0 .. 23).
         */
        setHours: function(hour) {
            this.originalCalendar.setHours(hour);
        },
        /**
         * Sets the minutes in the time selector. The argument must be an integer (0 .. 59). Note that this method does not force m to a multiple of minuteStep.
         */
        setMinutes: function(min) {
            this.originalCalendar.setMinutes(min);
        },
        /**
         * Adds a handler for the given event. As of this writing, event can be "onSelect", "onChange", "onTimeChange", "onFocus" and "onBlur". 
         * Note that multiple handlers can exist for an event and they will be called in the order they were registered.
         */
        addEventListener: function(evt, func) {
            this.originalCalendar.addEventListener(evt, func);
        },
        /**
         * Removes the given func handler from the list of registered hooks for the given event.
         */
        removeEventListener: function(evt, func) {
            this.originalCalendar.removeEventListener(evt, func);
        }
    }
	
    EUCalendarWrapper.formatDate = function(date, format) {
        return Calendar.printDate(date, dotNet2CalendarFormat(format));
    }

    EUCalendarWrapper.formatString = function(str, prop) {
        return Calendar.formatString(str, prop);
    }

    if(typeof jQuery != 'undefined') {
        function Plugin(option) {
            return this.each(function(){
                var $this = jQuery(this);
                var data = $this.data('eu.calendar');
                if(!data) $this.data('eu.calendar', (data = new EUCalendarWrapper(this, option)));
                if(typeof option == 'string' && typeof data[option] == 'function') data[option]();
            });
        }

        jQuery.eucalendar = EUCalendarWrapper;
        // Make wrapper avaible for jQuery plugin
        jQuery.fn.eucalendar = Plugin;
        jQuery.fn.eucalendar.Construtor = EUCalendarWrapper;
    } else {
        window.eucalendar = EUCalendarWrapper;
    }
    
})();