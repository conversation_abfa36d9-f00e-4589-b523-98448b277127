﻿@using Fragulizer.Common;
@{
    Layout = null;
    List<Fragulizer.Models.Partials.MarketInfo> lstMarketInfo = (List<Fragulizer.Models.Partials.MarketInfo>) ViewBag.MarketInfo;
    DateTime rightNow = TimeZoneInfo.ConvertTime(DateTime.UtcNow, TimeZoneInfo.Utc, TimeZoneInfo.FindSystemTimeZoneById(Utility.GetPageTimeZoneID()));
    string downloadPdfUrl = ViewBag.DownloadPdfToolUrl;
}
@using Euroland.Azure.Shared.ClientResource;
@using ToolsFramework;
@using ToolsFramework.Mvc;
<!DOCTYPE html>
<html lang="@Tool.Culture.Name.ToLower()">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE-edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <title>@Translations.HEADING_TEXT</title>
    @{
        
        Html.Css()
            .Add("baselib", new string[] { 
                "~/Content/eucalendar.css", 
                "~/Content/tooltip/etipstyle.css", 
                "~/Content/global.css" 
            })
            .Add("toolStyle", Tool.Settings.GetToolsStyleFileUri(), true)
            .Render();
    }
    @{Html.RenderPartial("StyleTemplate");}
    @{
        if (Tool.Settings.googleanalyticsenabled)        
        {
            //Html.GoogleAnalytics();
            <script type="text/javascript" src="/tools/common/google-analytics.js"></script>
        }
     }
</head>
@if (RequestHelper.TwoLetterLanguage.ToLower() == "ar")
{
@:<body dir="rtl" latin="@ViewBag.LatinNumber" class="@Tool.Culture.Name.ToLower() page-font-style body-rtl">
}
else
{ 
@:<body class="@Tool.Culture.Name.ToLower() page-font-style">
}
    <div class="wrapper @(ViewBag.IsExporting ? "exporting": "")" id="wrapper">
         @if(Tool.Settings.enableheading)
         {
            <div class="main-heading">@Translations.HEADING_TEXT</div>
         }

        <div class="share-type-selection">
            @{Html.RenderPartial("~/Views/Home/ShareTypeSelection.cshtml");}
        </div>
        @if (Tool.Settings.enableinstrumentstable)
        {
        <div class="date-time-heading">
            <span class="time-label">@Html.Raw(Translations.DATE_TIME.ToHtmlString())</span> <span class="time">
            @rightNow.ToString("dddd, " + Tool.Culture.DateTimeFormat.LongDatePattern + " " 
                                        + Tool.Culture.DateTimeFormat.ShortTimePattern)
                (@Fragulizer.Common.Utility.GetCurrentTimezone())</span>
        </div>

        <div class="share-selection">
            <table width="100%" cellpadding="0" cellspacing="0" class="table-share-info border-outline">
                <tr>
                    <td class="table-header table-of-instruments-header td-exchange-header">
                        @Translations.MARKET
                    </td>
                    @if (Tool.Settings.showcurrencycolumn)
                    {
                        @:<td class="table-header table-of-instruments-header td-currency-header">@Translations.CURRENCY</td>
                    }
                    <td class="table-header table-of-instruments-header td-last-header">
                        @Translations.LAST
                    </td>
                    <td class="table-header table-of-instruments-header td-change-header">
                        @Translations.CHANGE
                    </td>
                    <td class="table-header table-of-instruments-header td-high-header">
                        @Translations.HIGH
                    </td>
                    <td class="table-header table-of-instruments-header td-low-header">
                        @Translations.LOW
                    </td>
                    <td class="table-header table-of-instruments-header td-volume-header">
                        @Translations.VOLUME
                    </td>
                    <td class="table-header table-of-instruments-header td-time-header">
                        @Translations.TIME
                    </td>
                </tr>
                <tbody class="share-info-body">
                </tbody>
            </table>
            <div class="latest-trading-notice"></div>
        </div>
        }
        <div class="pisition-relative">
            <div class="loading display-none">
            </div>
        </div>
        <div class="fragmentation">
            <div class="fragmentation-heading">
                <div class="fragmentation-title">
                    <span class="title second-heading fragmentation-title-text">
                        @Translations.FRAGMENTATION</span>
                    <span class="selected-period fragmentation-period"></span>
                </div>
                <div class="period-selection">
                    <div class="period-container">
                        <ul>
                            <li><a href="javascript:void(0)" id="periodLive" period="LIVE" class="period-button @(Tool.Settings.defaultperiod.ToUpper() == "LIVE" ? "period-selected" : "")">@Translations.LIVE</a>
                            </li>
                            <li><a href="javascript:void(0)" id="period1M" period="1M" class="period-button @(Tool.Settings.defaultperiod.ToUpper() == "1M" ? "period-selected" : "")">@Translations.PERIOD_1_MONTH</a> </li>
                            <li><a href="javascript:void(0)" id="period3M" period="3M" class="period-button @(Tool.Settings.defaultperiod.ToUpper() == "3M" ? "period-selected" : "")">@Translations.PERIOD_3_MONTH</a> </li>
                            <li><a href="javascript:void(0)" id="period6M" period="6M" class="period-button @(Tool.Settings.defaultperiod.ToUpper() == "6M" ? "period-selected" : "")">@Translations.PERIOD_6_MONTH</a> </li>
                            <li><a href="javascript:void(0)" id="period1Y" period="1Y" class="period-button @(Tool.Settings.defaultperiod.ToUpper() == "1Y" ? "period-selected" : "")">@Translations.PERIOD_1_YEAR</a> </li>
                        </ul>
                    </div>
                    <div class="period-title">
                    <a href="javascript:void(0)" id="customperiod" period="CUSTOM">@Translations.SELECT_TIME_PERIOD</a>
                        <div class="calendar-Container">
                            <div class="custom-range-calendar" id="custom-range-calendar">
                                <div class="custom-range-calendar-header">
                                    <div class="custom-range-title">@Translations.CREATE_A_CUSTOM_TIMEFRAME</div>
                                    <div class="close-icon" title="Close" previousperiod="1day">
                                        <img src="@Url.ToAbsolute("~/Content/images/close.gif")" alt="" />
                                    </div>
                                </div>
                                <table>
                                    <tr>
                                        <td class="datepicker"><span id="from_calendar"></span></td>
                                        <td width="1px" class="td-calendar-spearator"></td>
                                        <td class="datepicker"><span id="to_calendar"></span></td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" class="button-custom-range"><input type="button" class="show-data" value="OK"/></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="chart-container" class="clear">
            </div>
            <div id="live-alert" class="live-alert"></div>
            <div id="legend-info">
            </div>
            <div style="clear: both;"></div>
        </div>
        <div class="clear market-share-pie-chart" style="display:none;">
            <div class="market-share-heading">
                <span class="title second-heading">@Translations.MARKET_SHARE</span> <span class="selected-period"></span>
            </div>
            <div class="market-share-container">
                <div class="market-share">                    
                    <table class="table-market-share border-outline" cellpadding="0" cellspacing="0">
                        <thead>
                            <tr>
                                <td class="table-header table-of-market-share-header td-exchange-header">
                                    @Translations.MARKET
                                </td>
                                <td class="table-header table-of-market-share-header td-volume-header">
                                    @Translations.VOLUME
                                </td>
                                <td class="table-header table-of-market-share-header td-changepro-header">
                                    @Translations.PERCENTAGE
                                </td>
                            </tr>
                        </thead>
                        <tbody class="market-info-body">
                        </tbody>
                    </table>
                </div>
                <div class="pie-chart-container">
                    <div id="pie-chart">
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>
        </div>
        @if (Tool.Settings.enableactivitytrend)
        {
            <div class="activity-trend clear" style="display: none;">
                <div class="title second-heading activity-trend-heading">
                    @Translations.ACTIVITY_TREND
                </div>
                <div id="columnChartArea"></div>
            </div>
        }
        <div class="footer clear" style="display: none;">
            <div class="command-button">
                @if(Tool.Settings.enableprint)
                {
                    <a href="javascript:window.print();" class="print-icon" etiptext="@Translations.PRINT">
                        <img src="@Url.Content("~/Content/Images/print.png")" style="width: 32px; height: 32px; margin: 6px; opacity: 0.75;">
                    </a>
                }
                @if(Tool.Settings.enablejpgdownload)
                {
                    <a href="javascript:void(0);" class="jpg-icon" etiptext="@Translations.EXPORT_TO_JPG" target="_blank">
                        <img src="@Url.Content("~/Content/Images/jpg.png")" style="width: 32px; height: 32px; margin: 6px; opacity: 0.75;">
                    </a>
                }
                @if(Tool.Settings.enablepdfdownload)
                {
                    <a href="javascript:void(0);" class="pdf-icon" etiptext="@Translations.EXPORT_TO_PDF" target="_blank">
                        <img src="@Url.Content("~/Content/Images/pdf.png")" style="width: 32px; height: 32px; margin: 6px; opacity: 0.75;">
                    </a>
                }
                @if (Tool.Settings.enableexceldownload)
                {
                    <a href="javascript:void(0);" class="excel-icon" etiptext="@Translations.EXPORT_TO_EXCEL">
                        <img src="@Url.Content("~/Content/Images/xls.png")" style="width: 32px; height: 32px; margin: 6px; opacity: 0.75;">
                    </a>
                }
            </div>
            <div class="footer-info">
                    @if(Tool.Settings.showsupplierinfo)
                    {
                        <div class="supply-by">
                            @if (Tool.Settings.showsupplierinfolink)
                            {
                            <span>
                                    @Html.Raw(Translations.SUPPLIED_BY_TEXT.ToString().Replace("@", "<a href=\"" + Fragulizer.Common.Utility.GetSupplyHyperlink() + "\" target=\"_blank\" class=\"hyperlink\">Euroland.com</a>"))
                            </span>
                            }
                            else
                            {
                            <span>
                                    @Translations.SUPPLIED_BY_TEXT.ToString().Replace("@", "Euroland.com")
                            </span>
                            }
                        </div>
                    }
                
                <div>
                    @Html.Raw(Utility.GetFooterString(lstMarketInfo))
                </div>
                    @if (Tool.Settings.showdisclaimerinfo)
                    {
                        <div class="disclaimer-box">
                            @{
                        Html.DisclaimerHyperlink(Translations.TERM_DATA_VENDOR_INFORMATION.ToString(), Translations.DISCLAIMER.ToString(), Tool.Culture.Name);
                            }
                        </div>
                    }
                    
                    @if(Tool.Settings.showcookiepolicyinfo)
                    {
                        <div class="cookies-box">
                            @{Html.CookiesHyperlink(Translations.COOKIES_POLICY.ToString(), Tool.Culture.Name);}
                        </div>
                    }
        </div>
        </div>
    </div>
    <div id="divHTMLTemplate" class="display-none">
        <table width="100%" cellpadding="0" cellspacing="0" class="table-share-info-temp">
            <thead>
                <tr>
                    <td class="table-header table-of-instruments-header td-exchange-header">
                        @Translations.MARKET
                    </td>
                    <td class="table-header table-of-instruments-header td-currency-header">
                        @Translations.CURRENCY
                    </td>
                    <td class="table-header table-of-instruments-header td-last-header">
                        @Translations.LAST
                    </td>
                    <td class="table-header table-of-instruments-header td-change-header">
                        @Translations.CHANGE
                    </td>
                    <td class="table-header table-of-instruments-header td-high-header">
                        @Translations.HIGH
                    </td>
                    <td class="table-header table-of-instruments-header td-low-header">
                        @Translations.LOW
                    </td>
                    <td class="table-header table-of-instruments-header td-volume-header">
                        @Translations.VOLUME
                    </td>
                    <td class="table-header table-of-instruments-header td-time-header">
                        @Translations.TIME
                    </td>
                </tr>
            </thead>
            <tbody>
                <tr id="{InstrumenId}" marketid="{MarketId}" class="share">
                    <td class="table-row table-of-instrument-row td-market-name border-inline">
                        {MarketName}
                        @if (Tool.Settings.showcurrencycolumn)
                        {
                            <span class="share-currency">({Currency})</span>
                        }
                    </td>
                     @if (Tool.Settings.showcurrencycolumn)
                     {
                        @:<td class="table-row table-of-instrument-row td-currency border-inline">{Currency}</td>
                      }
                    <td class="table-row table-of-instrument-row td-last border-inline">
                        {Last}
                    </td>
                    <td class="table-row table-of-instrument-row td-change border-inline {change_percent}">
                        {ChangePro}%
                    </td>
                    <td class="table-row table-of-instrument-row td-high border-inline">
                        {High}
                    </td>
                    <td class="table-row table-of-instrument-row td-low border-inline">
                        {Low}
                    </td>
                    <td class="table-row table-of-instrument-row td-volume border-inline">
                        {Volume}
                    </td>
                    <td class="table-row table-of-instrument-row td-time border-inline">
                        {Time}
                    </td>
                </tr>
            </tbody>
        </table>
        <table class="table-market-share-temp" cellpadding="0" cellspacing="0">
            <thead>
                <tr>
                    <td class="table-header table-of-market-share-header td-exchange-header">
                        @Translations.MARKET
                    </td>
                    <td class="table-header table-of-market-share-header td-volume-header">
                       @Translations.VOLUME
                    </td>
                    <td class="table-header table-of-market-share-header td-changepro-header">
                        @Translations.PERCENTAGE
                    </td>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="table-row market-share-row td-exchange border-inline">
                        {MarketName}
                    </td>
                    <td class="table-row market-share-row td-volume border-inline">
                        {Volume}
                    </td>
                    <td class="table-row market-share-row td-changepro border-inline">
                        {ChangePro}
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <script type="text/javascript" src="/tools/common/EurolandIFrameAutoHeight/EurolandToolAutoSizeObject.js"></script>
    @{
        Html.Js()
            .Add("baseLib", "~/Scripts/lib/jquery.js")
            .Add("baseLib", "~/Scripts/JsExtentions.js")
            .Add("baseLib", "~/Scripts/lib/euroland.etools.js")
            .Add("baseLib", "~/Scripts/lib/eucalendar.js")
            .Add("baseLib", "~/Scripts/lib/highstock.js")
            .Add("baseLib", "~/Scripts/lib/exporting.js")
            .Add("baseLib", "~/Scripts/lib/eu.responsive.js")
            .Render();
    }
    
    @{
        Html.JsLanguagePack(Tool.Culture, (id) => Services.Get<ILanguageService>().GetTranslation(id));
        Html.Js()
                 .Add("pageScript", "~/Scripts/Fragulizer.js")
        .Render();
    }
	
    <script type="text/javascript">
        var baseUri = '@Url.Content("~/")';
        var lang = '@RequestHelper.lang.ToLower()';
        var langShort = '@RequestHelper.TwoLetterLanguage.ToLower()';
        $.extend(Settings, {
            downloadUrl: @Html.SerializeJs(downloadPdfUrl),
            companyCode:  @Html.SerializeJs(RequestHelper.CompanyCode),
            dateFormat: @Html.SerializeJs(Tool.Culture.DateTimeFormat.ShortDatePattern),
            longDateFormat:@Html.SerializeJs(Tool.Culture.DateTimeFormat.LongDatePattern),
            dateSeparator: @Html.SerializeJs(Tool.Culture.DateTimeFormat.DateSeparator),
            decimalSeparator: @Html.SerializeJs(Tool.Culture.NumberFormat.NumberDecimalSeparator),
            thousandSeparator: @Html.SerializeJs(Tool.Culture.NumberFormat.NumberGroupSeparator),
            decimalDigits:@Html.SerializeJs(Tool.Culture.NumberFormat.NumberDecimalDigits),
            sharepriceNumberDecimalDigits:@Html.SerializeJs(Tool.Settings.sharepricenumberdecimaldigits),
            sharePricePercentageDecimalDigits:@Html.SerializeJs(Tool.Settings.sharepricepercentagedecimaldigits),
            negativeNumberFormat:@Html.SerializeJs(Tool.Culture.NumberFormat.NumberNegativePattern),
            shortTime: @Html.SerializeJs(Tool.Settings.format[Tool.Culture.Name]["ShortTime"].Value ?? Tool.Culture.DateTimeFormat.ShortTimePattern),
            longTime: @Html.SerializeJs(Tool.Settings.format[Tool.Culture.Name]["LongTime"].Value ?? Tool.Culture.DateTimeFormat.LongTimePattern),
            pagetimezone: @ViewBag.PageTimeZone,
            servertimezone: @ViewBag.ServerTimeZone,
            enableHeading: @Html.SerializeJs(Tool.Settings.enableheading),
            enableInstrumentsTable:@Html.SerializeJs(Tool.Settings.enableinstrumentstable),
            enableActivityTrend:@Html.SerializeJs(Tool.Settings.enableactivitytrend),
            fontStyle: {
                fontFamily:  @Html.SerializeJs(Tool.Settings.maintextfont),
                fontSize:  @Html.SerializeJs(Tool.Settings.maintextfontsize),
                color:  @Html.SerializeJs(Tool.Settings.maintextcolor)
            },
            SelectedPeriod: @Html.SerializeJs(Tool.Settings.defaultperiod),
            chartColors:@Html.SerializeJs(Tool.Settings.chartcolors.Trim().Split(';').ToArray()),
            ChartBGColor:@Html.SerializeJs(Tool.Settings.chartbgcolor),
            pieBorderWidth: @Html.SerializeJs(Tool.Settings.pieborderwidth)
        });
        $.extend(Labels, {
            msgNoDataAvailable:  @Html.SerializeJs(Translations.NO_DATA_AVAILABLE.ToString()),
            selectedperiod: @Html.SerializeJs(Translations.SELECTED_PERIOD.ToString()),
            lastupdate: @Html.SerializeJs(Translations.LAST_UPDATE.ToString())
        });
    </script>

    @if (ViewBag.IsExporting)
    {
        <img id="fakeAsyncActivity" src="@Url.Content("~/home/<USER>")" />
    }
</body>
</html>
