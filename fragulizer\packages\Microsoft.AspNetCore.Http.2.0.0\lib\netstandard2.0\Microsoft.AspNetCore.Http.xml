<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Http</name>
    </assembly>
    <members>
        <member name="P:Microsoft.AspNetCore.Http.DefaultHttpContext.Authentication">
            <summary>
            This is obsolete and will be removed in a future version. 
            The recommended alternative is to use Microsoft.AspNetCore.Authentication.AuthenticationHttpContextExtensions.
            See https://go.microsoft.com/fwlink/?linkid=845470.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Features.DefaultSessionFeature">
            <summary>
            This type exists only for the purpose of unit testing where the user can directly set the
            <see cref="P:Microsoft.AspNetCore.Http.HttpContext.Session"/> property without the need for creating a <see cref="T:Microsoft.AspNetCore.Http.Features.ISessionFeature"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FormOptions.BufferBody">
            <summary>
            Enables full request body buffering. Use this if multiple components need to read the raw stream.
            The default value is false.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FormOptions.MemoryBufferThreshold">
            <summary>
            If <see cref="P:Microsoft.AspNetCore.Http.Features.FormOptions.BufferBody"/> is enabled, this many bytes of the body will be buffered in memory.
            If this threshold is exceeded then the buffer will be moved to a temp file on disk instead.
            This also applies when buffering individual multipart section bodies.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FormOptions.BufferBodyLengthLimit">
            <summary>
            If <see cref="P:Microsoft.AspNetCore.Http.Features.FormOptions.BufferBody"/> is enabled, this is the limit for the total number of bytes that will
            be buffered. Forms that exceed this limit will throw an <see cref="T:System.IO.InvalidDataException"/> when parsed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FormOptions.ValueCountLimit">
            <summary>
            A limit for the number of form entries to allow.
            Forms that exceed this limit will throw an <see cref="T:System.IO.InvalidDataException"/> when parsed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FormOptions.KeyLengthLimit">
            <summary>
            A limit on the length of individual keys. Forms containing keys that exceed this limit will
            throw an <see cref="T:System.IO.InvalidDataException"/> when parsed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FormOptions.ValueLengthLimit">
            <summary>
            A limit on the length of individual form values. Forms containing values that exceed this
            limit will throw an <see cref="T:System.IO.InvalidDataException"/> when parsed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FormOptions.MultipartBoundaryLengthLimit">
            <summary>
            A limit for the length of the boundary identifier. Forms with boundaries that exceed this
            limit will throw an <see cref="T:System.IO.InvalidDataException"/> when parsed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FormOptions.MultipartHeadersCountLimit">
            <summary>
            A limit for the number of headers to allow in each multipart section. Headers with the same name will
            be combined. Form sections that exceed this limit will throw an <see cref="T:System.IO.InvalidDataException"/>
            when parsed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FormOptions.MultipartHeadersLengthLimit">
            <summary>
            A limit for the total length of the header keys and values in each multipart section.
            Form sections that exceed this limit will throw an <see cref="T:System.IO.InvalidDataException"/> when parsed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.FormOptions.MultipartBodyLengthLimit">
            <summary>
            A limit for the length of each multipart body. Forms sections that exceed this limit will throw an
            <see cref="T:System.IO.InvalidDataException"/> when parsed.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Features.ResponseCookiesFeature">
            <summary>
            Default implementation of <see cref="T:Microsoft.AspNetCore.Http.Features.IResponseCookiesFeature"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.ResponseCookiesFeature.#ctor(Microsoft.AspNetCore.Http.Features.IFeatureCollection)">
            <summary>
            Initializes a new <see cref="T:Microsoft.AspNetCore.Http.Features.ResponseCookiesFeature"/> instance.
            </summary>
            <param name="features">
            <see cref="T:Microsoft.AspNetCore.Http.Features.IFeatureCollection"/> containing all defined features, including this
            <see cref="T:Microsoft.AspNetCore.Http.Features.IResponseCookiesFeature"/> and the <see cref="T:Microsoft.AspNetCore.Http.Features.IHttpResponseFeature"/>.
            </param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Features.ResponseCookiesFeature.#ctor(Microsoft.AspNetCore.Http.Features.IFeatureCollection,Microsoft.Extensions.ObjectPool.ObjectPool{System.Text.StringBuilder})">
            <summary>
            Initializes a new <see cref="T:Microsoft.AspNetCore.Http.Features.ResponseCookiesFeature"/> instance.
            </summary>
            <param name="features">
            <see cref="T:Microsoft.AspNetCore.Http.Features.IFeatureCollection"/> containing all defined features, including this
            <see cref="T:Microsoft.AspNetCore.Http.Features.IResponseCookiesFeature"/> and the <see cref="T:Microsoft.AspNetCore.Http.Features.IHttpResponseFeature"/>.
            </param>
            <param name="builderPool">The <see cref="T:Microsoft.Extensions.ObjectPool.ObjectPool`1"/>, if available.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Features.ResponseCookiesFeature.Cookies">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.AspNetCore.Http.FormCollection">
            <summary>
            Contains the parsed form values.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.FormCollection.Item(System.String)">
            <summary>
            Get or sets the associated value from the collection as a single string.
            </summary>
            <param name="key">The header name.</param>
            <returns>the associated value from the collection as a StringValues or StringValues.Empty if the key is not present.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.FormCollection.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" />;.
            </summary>
            <returns>The number of elements contained in the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" />.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.FormCollection.ContainsKey(System.String)">
            <summary>
            Determines whether the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" /> contains a specific key.
            </summary>
            <param name="key">The key.</param>
            <returns>true if the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" /> contains a specific key; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.FormCollection.TryGetValue(System.String,Microsoft.Extensions.Primitives.StringValues@)">
            <summary>
            Retrieves a value from the dictionary.
            </summary>
            <param name="key">The header name.</param>
            <param name="value">The value.</param>
            <returns>true if the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" /> contains the key; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.FormCollection.GetEnumerator">
            <summary>
            Returns an struct enumerator that iterates through a collection without boxing and is also used via the <see cref="T:Microsoft.AspNetCore.Http.IFormCollection" /> interface.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Http.FormCollection.Enumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.FormCollection.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{System#String,Microsoft#Extensions#Primitives#StringValues}}#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection, boxes in non-empty path.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.FormCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection, boxes in non-empty path.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.HeaderDictionary">
            <summary>
            Represents a wrapper for RequestHeaders and ResponseHeaders.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HeaderDictionary.Item(System.String)">
            <summary>
            Get or sets the associated value from the collection as a single string.
            </summary>
            <param name="key">The header name.</param>
            <returns>the associated value from the collection as a StringValues or StringValues.Empty if the key is not present.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HeaderDictionary.System#Collections#Generic#IDictionary{System#String,Microsoft#Extensions#Primitives#StringValues}#Item(System.String)">
            <summary>
            Throws KeyNotFoundException if the key is not present.
            </summary>
            <param name="key">The header name.</param>
            <returns></returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HeaderDictionary.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" />;.
            </summary>
            <returns>The number of elements contained in the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" />.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HeaderDictionary.IsReadOnly">
            <summary>
            Gets a value that indicates whether the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" /> is in read-only mode.
            </summary>
            <returns>true if the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" /> is in read-only mode; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionary.Add(System.Collections.Generic.KeyValuePair{System.String,Microsoft.Extensions.Primitives.StringValues})">
            <summary>
            Adds a new list of items to the collection.
            </summary>
            <param name="item">The item to add.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionary.Add(System.String,Microsoft.Extensions.Primitives.StringValues)">
            <summary>
            Adds the given header and values to the collection.
            </summary>
            <param name="key">The header name.</param>
            <param name="value">The header values.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionary.Clear">
            <summary>
            Clears the entire list of objects.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionary.Contains(System.Collections.Generic.KeyValuePair{System.String,Microsoft.Extensions.Primitives.StringValues})">
            <summary>
            Returns a value indicating whether the specified object occurs within this collection.
            </summary>
            <param name="item">The item.</param>
            <returns>true if the specified object occurs within this collection; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionary.ContainsKey(System.String)">
            <summary>
            Determines whether the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" /> contains a specific key.
            </summary>
            <param name="key">The key.</param>
            <returns>true if the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" /> contains a specific key; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionary.CopyTo(System.Collections.Generic.KeyValuePair{System.String,Microsoft.Extensions.Primitives.StringValues}[],System.Int32)">
            <summary>
            Copies the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" /> elements to a one-dimensional Array instance at the specified index.
            </summary>
            <param name="array">The one-dimensional Array that is the destination of the specified objects copied from the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" />.</param>
            <param name="arrayIndex">The zero-based index in <paramref name="array" /> at which copying begins.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionary.Remove(System.Collections.Generic.KeyValuePair{System.String,Microsoft.Extensions.Primitives.StringValues})">
            <summary>
            Removes the given item from the the collection.
            </summary>
            <param name="item">The item.</param>
            <returns>true if the specified object was removed from the collection; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionary.Remove(System.String)">
            <summary>
            Removes the given header from the collection.
            </summary>
            <param name="key">The header name.</param>
            <returns>true if the specified object was removed from the collection; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionary.TryGetValue(System.String,Microsoft.Extensions.Primitives.StringValues@)">
            <summary>
            Retrieves a value from the dictionary.
            </summary>
            <param name="key">The header name.</param>
            <param name="value">The value.</param>
            <returns>true if the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" /> contains the key; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionary.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary.Enumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionary.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{System#String,Microsoft#Extensions#Primitives#StringValues}}#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionary.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Internal.DefaultConnectionInfo.Id">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Internal.FormFile.ContentDisposition">
            <summary>
            Gets the raw Content-Disposition header of the uploaded file.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Internal.FormFile.ContentType">
            <summary>
            Gets the raw Content-Type header of the uploaded file.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Internal.FormFile.Headers">
            <summary>
            Gets the header dictionary of the uploaded file.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Internal.FormFile.Length">
            <summary>
            Gets the file length in bytes.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Internal.FormFile.Name">
            <summary>
            Gets the name from the Content-Disposition header.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Internal.FormFile.FileName">
            <summary>
            Gets the file name from the Content-Disposition header.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.FormFile.OpenReadStream">
            <summary>
            Opens the request stream for reading the uploaded file.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.FormFile.CopyTo(System.IO.Stream)">
            <summary>
            Copies the contents of the uploaded file to the <paramref name="target"/> stream.
            </summary>
            <param name="target">The stream to copy the file contents to.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.FormFile.CopyToAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Asynchronously copies the contents of the uploaded file to the <paramref name="target"/> stream.
            </summary>
            <param name="target">The stream to copy the file contents to.</param>
            <param name="cancellationToken"></param>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Internal.QueryCollection">
            <summary>
            The HttpRequest query string collection
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Internal.QueryCollection.Item(System.String)">
            <summary>
            Get or sets the associated value from the collection as a single string.
            </summary>
            <param name="key">The header name.</param>
            <returns>the associated value from the collection as a StringValues or StringValues.Empty if the key is not present.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Internal.QueryCollection.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" />;.
            </summary>
            <returns>The number of elements contained in the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" />.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.QueryCollection.ContainsKey(System.String)">
            <summary>
            Determines whether the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" /> contains a specific key.
            </summary>
            <param name="key">The key.</param>
            <returns>true if the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" /> contains a specific key; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.QueryCollection.TryGetValue(System.String,Microsoft.Extensions.Primitives.StringValues@)">
            <summary>
            Retrieves a value from the dictionary.
            </summary>
            <param name="key">The header name.</param>
            <param name="value">The value.</param>
            <returns>true if the <see cref="T:Microsoft.AspNetCore.Http.HeaderDictionary" /> contains the key; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.QueryCollection.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Http.Internal.QueryCollection.Enumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.QueryCollection.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{System#String,Microsoft#Extensions#Primitives#StringValues}}#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.QueryCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Internal.ReferenceReadStream">
            <summary>
            A Stream that wraps another stream starting at a certain offset and reading for the given length.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.RequestCookieCollection.GetEnumerator">
            <summary>
            Returns an struct enumerator that iterates through a collection without boxing.
            </summary>
            <returns>An <see cref="T:Microsoft.AspNetCore.Http.Internal.RequestCookieCollection.Enumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.RequestCookieCollection.System#Collections#Generic#IEnumerable{System#Collections#Generic#KeyValuePair{System#String,System#String}}#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection, boxes in non-empty path.
            </summary>
            <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.RequestCookieCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection, boxes in non-empty path.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Internal.ResponseCookies">
            <summary>
            A wrapper for the response Set-Cookie header.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.ResponseCookies.#ctor(Microsoft.AspNetCore.Http.IHeaderDictionary,Microsoft.Extensions.ObjectPool.ObjectPool{System.Text.StringBuilder})">
            <summary>
            Create a new wrapper.
            </summary>
            <param name="headers">The <see cref="T:Microsoft.AspNetCore.Http.IHeaderDictionary"/> for the response.</param>
            <param name="builderPool">The <see cref="T:Microsoft.Extensions.ObjectPool.ObjectPool`1"/>, if available.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.ResponseCookies.Append(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.ResponseCookies.Append(System.String,System.String,Microsoft.AspNetCore.Http.CookieOptions)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.ResponseCookies.Delete(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Internal.ResponseCookies.Delete(System.String,Microsoft.AspNetCore.Http.CookieOptions)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.AspNetCore.Http.RequestFormReaderExtensions.ReadFormAsync(Microsoft.AspNetCore.Http.HttpRequest,Microsoft.AspNetCore.Http.Features.FormOptions,System.Threading.CancellationToken)">
            <summary>
            Read the request body as a form with the given options. These options will only be used
            if the form has not already been read.
            </summary>
            <param name="request">The request.</param>
            <param name="options">Options for reading the form.</param>
            <param name="cancellationToken"></param>
            <returns>The parsed form.</returns>
        </member>
    </members>
</doc>
