<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.AspNetCore.Http.Abstractions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.AspNetCore.Http.Authentication.AuthenticateInfo">
            <summary>
            Used to store the results of an Authenticate call.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Authentication.AuthenticateInfo.Principal">
            <summary>
            The <see cref="T:System.Security.Claims.ClaimsPrincipal"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Authentication.AuthenticateInfo.Properties">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Http.Authentication.AuthenticationProperties"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Authentication.AuthenticateInfo.Description">
            <summary>
            The <see cref="T:Microsoft.AspNetCore.Http.Authentication.AuthenticationDescription"/>.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Authentication.AuthenticationDescription">
            <summary>
            Contains information describing an authentication provider.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Authentication.AuthenticationDescription.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Authentication.AuthenticationDescription"/> class
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Authentication.AuthenticationDescription.#ctor(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Authentication.AuthenticationDescription"/> class
            </summary>
            <param name="items"></param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Authentication.AuthenticationDescription.Items">
            <summary>
            Contains metadata about the authentication provider.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Authentication.AuthenticationDescription.AuthenticationScheme">
            <summary>
            Gets or sets the name used to reference the authentication middleware instance.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Authentication.AuthenticationDescription.DisplayName">
            <summary>
            Gets or sets the display name for the authentication provider.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.Authentication.AuthenticationManager.AutomaticScheme">
            <summary>
            Constant used to represent the automatic scheme
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Authentication.AuthenticationManager.ForbidAsync">
            <summary>
            Creates a challenge for the authentication manager with <see cref="F:Microsoft.AspNetCore.Http.Features.Authentication.ChallengeBehavior.Forbidden"/>.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous challenge operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Authentication.AuthenticationManager.ForbidAsync(Microsoft.AspNetCore.Http.Authentication.AuthenticationProperties)">
            <summary>
            Creates a challenge for the authentication manager with <see cref="F:Microsoft.AspNetCore.Http.Features.Authentication.ChallengeBehavior.Forbidden"/>.
            </summary>
            <param name="properties">Additional arbitrary values which may be used by particular authentication types.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous challenge operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.Authentication.AuthenticationProperties">
            <summary>
            Dictionary used to store state values about the authentication session.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Authentication.AuthenticationProperties.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Authentication.AuthenticationProperties"/> class
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Authentication.AuthenticationProperties.#ctor(System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.AspNetCore.Http.Authentication.AuthenticationProperties"/> class
            </summary>
            <param name="items"></param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Authentication.AuthenticationProperties.Items">
            <summary>
            State values about the authentication session.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Authentication.AuthenticationProperties.IsPersistent">
            <summary>
            Gets or sets whether the authentication session is persisted across multiple requests.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Authentication.AuthenticationProperties.RedirectUri">
            <summary>
            Gets or sets the full path or absolute URI to be used as an http redirect response value.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Authentication.AuthenticationProperties.IssuedUtc">
            <summary>
            Gets or sets the time at which the authentication ticket was issued.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Authentication.AuthenticationProperties.ExpiresUtc">
            <summary>
            Gets or sets the time at which the authentication ticket expires.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Authentication.AuthenticationProperties.AllowRefresh">
            <summary>
            Gets or sets if refreshing the authentication session should be allowed.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.ConnectionInfo.Id">
            <summary>
            Gets or sets a unique identifier to represent this connection.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.CookieBuilder">
            <summary>
            Defines settings used to create a cookie.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.CookieBuilder.Name">
            <summary>
            The name of the cookie.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.CookieBuilder.Path">
            <summary>
            The cookie path.
            </summary>
            <remarks>
            Determines the value that will set on <seealso cref="P:Microsoft.AspNetCore.Http.CookieOptions.Path"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.CookieBuilder.Domain">
            <summary>
            The domain to associate the cookie with.
            </summary>
            <remarks>
            Determines the value that will set on <seealso cref="P:Microsoft.AspNetCore.Http.CookieOptions.Domain"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.CookieBuilder.HttpOnly">
            <summary>
            Indicates whether a cookie is accessible by client-side script.
            </summary>
            <remarks>
            Determines the value that will set on <seealso cref="P:Microsoft.AspNetCore.Http.CookieOptions.HttpOnly"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.CookieBuilder.SameSite">
            <summary>
            The SameSite attribute of the cookie. The default value is <see cref="F:Microsoft.AspNetCore.Http.SameSiteMode.Lax"/>
            </summary>
            <remarks>
            Determines the value that will set on <seealso cref="P:Microsoft.AspNetCore.Http.CookieOptions.SameSite"/>.
            </remarks>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.CookieBuilder.SecurePolicy">
            <summary>
            The policy that will be used to determine <seealso cref="P:Microsoft.AspNetCore.Http.CookieOptions.Secure"/>.
            This is determined from the <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> passed to <see cref="M:Microsoft.AspNetCore.Http.CookieBuilder.Build(Microsoft.AspNetCore.Http.HttpContext,System.DateTimeOffset)"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.CookieBuilder.Expiration">
            <summary>
            Gets or sets the lifespan of a cookie.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.CookieBuilder.Build(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Creates the cookie options from the given <paramref name="context"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>.</param>
            <returns>The cookie options.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.CookieBuilder.Build(Microsoft.AspNetCore.Http.HttpContext,System.DateTimeOffset)">
            <summary>
            Creates the cookie options from the given <paramref name="context"/> with an expiration based on <paramref name="expiresFrom"/> and <see cref="P:Microsoft.AspNetCore.Http.CookieBuilder.Expiration"/>.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/>.</param>
            <param name="expiresFrom">The time to use as the base for computing <seealso cref="P:Microsoft.AspNetCore.Http.CookieOptions.Expires" />.</param>
            <returns>The cookie options.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.CookieSecurePolicy">
            <summary>
            Determines how cookie security properties are set.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.CookieSecurePolicy.SameAsRequest">
            <summary>
            If the URI that provides the cookie is HTTPS, then the cookie will only be returned to the server on 
            subsequent HTTPS requests. Otherwise if the URI that provides the cookie is HTTP, then the cookie will 
            be returned to the server on all HTTP and HTTPS requests. This is the default value because it ensures
            HTTPS for all authenticated requests on deployed servers, and also supports HTTP for localhost development 
            and for servers that do not have HTTPS support.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.CookieSecurePolicy.Always">
            <summary>
            Secure is always marked true. Use this value when your login page and all subsequent pages
            requiring the authenticated identity are HTTPS. Local development will also need to be done with HTTPS urls.
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.CookieSecurePolicy.None">
            <summary>
            Secure is not marked true. Use this value when your login page is HTTPS, but other pages
            on the site which are HTTP also require authentication information. This setting is not recommended because
            the authentication information provided with an HTTP request may be observed and used by other computers
            on your local network or wireless connection.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionaryExtensions.Append(Microsoft.AspNetCore.Http.IHeaderDictionary,System.String,Microsoft.Extensions.Primitives.StringValues)">
            <summary>
            Add new values. Each item remains a separate array entry.
            </summary>
            <param name="headers">The <see cref="T:Microsoft.AspNetCore.Http.IHeaderDictionary"/> to use.</param>
            <param name="key">The header name.</param>
            <param name="value">The header value.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionaryExtensions.AppendCommaSeparatedValues(Microsoft.AspNetCore.Http.IHeaderDictionary,System.String,System.String[])">
            <summary>
            Quotes any values containing comas, and then coma joins all of the values with any existing values.
            </summary>
            <param name="headers">The <see cref="T:Microsoft.AspNetCore.Http.IHeaderDictionary"/> to use.</param>
            <param name="key">The header name.</param>
            <param name="values">The header values.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionaryExtensions.GetCommaSeparatedValues(Microsoft.AspNetCore.Http.IHeaderDictionary,System.String)">
            <summary>
            Get the associated values from the collection separated into individual values.
            Quoted values will not be split, and the quotes will be removed.
            </summary>
            <param name="headers">The <see cref="T:Microsoft.AspNetCore.Http.IHeaderDictionary"/> to use.</param>
            <param name="key">The header name.</param>
            <returns>the associated values from the collection separated into individual values, or StringValues.Empty if the key is not present.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HeaderDictionaryExtensions.SetCommaSeparatedValues(Microsoft.AspNetCore.Http.IHeaderDictionary,System.String,System.String[])">
            <summary>
            Quotes any values containing comas, and then coma joins all of the values.
            </summary>
            <param name="headers">The <see cref="T:Microsoft.AspNetCore.Http.IHeaderDictionary"/> to use.</param>
            <param name="key">The header name.</param>
            <param name="values">The header values.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.HttpResponseWritingExtensions">
            <summary>
            Convenience methods for writing to the response.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HttpResponseWritingExtensions.WriteAsync(Microsoft.AspNetCore.Http.HttpResponse,System.String,System.Threading.CancellationToken)">
            <summary>
            Writes the given text to the response body. UTF-8 encoding will be used.
            </summary>
            <param name="response">The <see cref="T:Microsoft.AspNetCore.Http.HttpResponse"/>.</param>
            <param name="text">The text to write to the response.</param>
            <param name="cancellationToken">Notifies when request operations should be cancelled.</param>
            <returns>A task that represents the completion of the write operation.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HttpResponseWritingExtensions.WriteAsync(Microsoft.AspNetCore.Http.HttpResponse,System.String,System.Text.Encoding,System.Threading.CancellationToken)">
            <summary>
            Writes the given text to the response body using the given encoding.
            </summary>
            <param name="response">The <see cref="T:Microsoft.AspNetCore.Http.HttpResponse"/>.</param>
            <param name="text">The text to write to the response.</param>
            <param name="encoding">The encoding to use.</param>
            <param name="cancellationToken">Notifies when request operations should be cancelled.</param>
            <returns>A task that represents the completion of the write operation.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.FragmentString">
            <summary>
            Provides correct handling for FragmentString value when needed to generate a URI string
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.FragmentString.Empty">
            <summary>
            Represents the empty fragment string. This field is read-only.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.FragmentString.#ctor(System.String)">
            <summary>
            Initialize the fragment string with a given value. This value must be in escaped and delimited format with
            a leading '#' character.
            </summary>
            <param name="value">The fragment string to be assigned to the Value property.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.FragmentString.Value">
            <summary>
            The escaped fragment string with the leading '#' character
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.FragmentString.HasValue">
            <summary>
            True if the fragment string is not empty
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.FragmentString.ToString">
            <summary>
            Provides the fragment string escaped in a way which is correct for combining into the URI representation.
            A leading '#' character will be included unless the Value is null or empty. Characters which are potentially
            dangerous are escaped.
            </summary>
            <returns>The fragment string value</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.FragmentString.ToUriComponent">
            <summary>
            Provides the fragment string escaped in a way which is correct for combining into the URI representation.
            A leading '#' character will be included unless the Value is null or empty. Characters which are potentially
            dangerous are escaped.
            </summary>
            <returns>The fragment string value</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.FragmentString.FromUriComponent(System.String)">
            <summary>
            Returns an FragmentString given the fragment as it is escaped in the URI format. The string MUST NOT contain any
            value that is not a fragment.
            </summary>
            <param name="uriComponent">The escaped fragment as it appears in the URI format.</param>
            <returns>The resulting FragmentString</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.FragmentString.FromUriComponent(System.Uri)">
            <summary>
            Returns an FragmentString given the fragment as from a Uri object. Relative Uri objects are not supported.
            </summary>
            <param name="uri">The Uri object</param>
            <returns>The resulting FragmentString</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.HostString">
            <summary>
            Represents the host portion of a URI can be used to construct URI's properly formatted and encoded for use in
            HTTP headers.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HostString.#ctor(System.String)">
            <summary>
            Creates a new HostString without modification. The value should be Unicode rather than punycode, and may have a port.
            IPv4 and IPv6 addresses are also allowed, and also may have ports.
            </summary>
            <param name="value"></param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HostString.#ctor(System.String,System.Int32)">
            <summary>
            Creates a new HostString from its host and port parts.
            </summary>
            <param name="host">The value should be Unicode rather than punycode. IPv6 addresses must use square braces.</param>
            <param name="port">A positive, greater than 0 value representing the port in the host string.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HostString.Value">
            <summary>
            Returns the original value from the constructor.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HostString.Host">
            <summary>
            Returns the value of the host part of the value. The port is removed if it was present.
            IPv6 addresses will have brackets added if they are missing.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HostString.Port">
            <summary>
            Returns the value of the port part of the host, or <value>null</value> if none is found.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HostString.ToString">
            <summary>
            Returns the value as normalized by ToUriComponent().
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HostString.ToUriComponent">
            <summary>
            Returns the value properly formatted and encoded for use in a URI in a HTTP header.
            Any Unicode is converted to punycode. IPv6 addresses will have brackets added if they are missing.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HostString.FromUriComponent(System.String)">
            <summary>
            Creates a new HostString from the given URI component.
            Any punycode will be converted to Unicode.
            </summary>
            <param name="uriComponent"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HostString.FromUriComponent(System.Uri)">
            <summary>
            Creates a new HostString from the host and port of the give Uri instance.
            Punycode will be converted to Unicode.
            </summary>
            <param name="uri"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HostString.Equals(Microsoft.AspNetCore.Http.HostString)">
            <summary>
            Compares the equality of the Value property, ignoring case.
            </summary>
            <param name="other"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HostString.Equals(System.Object)">
            <summary>
            Compares against the given object only if it is a HostString.
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HostString.GetHashCode">
            <summary>
            Gets a hash code for the value.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HostString.op_Equality(Microsoft.AspNetCore.Http.HostString,Microsoft.AspNetCore.Http.HostString)">
            <summary>
            Compares the two instances for equality.
            </summary>
            <param name="left"></param>
            <param name="right"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HostString.op_Inequality(Microsoft.AspNetCore.Http.HostString,Microsoft.AspNetCore.Http.HostString)">
            <summary>
            Compares the two instances for inequality.
            </summary>
            <param name="left"></param>
            <param name="right"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HostString.GetParts(System.String@,System.String@)">
            <summary>
            Parses the current value. IPv6 addresses will have brackets added if they are missing.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.HttpContext">
            <summary>
            Encapsulates all HTTP-specific information about an individual HTTP request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpContext.Features">
            <summary>
            Gets the collection of HTTP features provided by the server and middleware available on this request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpContext.Request">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Http.HttpRequest"/> object for this request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpContext.Response">
            <summary>
            Gets the <see cref="T:Microsoft.AspNetCore.Http.HttpResponse"/> object for this request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpContext.Connection">
            <summary>
            Gets information about the underlying connection for this request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpContext.WebSockets">
            <summary>
            Gets an object that manages the establishment of WebSocket connections for this request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpContext.Authentication">
            <summary>
            This is obsolete and will be removed in a future version. 
            The recommended alternative is to use Microsoft.AspNetCore.Authentication.AuthenticationHttpContextExtensions.
            See https://go.microsoft.com/fwlink/?linkid=845470.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpContext.User">
            <summary>
            Gets or sets the user for this request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpContext.Items">
            <summary>
            Gets or sets a key/value collection that can be used to share data within the scope of this request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpContext.RequestServices">
            <summary>
            Gets or sets the <see cref="T:System.IServiceProvider"/> that provides access to the request's service container.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpContext.RequestAborted">
            <summary>
            Notifies when the connection underlying this request is aborted and thus request operations should be
            cancelled.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpContext.TraceIdentifier">
            <summary>
            Gets or sets a unique identifier to represent this request in trace logs.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpContext.Session">
            <summary>
            Gets or sets the object used to manage user session data for this request.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HttpContext.Abort">
            <summary>
            Aborts the connection underlying this request.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.HttpRequest">
            <summary>
            Represents the incoming side of an individual HTTP request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.HttpContext">
            <summary>
            Gets the <see cref="P:Microsoft.AspNetCore.Http.HttpRequest.HttpContext"/> for this request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.Method">
            <summary>
            Gets or set the HTTP method.
            </summary>
            <returns>The HTTP method.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.Scheme">
            <summary>
            Gets or set the HTTP request scheme.
            </summary>
            <returns>The HTTP request scheme.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.IsHttps">
            <summary>
            Returns true if the RequestScheme is https.
            </summary>
            <returns>true if this request is using https; otherwise, false.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.Host">
            <summary>
            Gets or set the Host header. May include the port.
            </summary>
            <return>The Host header.</return>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.PathBase">
            <summary>
            Gets or set the RequestPathBase.
            </summary>
            <returns>The RequestPathBase.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.Path">
            <summary>
            Gets or set the request path from RequestPath.
            </summary>
            <returns>The request path from RequestPath.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.QueryString">
            <summary>
            Gets or set the raw query string used to create the query collection in Request.Query.
            </summary>
            <returns>The raw query string.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.Query">
            <summary>
            Gets the query value collection parsed from Request.QueryString.
            </summary>
            <returns>The query value collection parsed from Request.QueryString.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.Protocol">
            <summary>
            Gets or set the RequestProtocol.
            </summary>
            <returns>The RequestProtocol.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.Headers">
            <summary>
            Gets the request headers.
            </summary>
            <returns>The request headers.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.Cookies">
            <summary>
            Gets the collection of Cookies for this request.
            </summary>
            <returns>The collection of Cookies for this request.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.ContentLength">
            <summary>
            Gets or sets the Content-Length header
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.ContentType">
            <summary>
            Gets or sets the Content-Type header.
            </summary>
            <returns>The Content-Type header.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.Body">
            <summary>
            Gets or set the RequestBody Stream.
            </summary>
            <returns>The RequestBody Stream.</returns>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.HasFormContentType">
            <summary>
            Checks the content-type header for form types.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpRequest.Form">
            <summary>
            Gets or sets the request body as a form.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HttpRequest.ReadFormAsync(System.Threading.CancellationToken)">
            <summary>
            Reads the request body if it is a form.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.HttpResponse">
            <summary>
            Represents the outgoing side of an individual HTTP request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpResponse.HttpContext">
            <summary>
            Gets the <see cref="P:Microsoft.AspNetCore.Http.HttpResponse.HttpContext"/> for this response.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpResponse.StatusCode">
            <summary>
            Gets or sets the HTTP response code.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpResponse.Headers">
            <summary>
            Gets the response headers.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpResponse.Body">
            <summary>
            Gets or sets the response body <see cref="T:System.IO.Stream"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpResponse.ContentLength">
            <summary>
            Gets or sets the value for the <c>Content-Length</c> response header.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpResponse.ContentType">
            <summary>
            Gets or sets the value for the <c>Content-Type</c> response header.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpResponse.Cookies">
            <summary>
            Gets an object that can be used to manage cookies for this response.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.HttpResponse.HasStarted">
            <summary>
            Gets a value indicating whether response headers have been sent to the client.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HttpResponse.OnStarting(System.Func{System.Object,System.Threading.Tasks.Task},System.Object)">
            <summary>
            Adds a delegate to be invoked just before response headers will be sent to the client.
            </summary>
            <param name="callback">The delegate to execute.</param>
            <param name="state">A state object to capture and pass back to the delegate.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HttpResponse.OnStarting(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Adds a delegate to be invoked just before response headers will be sent to the client.
            </summary>
            <param name="callback">The delegate to execute.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HttpResponse.OnCompleted(System.Func{System.Object,System.Threading.Tasks.Task},System.Object)">
            <summary>
            Adds a delegate to be invoked after the response has finished being sent to the client.
            </summary>
            <param name="callback">The delegate to invoke.</param>
            <param name="state">A state object to capture and pass back to the delegate.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HttpResponse.RegisterForDispose(System.IDisposable)">
            <summary>
            Registers an object for disposal by the host once the request has finished processing.
            </summary>
            <param name="disposable">The object to be disposed.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HttpResponse.OnCompleted(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Adds a delegate to be invoked after the response has finished being sent to the client.
            </summary>
            <param name="callback">The delegate to invoke.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HttpResponse.Redirect(System.String)">
            <summary>
            Returns a temporary redirect response (HTTP 302) to the client.
            </summary>
            <param name="location">The URL to redirect the client to.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.HttpResponse.Redirect(System.String,System.Boolean)">
            <summary>
            Returns a redirect response (HTTP 301 or HTTP 302) to the client.
            </summary>
            <param name="location">The URL to redirect the client to.</param>
            <param name="permanent"><c>True</c> if the redirect is permanent (301), otherwise <c>false</c> (302).</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.IMiddleware">
            <summary>
            Defines middleware that can be added to the application's request pipeline.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.IMiddleware.InvokeAsync(Microsoft.AspNetCore.Http.HttpContext,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Request handling method.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the current request.</param>
            <param name="next">The delegate representing the remaining middleware in the request pipeline.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the execution of this middleware.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.IMiddlewareFactory">
            <summary>
            Provides methods to create middlware.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.IMiddlewareFactory.Create(System.Type)">
            <summary>
            Creates a middleware instance for each request.
            </summary>
            <param name="middlewareType">The concrete <see cref="T:System.Type"/> of the <see cref="T:Microsoft.AspNetCore.Http.IMiddleware"/>.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Http.IMiddleware"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.IMiddlewareFactory.Release(Microsoft.AspNetCore.Http.IMiddleware)">
            <summary>
            Releases a <see cref="T:Microsoft.AspNetCore.Http.IMiddleware"/> instance at the end of each request.
            </summary>
            <param name="middleware">The <see cref="T:Microsoft.AspNetCore.Http.IMiddleware"/> instance to release.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.PathString">
            <summary>
            Provides correct escaping for Path and PathBase values when needed to reconstruct a request or redirect URI string
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.PathString.Empty">
            <summary>
            Represents the empty path. This field is read-only.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.#ctor(System.String)">
            <summary>
            Initalize the path string with a given value. This value must be in unescaped format. Use
            PathString.FromUriComponent(value) if you have a path value which is in an escaped format.
            </summary>
            <param name="value">The unescaped path to be assigned to the Value property.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.PathString.Value">
            <summary>
            The unescaped path value
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.PathString.HasValue">
            <summary>
            True if the path is not empty
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.ToString">
            <summary>
            Provides the path string escaped in a way which is correct for combining into the URI representation.
            </summary>
            <returns>The escaped path value</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.ToUriComponent">
            <summary>
            Provides the path string escaped in a way which is correct for combining into the URI representation.
            </summary>
            <returns>The escaped path value</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.FromUriComponent(System.String)">
            <summary>
            Returns an PathString given the path as it is escaped in the URI format. The string MUST NOT contain any
            value that is not a path.
            </summary>
            <param name="uriComponent">The escaped path as it appears in the URI format.</param>
            <returns>The resulting PathString</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.FromUriComponent(System.Uri)">
            <summary>
            Returns an PathString given the path as from a Uri object. Relative Uri objects are not supported.
            </summary>
            <param name="uri">The Uri object</param>
            <returns>The resulting PathString</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.StartsWithSegments(Microsoft.AspNetCore.Http.PathString)">
            <summary>
            Determines whether the beginning of this <see cref="T:Microsoft.AspNetCore.Http.PathString"/> instance matches the specified <see cref="T:Microsoft.AspNetCore.Http.PathString"/>.
            </summary>
            <param name="other">The <see cref="T:Microsoft.AspNetCore.Http.PathString"/> to compare.</param>
            <returns>true if value matches the beginning of this string; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.StartsWithSegments(Microsoft.AspNetCore.Http.PathString,System.StringComparison)">
            <summary>
            Determines whether the beginning of this <see cref="T:Microsoft.AspNetCore.Http.PathString"/> instance matches the specified <see cref="T:Microsoft.AspNetCore.Http.PathString"/> when compared
            using the specified comparison option.
            </summary>
            <param name="other">The <see cref="T:Microsoft.AspNetCore.Http.PathString"/> to compare.</param>
            <param name="comparisonType">One of the enumeration values that determines how this <see cref="T:Microsoft.AspNetCore.Http.PathString"/> and value are compared.</param>
            <returns>true if value matches the beginning of this string; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.StartsWithSegments(Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.PathString@)">
            <summary>
            Determines whether the beginning of this <see cref="T:Microsoft.AspNetCore.Http.PathString"/> instance matches the specified <see cref="T:Microsoft.AspNetCore.Http.PathString"/> and returns
            the remaining segments.
            </summary>
            <param name="other">The <see cref="T:Microsoft.AspNetCore.Http.PathString"/> to compare.</param>
            <param name="remaining">The remaining segments after the match.</param>
            <returns>true if value matches the beginning of this string; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.StartsWithSegments(Microsoft.AspNetCore.Http.PathString,System.StringComparison,Microsoft.AspNetCore.Http.PathString@)">
            <summary>
            Determines whether the beginning of this <see cref="T:Microsoft.AspNetCore.Http.PathString"/> instance matches the specified <see cref="T:Microsoft.AspNetCore.Http.PathString"/> when compared
            using the specified comparison option and returns the remaining segments.
            </summary>
            <param name="other">The <see cref="T:Microsoft.AspNetCore.Http.PathString"/> to compare.</param>
            <param name="comparisonType">One of the enumeration values that determines how this <see cref="T:Microsoft.AspNetCore.Http.PathString"/> and value are compared.</param>
            <param name="remaining">The remaining segments after the match.</param>
            <returns>true if value matches the beginning of this string; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.StartsWithSegments(Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.PathString@,Microsoft.AspNetCore.Http.PathString@)">
            <summary>
            Determines whether the beginning of this <see cref="T:Microsoft.AspNetCore.Http.PathString"/> instance matches the specified <see cref="T:Microsoft.AspNetCore.Http.PathString"/> and returns
            the matched and remaining segments.
            </summary>
            <param name="other">The <see cref="T:Microsoft.AspNetCore.Http.PathString"/> to compare.</param>
            <param name="matched">The matched segments with the original casing in the source value.</param>
            <param name="remaining">The remaining segments after the match.</param>
            <returns>true if value matches the beginning of this string; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.StartsWithSegments(Microsoft.AspNetCore.Http.PathString,System.StringComparison,Microsoft.AspNetCore.Http.PathString@,Microsoft.AspNetCore.Http.PathString@)">
            <summary>
            Determines whether the beginning of this <see cref="T:Microsoft.AspNetCore.Http.PathString"/> instance matches the specified <see cref="T:Microsoft.AspNetCore.Http.PathString"/> when compared
            using the specified comparison option and returns the matched and remaining segments.
            </summary>
            <param name="other">The <see cref="T:Microsoft.AspNetCore.Http.PathString"/> to compare.</param>
            <param name="comparisonType">One of the enumeration values that determines how this <see cref="T:Microsoft.AspNetCore.Http.PathString"/> and value are compared.</param>
            <param name="matched">The matched segments with the original casing in the source value.</param>
            <param name="remaining">The remaining segments after the match.</param>
            <returns>true if value matches the beginning of this string; otherwise, false.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.Add(Microsoft.AspNetCore.Http.PathString)">
            <summary>
            Adds two PathString instances into a combined PathString value.
            </summary>
            <returns>The combined PathString value</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.Add(Microsoft.AspNetCore.Http.QueryString)">
            <summary>
            Combines a PathString and QueryString into the joined URI formatted string value.
            </summary>
            <returns>The joined URI formatted string value</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.Equals(Microsoft.AspNetCore.Http.PathString)">
            <summary>
            Compares this PathString value to another value. The default comparison is StringComparison.OrdinalIgnoreCase.
            </summary>
            <param name="other">The second PathString for comparison.</param>
            <returns>True if both PathString values are equal</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.Equals(Microsoft.AspNetCore.Http.PathString,System.StringComparison)">
            <summary>
            Compares this PathString value to another value using a specific StringComparison type
            </summary>
            <param name="other">The second PathString for comparison</param>
            <param name="comparisonType">The StringComparison type to use</param>
            <returns>True if both PathString values are equal</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.Equals(System.Object)">
            <summary>
            Compares this PathString value to another value. The default comparison is StringComparison.OrdinalIgnoreCase.
            </summary>
            <param name="obj">The second PathString for comparison.</param>
            <returns>True if both PathString values are equal</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.GetHashCode">
            <summary>
            Returns the hash code for the PathString value. The hash code is provided by the OrdinalIgnoreCase implementation.
            </summary>
            <returns>The hash code</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.op_Equality(Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.PathString)">
            <summary>
            Operator call through to Equals
            </summary>
            <param name="left">The left parameter</param>
            <param name="right">The right parameter</param>
            <returns>True if both PathString values are equal</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.op_Inequality(Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.PathString)">
            <summary>
            Operator call through to Equals
            </summary>
            <param name="left">The left parameter</param>
            <param name="right">The right parameter</param>
            <returns>True if both PathString values are not equal</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.op_Addition(System.String,Microsoft.AspNetCore.Http.PathString)">
            <summary>
            </summary>
            <param name="left">The left parameter</param>
            <param name="right">The right parameter</param>
            <returns>The ToString combination of both values</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.op_Addition(Microsoft.AspNetCore.Http.PathString,System.String)">
            <summary>
            </summary>
            <param name="left">The left parameter</param>
            <param name="right">The right parameter</param>
            <returns>The ToString combination of both values</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.op_Addition(Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.PathString)">
            <summary>
            Operator call through to Add
            </summary>
            <param name="left">The left parameter</param>
            <param name="right">The right parameter</param>
            <returns>The PathString combination of both values</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.op_Addition(Microsoft.AspNetCore.Http.PathString,Microsoft.AspNetCore.Http.QueryString)">
            <summary>
            Operator call through to Add
            </summary>
            <param name="left">The left parameter</param>
            <param name="right">The right parameter</param>
            <returns>The PathString combination of both values</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.op_Implicit(System.String)~Microsoft.AspNetCore.Http.PathString">
            <summary>
            Implicitly creates a new PathString from the given string.
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.PathString.op_Implicit(Microsoft.AspNetCore.Http.PathString)~System.String">
            <summary>
            Implicitly calls ToString().
            </summary>
            <param name="path"></param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Abstractions.Resources.Exception_UseMiddlewareIServiceProviderNotAvailable">
            <summary>
            '{0}' is not available.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Abstractions.Resources.FormatException_UseMiddlewareIServiceProviderNotAvailable(System.Object)">
            <summary>
            '{0}' is not available.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Abstractions.Resources.Exception_UseMiddlewareNoInvokeMethod">
            <summary>
            No public '{0}' or '{1}' method found.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Abstractions.Resources.FormatException_UseMiddlewareNoInvokeMethod(System.Object,System.Object)">
            <summary>
            No public '{0}' or '{1}' method found.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Abstractions.Resources.Exception_UseMiddlewareNonTaskReturnType">
            <summary>
            '{0}' or '{1}' does not return an object of type '{2}'.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Abstractions.Resources.FormatException_UseMiddlewareNonTaskReturnType(System.Object,System.Object,System.Object)">
            <summary>
            '{0}' or '{1}' does not return an object of type '{2}'.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Abstractions.Resources.Exception_UseMiddlewareNoParameters">
            <summary>
            The '{0}' or '{1}' method's first argument must be of type '{2}'.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Abstractions.Resources.FormatException_UseMiddlewareNoParameters(System.Object,System.Object,System.Object)">
            <summary>
            The '{0}' or '{1}' method's first argument must be of type '{2}'.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Abstractions.Resources.Exception_UseMiddleMutlipleInvokes">
            <summary>
            Multiple public '{0}' or '{1}' methods are available.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Abstractions.Resources.FormatException_UseMiddleMutlipleInvokes(System.Object,System.Object)">
            <summary>
            Multiple public '{0}' or '{1}' methods are available.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Abstractions.Resources.Exception_PathMustStartWithSlash">
            <summary>
            The path in '{0}' must start with '/'.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Abstractions.Resources.FormatException_PathMustStartWithSlash(System.Object)">
            <summary>
            The path in '{0}' must start with '/'.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Abstractions.Resources.Exception_InvokeMiddlewareNoService">
            <summary>
            Unable to resolve service for type '{0}' while attempting to Invoke middleware '{1}'.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Abstractions.Resources.FormatException_InvokeMiddlewareNoService(System.Object,System.Object)">
            <summary>
            Unable to resolve service for type '{0}' while attempting to Invoke middleware '{1}'.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Abstractions.Resources.Exception_InvokeDoesNotSupportRefOrOutParams">
            <summary>
            The '{0}' method must not have ref or out parameters.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Abstractions.Resources.FormatException_InvokeDoesNotSupportRefOrOutParams(System.Object)">
            <summary>
            The '{0}' method must not have ref or out parameters.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Abstractions.Resources.Exception_PortMustBeGreaterThanZero">
            <summary>
            The value must be greater than zero.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Abstractions.Resources.FormatException_PortMustBeGreaterThanZero">
            <summary>
            The value must be greater than zero.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Abstractions.Resources.Exception_UseMiddlewareNoMiddlewareFactory">
            <summary>
            No service for type '{0}' has been registered.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Abstractions.Resources.FormatException_UseMiddlewareNoMiddlewareFactory(System.Object)">
            <summary>
            No service for type '{0}' has been registered.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Abstractions.Resources.Exception_UseMiddlewareUnableToCreateMiddleware">
            <summary>
            '{0}' failed to create middleware of type '{1}'.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Abstractions.Resources.FormatException_UseMiddlewareUnableToCreateMiddleware(System.Object,System.Object)">
            <summary>
            '{0}' failed to create middleware of type '{1}'.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Abstractions.Resources.Exception_UseMiddlewareExplicitArgumentsNotSupported">
            <summary>
            Types that implement '{0}' do not support explicit arguments.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Abstractions.Resources.FormatException_UseMiddlewareExplicitArgumentsNotSupported(System.Object)">
            <summary>
            Types that implement '{0}' do not support explicit arguments.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.Abstractions.Resources.ArgumentCannotBeNullOrEmpty">
            <summary>
            Argument cannot be null or empty.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.Abstractions.Resources.FormatArgumentCannotBeNullOrEmpty">
            <summary>
            Argument cannot be null or empty.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.QueryString">
            <summary>
            Provides correct handling for QueryString value when needed to reconstruct a request or redirect URI string
            </summary>
        </member>
        <member name="F:Microsoft.AspNetCore.Http.QueryString.Empty">
            <summary>
            Represents the empty query string. This field is read-only.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.QueryString.#ctor(System.String)">
            <summary>
            Initialize the query string with a given value. This value must be in escaped and delimited format with
            a leading '?' character. 
            </summary>
            <param name="value">The query string to be assigned to the Value property.</param>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.QueryString.Value">
            <summary>
            The escaped query string with the leading '?' character
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.QueryString.HasValue">
            <summary>
            True if the query string is not empty
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.QueryString.ToString">
            <summary>
            Provides the query string escaped in a way which is correct for combining into the URI representation. 
            A leading '?' character will be included unless the Value is null or empty. Characters which are potentially
            dangerous are escaped.
            </summary>
            <returns>The query string value</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.QueryString.ToUriComponent">
            <summary>
            Provides the query string escaped in a way which is correct for combining into the URI representation. 
            A leading '?' character will be included unless the Value is null or empty. Characters which are potentially
            dangerous are escaped.
            </summary>
            <returns>The query string value</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.QueryString.FromUriComponent(System.String)">
            <summary>
            Returns an QueryString given the query as it is escaped in the URI format. The string MUST NOT contain any
            value that is not a query.
            </summary>
            <param name="uriComponent">The escaped query as it appears in the URI format.</param>
            <returns>The resulting QueryString</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.QueryString.FromUriComponent(System.Uri)">
            <summary>
            Returns an QueryString given the query as from a Uri object. Relative Uri objects are not supported.
            </summary>
            <param name="uri">The Uri object</param>
            <returns>The resulting QueryString</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.QueryString.Create(System.String,System.String)">
            <summary>
            Create a query string with a single given parameter name and value.
            </summary>
            <param name="name">The un-encoded parameter name</param>
            <param name="value">The un-encoded parameter value</param>
            <returns>The resulting QueryString</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.QueryString.Create(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Creates a query string composed from the given name value pairs.
            </summary>
            <param name="parameters"></param>
            <returns>The resulting QueryString</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.QueryString.Create(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,Microsoft.Extensions.Primitives.StringValues}})">
            <summary>
            Creates a query string composed from the given name value pairs.
            </summary>
            <param name="parameters"></param>
            <returns>The resulting QueryString</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.RequestDelegate">
            <summary>
            A function that can process an HTTP request.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the request.</param>
            <returns>A task that represents the completion of request processing.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Http.WebSocketManager">
            <summary>
            Manages the establishment of WebSocket connections for a specific HTTP request. 
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.WebSocketManager.IsWebSocketRequest">
            <summary>
            Gets a value indicating whether the request is a WebSocket establishment request.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Http.WebSocketManager.WebSocketRequestedProtocols">
            <summary>
            Gets the list of requested WebSocket sub-protocols.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.WebSocketManager.AcceptWebSocketAsync">
            <summary>
            Transitions the request to a WebSocket connection.
            </summary>
            <returns>A task representing the completion of the transition.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Http.WebSocketManager.AcceptWebSocketAsync(System.String)">
            <summary>
            Transitions the request to a WebSocket connection using the specified sub-protocol.
            </summary>
            <param name="subProtocol">The sub-protocol to use.</param>
            <returns>A task representing the completion of the transition.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.MapExtensions">
            <summary>
            Extension methods for the <see cref="T:Microsoft.AspNetCore.Builder.Extensions.MapMiddleware"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.MapExtensions.Map(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Http.PathString,System.Action{Microsoft.AspNetCore.Builder.IApplicationBuilder})">
            <summary>
            Branches the request pipeline based on matches of the given request path. If the request path starts with
            the given path, the branch is executed.
            </summary>
            <param name="app">The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> instance.</param>
            <param name="pathMatch">The request path to match.</param>
            <param name="configuration">The branch to take for positive path matches.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> instance.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.Extensions.MapMiddleware">
            <summary>
            Respresents a middleware that maps a request path to a sub-request pipeline.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.Extensions.MapMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.AspNetCore.Builder.Extensions.MapOptions)">
            <summary>
            Creates a new instace of <see cref="T:Microsoft.AspNetCore.Builder.Extensions.MapMiddleware"/>.
            </summary>
            <param name="next">The delegate representing the next middleware in the request pipeline.</param>
            <param name="options">The middleware options.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.Extensions.MapMiddleware.Invoke(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Executes the middleware.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the current request.</param>
            <returns>A task that represents the execution of this middleware.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.Extensions.MapOptions">
            <summary>
            Options for the <see cref="T:Microsoft.AspNetCore.Builder.Extensions.MapMiddleware"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Builder.Extensions.MapOptions.PathMatch">
            <summary>
            The path to match.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Builder.Extensions.MapOptions.Branch">
            <summary>
            The branch taken for a positive match.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.Extensions.MapWhenMiddleware">
            <summary>
            Respresents a middleware that runs a sub-request pipeline when a given predicate is matched.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.Extensions.MapWhenMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.AspNetCore.Builder.Extensions.MapWhenOptions)">
            <summary>
            Creates a new instance of <see cref="T:Microsoft.AspNetCore.Builder.Extensions.MapWhenMiddleware"/>.
            </summary>
            <param name="next">The delegate representing the next middleware in the request pipeline.</param>
            <param name="options">The middleware options.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.Extensions.MapWhenMiddleware.Invoke(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Executes the middleware.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the current request.</param>
            <returns>A task that represents the execution of this middleware.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.Extensions.MapWhenOptions">
            <summary>
            Options for the <see cref="T:Microsoft.AspNetCore.Builder.Extensions.MapWhenMiddleware"/>.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Builder.Extensions.MapWhenOptions.Predicate">
            <summary>
            The user callback that determines if the branch should be taken.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Builder.Extensions.MapWhenOptions.Branch">
            <summary>
            The branch taken for a positive match.
            </summary>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.Extensions.UsePathBaseMiddleware">
            <summary>
            Represents a middleware that extracts the specified path base from request path and postpend it to the request path base.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.Extensions.UsePathBaseMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.AspNetCore.Http.PathString)">
            <summary>
            Creates a new instace of <see cref="T:Microsoft.AspNetCore.Builder.Extensions.UsePathBaseMiddleware"/>.
            </summary>
            <param name="next">The delegate representing the next middleware in the request pipeline.</param>
            <param name="pathBase">The path base to extract.</param>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.Extensions.UsePathBaseMiddleware.Invoke(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Executes the middleware.
            </summary>
            <param name="context">The <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the current request.</param>
            <returns>A task that represents the execution of this middleware.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.MapWhenExtensions">
            <summary>
            Extension methods for the <see cref="T:Microsoft.AspNetCore.Builder.Extensions.MapWhenMiddleware"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.MapWhenExtensions.MapWhen(Microsoft.AspNetCore.Builder.IApplicationBuilder,System.Func{Microsoft.AspNetCore.Http.HttpContext,System.Boolean},System.Action{Microsoft.AspNetCore.Builder.IApplicationBuilder})">
            <summary>
            Branches the request pipeline based on the result of the given predicate.
            </summary>
            <param name="app"></param>
            <param name="predicate">Invoked with the request environment to determine if the branch should be taken</param>
            <param name="configuration">Configures a branch to take</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.RunExtensions">
            <summary>
            Extension methods for adding terminal middleware.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.RunExtensions.Run(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            Adds a terminal middleware delegate to the application's request pipeline.
            </summary>
            <param name="app">The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> instance.</param>
            <param name="handler">A delegate that handles the request.</param>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.UseExtensions">
            <summary>
            Extension methods for adding middleware.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.UseExtensions.Use(Microsoft.AspNetCore.Builder.IApplicationBuilder,System.Func{Microsoft.AspNetCore.Http.HttpContext,System.Func{System.Threading.Tasks.Task},System.Threading.Tasks.Task})">
            <summary>
            Adds a middleware delegate defined in-line to the application's request pipeline.
            </summary>
            <param name="app">The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> instance.</param>
            <param name="middleware">A function that handles the request or calls the given next function.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> instance.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.UseMiddlewareExtensions">
            <summary>
            Extension methods for adding typed middleware.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.UseMiddleware``1(Microsoft.AspNetCore.Builder.IApplicationBuilder,System.Object[])">
            <summary>
            Adds a middleware type to the application's request pipeline.
            </summary>
            <typeparam name="TMiddleware">The middleware type.</typeparam>
            <param name="app">The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> instance.</param>
            <param name="args">The arguments to pass to the middleware type instance's constructor.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> instance.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.UseMiddleware(Microsoft.AspNetCore.Builder.IApplicationBuilder,System.Type,System.Object[])">
            <summary>
            Adds a middleware type to the application's request pipeline.
            </summary>
            <param name="app">The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> instance.</param>
            <param name="middleware">The middleware type.</param>
            <param name="args">The arguments to pass to the middleware type instance's constructor.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> instance.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.UsePathBaseExtensions">
            <summary>
            Extension methods for <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.UsePathBaseExtensions.UsePathBase(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Http.PathString)">
            <summary>
            Adds a middleware that extracts the specified path base from request path and postpend it to the request path base.
            </summary>
            <param name="app">The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> instance.</param>
            <param name="pathBase">The path base to extract.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> instance.</returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.UseWhenExtensions">
            <summary>
            Extension methods for <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.UseWhenExtensions.UseWhen(Microsoft.AspNetCore.Builder.IApplicationBuilder,System.Func{Microsoft.AspNetCore.Http.HttpContext,System.Boolean},System.Action{Microsoft.AspNetCore.Builder.IApplicationBuilder})">
            <summary>
            Conditionally creates a branch in the request pipeline that is rejoined to the main pipeline.
            </summary>
            <param name="app"></param>
            <param name="predicate">Invoked with the request environment to determine if the branch should be taken</param>
            <param name="configuration">Configures a branch to take</param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.AspNetCore.Builder.IApplicationBuilder">
            <summary>
            Defines a class that provides the mechanisms to configure an application's request pipeline.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Builder.IApplicationBuilder.ApplicationServices">
            <summary>
            Gets or sets the <see cref="T:System.IServiceProvider"/> that provides access to the application's service container.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Builder.IApplicationBuilder.ServerFeatures">
            <summary>
            Gets the set of HTTP features the application's server provides.
            </summary>
        </member>
        <member name="P:Microsoft.AspNetCore.Builder.IApplicationBuilder.Properties">
            <summary>
            Gets a key/value collection that can be used to share data between middleware.
            </summary>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.IApplicationBuilder.Use(System.Func{Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.AspNetCore.Http.RequestDelegate})">
            <summary>
            Adds a middleware delegate to the application's request pipeline.
            </summary>
            <param name="middleware">The middleware delgate.</param>
            <returns>The <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.IApplicationBuilder.New">
            <summary>
            Creates a new <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/> that shares the <see cref="P:Microsoft.AspNetCore.Builder.IApplicationBuilder.Properties"/> of this
            <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.
            </summary>
            <returns>The new <see cref="T:Microsoft.AspNetCore.Builder.IApplicationBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.AspNetCore.Builder.IApplicationBuilder.Build">
            <summary>
            Builds the delegate used by this application to process HTTP requests.
            </summary>
            <returns>The request handling delegate.</returns>
        </member>
    </members>
</doc>
