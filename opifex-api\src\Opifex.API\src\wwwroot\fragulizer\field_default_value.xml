<?xml version="1.0" encoding="utf-8" ?>
<settings>
    <!--1. Enable/disable the heading on the User Interface-->
    <EnableHeading>false</EnableHeading>

    <!--2. Enable/disable "Table of instruments" section-->
    <EnableInstrumentsTable>false</EnableInstrumentsTable>
    <ShowCurrencyColumn>false</ShowCurrencyColumn>

    <!--3. Enable/disable "Activity trend" section-->
    <EnableActivityTrend>false</EnableActivityTrend>

    <!--4. Change default period for Fragmentation and Market Share sections-->
    <!--(1M - 3M - 6M - 1Y - LIVE)-->
    <DefaultPeriod>3M</DefaultPeriod>


    <!--6. Custom Marketname-->
    <CustomMarketName>
    </CustomMarketName>

    <!-- 7.	Default culture and language-->
    <DefaultCulture>en-GB</DefaultCulture>

    <!--7. Format-->
    <Format>
        <en-GB>
            <ShortDate xml:space="preserve">dd/MM/yyyy</ShortDate>
            <LongDate xml:space="preserve">MMM d, yyyy</LongDate>
            <ShortTime xml:space="preserve">HH:mm</ShortTime>
            <LongTime xml:space="preserve">HH:mm.ss</LongTime>
            <ThousandsSeparator xml:space="preserve">,</ThousandsSeparator>
            <NegativeNumberFormat xml:space="preserve">-n</NegativeNumberFormat>
        </en-GB>
    </Format>

    <!--8. TimeZoneID-->
    <TimeZone>W. Europe Standard Time</TimeZone>

    <!--9. Share price number decimal digits-->
    <SharePriceNumberDecimalDigits>2</SharePriceNumberDecimalDigits>
    <!--10. Shareprice percen tage decimal digits-->
    <SharePricePercentageDecimalDigits>2</SharePricePercentageDecimalDigits>

    <!--11. Share price value increase color-->
    <SharePriceValueIncreaseColor>#89A54E</SharePriceValueIncreaseColor>

    <!--12. Share price value decrease color-->
    <SharePriceValueDecreaseColor>#AA4643</SharePriceValueDecreaseColor>

    <!--13. Footer-->
    <ShowDataProviderInfo>false</ShowDataProviderInfo>
    <ShowDataDelayInfo>true</ShowDataDelayInfo>
    <ShowSupplierInfo>true</ShowSupplierInfo>
    <ShowSupplierInfoLink>true</ShowSupplierInfoLink>
    <ShowDisclaimerInfo>True</ShowDisclaimerInfo>

    <!--14. Chart-->
    <NumberOfYearOnColumnChart>3</NumberOfYearOnColumnChart>

    <ChartColors>#659B8D;#c6b391;#FF6666;#FFC231;#7a6a49;#7b7a68;#47341f;#00945f</ChartColors>

    <EnableExcelDownload>true</EnableExcelDownload>

    <!--15. Use latin number for arabic language-->
    <UseLatinNumbers>false</UseLatinNumbers>

    <!--16. Styling of the tool-->
    <MainTextFont></MainTextFont>
    <MainTextFontSize></MainTextFontSize>
    <MainTextColor></MainTextColor>

    <MainHeadingFont></MainHeadingFont>
    <MainHeadingFontSize></MainHeadingFontSize>
    <MainHeadingColor></MainHeadingColor>

    <SecondHeadingFont></SecondHeadingFont>
    <SecondHeadingFontSize></SecondHeadingFontSize>
    <SecondHeadingColor></SecondHeadingColor>

    <TableHeadFont></TableHeadFont>
    <TableHeadFontSize></TableHeadFontSize>
    <TableHeadColor></TableHeadColor>
    <TableHeadBackgroundColor></TableHeadBackgroundColor>

    <TableInlineBorderColor>#d2d2d2</TableInlineBorderColor>
    <TableInlineBorderEnable>false</TableInlineBorderEnable>

    <TableOutlineBorderColor>#d2d2d2</TableOutlineBorderColor>
    <TableOutlineBorderEnable>false</TableOutlineBorderEnable>

    <TableOddRowColor>#fff</TableOddRowColor>
    <TableEvenRowColor>#fff</TableEvenRowColor>

    <LinkColor>#0088c3</LinkColor>
    <LinkHoverColor>#339fcf</LinkHoverColor>
    <LinkUnderline></LinkUnderline>
</settings>