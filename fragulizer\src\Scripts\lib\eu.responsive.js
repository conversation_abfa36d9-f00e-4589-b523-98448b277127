﻿/*! matchMedia() polyfill - Test a CSS media type/query in JS. Authors & copyright (c) 2012: <PERSON>, <PERSON>, <PERSON>. Dual MIT/BSD license */
/*! NOTE: If you're already including a window.matchMedia polyfill via Modernizr or otherwise, you don't need this part */
window.matchMedia = window.matchMedia || (function (doc, undefined) {

    var bool,
      docElem = doc.documentElement,
      refNode = docElem.firstElementChild || docElem.firstChild,
    // fakeBody required for <FF4 when executed in <head>
      fakeBody = doc.createElement('body'),
      div = doc.createElement('div');

    div.id = 'mq-test-1';
    div.style.cssText = "position:absolute;top:-100em";
    fakeBody.style.background = "none";
    fakeBody.appendChild(div);

    return function (q) {

        div.innerHTML = '&shy;<style media="' + q + '"> #mq-test-1 { width: 42px; }</style>';

        docElem.insertBefore(fakeBody, refNode);
        bool = div.offsetWidth == 42;
        docElem.removeChild(fakeBody);

        return { matches: bool, media: q };
    };

})(document);




/*! Respond.js v1.1.0: min/max-width media query polyfill. (c) Scott Jehl. MIT/GPLv2 Lic. j.mp/respondjs  */
(function (win) {
    //exposed namespace
    win.respond = {};

    //define update even in native-mq-supporting browsers, to avoid errors
    respond.update = function () { };

    //expose media query support flag for external use
    respond.mediaQueriesSupported = win.matchMedia && win.matchMedia("only all").matches;

    //if media queries are supported, exit here
    if (respond.mediaQueriesSupported) { return; }

    //define vars
    var doc = win.document,
		docElem = doc.documentElement,
		mediastyles = [],
		rules = [],
		appendedEls = [],
		parsedSheets = {},
		resizeThrottle = 30,
		head = doc.getElementsByTagName("head")[0] || docElem,
		base = doc.getElementsByTagName("base")[0],
		links = head.getElementsByTagName("link"),
		requestQueue = [],

    //loop stylesheets, send text content to translate
		ripCSS = function () {
		    var sheets = links,
				sl = sheets.length,
				i = 0,
		    //vars for loop:
				sheet, href, media, isCSS;

		    for (; i < sl; i++) {
		        sheet = sheets[i],
				href = sheet.href,
				media = sheet.media,
				isCSS = sheet.rel && sheet.rel.toLowerCase() === "stylesheet";

		        //only links plz and prevent re-parsing
		        if (!!href && isCSS && !parsedSheets[href]) {
		            // selectivizr exposes css through the rawCssText expando
		            if (sheet.styleSheet && sheet.styleSheet.rawCssText) {
		                translate(sheet.styleSheet.rawCssText, href, media);
		                parsedSheets[href] = true;
		            } else {
		                if ((!/^([a-zA-Z:]*\/\/)/.test(href) && !base)
							|| href.replace(RegExp.$1, "").split("/")[0] === win.location.host) {
		                    requestQueue.push({
		                        href: href,
		                        media: media
		                    });
		                }
		            }
		        }
		    }
		    makeRequests();
		},

    //recurse through request queue, get css text
		makeRequests = function () {
		    if (requestQueue.length) {
		        var thisRequest = requestQueue.shift();

		        ajax(thisRequest.href, function (styles) {
		            translate(styles, thisRequest.href, thisRequest.media);
		            parsedSheets[thisRequest.href] = true;
		            makeRequests();
		        });
		    }
		},

    //find media blocks in css text, convert to style blocks
		translate = function (styles, href, media) {
		    var qs = styles.match(/@media[^\{]+\{([^\{\}]*\{[^\}\{]*\})+/gi),
				ql = qs && qs.length || 0,
		    //try to get CSS path
				href = href.substring(0, href.lastIndexOf("/")),
				repUrls = function (css) {
				    return css.replace(/(url\()['"]?([^\/\)'"][^:\)'"]+)['"]?(\))/g, "$1" + href + "$2$3");
				},
				useMedia = !ql && media,
		    //vars used in loop
				i = 0,
				j, fullq, thisq, eachq, eql;

		    //if path exists, tack on trailing slash
		    if (href.length) { href += "/"; }

		    //if no internal queries exist, but media attr does, use that	
		    //note: this currently lacks support for situations where a media attr is specified on a link AND
		    //its associated stylesheet has internal CSS media queries.
		    //In those cases, the media attribute will currently be ignored.
		    if (useMedia) {
		        ql = 1;
		    }


		    for (; i < ql; i++) {
		        j = 0;

		        //media attr
		        if (useMedia) {
		            fullq = media;
		            rules.push(repUrls(styles));
		        }
		        //parse for styles
		        else {
		            fullq = qs[i].match(/@media *([^\{]+)\{([\S\s]+?)$/) && RegExp.$1;
		            rules.push(RegExp.$2 && repUrls(RegExp.$2));
		        }

		        eachq = fullq.split(",");
		        eql = eachq.length;

		        for (; j < eql; j++) {
		            thisq = eachq[j];
		            mediastyles.push({
		                media: thisq.split("(")[0].match(/(only\s+)?([a-zA-Z]+)\s?/) && RegExp.$2 || "all",
		                rules: rules.length - 1,
		                hasquery: thisq.indexOf("(") > -1,
		                minw: thisq.match(/\(min\-width:[\s]*([\s]*[0-9\.]+)(px|em)[\s]*\)/) && parseFloat(RegExp.$1) + (RegExp.$2 || ""),
		                maxw: thisq.match(/\(max\-width:[\s]*([\s]*[0-9\.]+)(px|em)[\s]*\)/) && parseFloat(RegExp.$1) + (RegExp.$2 || "")
		            });
		        }
		    }

		    applyMedia();
		},

		lastCall,

		resizeDefer,

    // returns the value of 1em in pixels
		getEmValue = function () {
		    var ret,
				div = doc.createElement('div'),
				body = doc.body,
				fakeUsed = false;

		    div.style.cssText = "position:absolute;font-size:1em;width:1em";

		    if (!body) {
		        body = fakeUsed = doc.createElement("body");
		        body.style.background = "none";
		    }

		    body.appendChild(div);

		    docElem.insertBefore(body, docElem.firstChild);

		    ret = div.offsetWidth;

		    if (fakeUsed) {
		        docElem.removeChild(body);
		    }
		    else {
		        body.removeChild(div);
		    }

		    //also update eminpx before returning
		    ret = eminpx = parseFloat(ret);

		    return ret;
		},

    //cached container for 1em value, populated the first time it's needed 
		eminpx,

    //enable/disable styles
		applyMedia = function (fromResize) {
		    var name = "clientWidth",
				docElemProp = docElem[name],
				currWidth = doc.compatMode === "CSS1Compat" && docElemProp || doc.body[name] || docElemProp,
				styleBlocks = {},
				lastLink = links[links.length - 1],
				now = (new Date()).getTime();

		    //throttle resize calls	
		    if (fromResize && lastCall && now - lastCall < resizeThrottle) {
		        clearTimeout(resizeDefer);
		        resizeDefer = setTimeout(applyMedia, resizeThrottle);
		        return;
		    }
		    else {
		        lastCall = now;
		    }

		    for (var i in mediastyles) {
		        var thisstyle = mediastyles[i],
					min = thisstyle.minw,
					max = thisstyle.maxw,
					minnull = min === null,
					maxnull = max === null,
					em = "em";

		        if (!!min) {
		            min = parseFloat(min) * (min.indexOf(em) > -1 ? (eminpx || getEmValue()) : 1);
		        }
		        if (!!max) {
		            max = parseFloat(max) * (max.indexOf(em) > -1 ? (eminpx || getEmValue()) : 1);
		        }

		        // if there's no media query at all (the () part), or min or max is not null, and if either is present, they're true
		        if (!thisstyle.hasquery || (!minnull || !maxnull) && (minnull || currWidth >= min) && (maxnull || currWidth <= max)) {
		            if (!styleBlocks[thisstyle.media]) {
		                styleBlocks[thisstyle.media] = [];
		            }
		            styleBlocks[thisstyle.media].push(rules[thisstyle.rules]);
		        }
		    }

		    //remove any existing respond style element(s)
		    for (var i in appendedEls) {
		        if (appendedEls[i] && appendedEls[i].parentNode === head) {
		            head.removeChild(appendedEls[i]);
		        }
		    }

		    //inject active styles, grouped by media type
		    for (var i in styleBlocks) {
		        var ss = doc.createElement("style"),
					css = styleBlocks[i].join("\n");

		        ss.type = "text/css";
		        ss.media = i;

		        //originally, ss was appended to a documentFragment and sheets were appended in bulk.
		        //this caused crashes in IE in a number of circumstances, such as when the HTML element had a bg image set, so appending beforehand seems best. Thanks to @dvelyk for the initial research on this one!
		        head.insertBefore(ss, lastLink.nextSibling);

		        if (ss.styleSheet) {
		            ss.styleSheet.cssText = css;
		        }
		        else {
		            ss.appendChild(doc.createTextNode(css));
		        }

		        //push to appendedEls to track for later removal
		        appendedEls.push(ss);
		    }
		},
    //tweaked Ajax functions from Quirksmode
		ajax = function (url, callback) {
		    var req = xmlHttp();
		    if (!req) {
		        return;
		    }
		    req.open("GET", url, true);
		    req.onreadystatechange = function () {
		        if (req.readyState != 4 || req.status != 200 && req.status != 304) {
		            return;
		        }
		        callback(req.responseText);
		    }
		    if (req.readyState == 4) {
		        return;
		    }
		    req.send(null);
		},
    //define ajax obj 
		xmlHttp = (function () {
		    var xmlhttpmethod = false;
		    try {
		        xmlhttpmethod = new XMLHttpRequest();
		    }
		    catch (e) {
		        xmlhttpmethod = new ActiveXObject("Microsoft.XMLHTTP");
		    }
		    return function () {
		        return xmlhttpmethod;
		    };
		})();

    //translate CSS
    ripCSS();

    //expose update for re-running respond later on
    respond.update = ripCSS;

    //adjust on resize
    function callMedia() {
        applyMedia(true);
    }
    if (win.addEventListener) {
        win.addEventListener("resize", callMedia, false);
    }
    else if (win.attachEvent) {
        win.attachEvent("onresize", callMedia);
    }
})(this);

// ----------------------------------------------------------------------
// <copyright file="euroland-iframefix.js" company="Euroland.com">
//     Copyright (c) Euroland.com. All rights reserved.
// </copyright>
// <author><EMAIL></author>
// <created>Tuesday, July 02, 2013 10:27</created>
// <lastedit>Tuesday, July 02, 2013 10:27</lastedit>
// <changes>
// </changes>
// -----------------------------------------------------------------------

/// <reference path="jquery-1.6.2.js" />
/// <reference path="../JsExtentions.js" />
/// <reference path="highstock.js" />

/*
* Dependencies: JsExtentions.js, highstock.js
*/

/**
* IFrame Fix for webpage on Safari on touch device when flip between portrait and lanscape
*/
(function ($) {
    function eulog(msg) {
        if (!$('#log').length) {
            $('<div/>').attr('id', 'log').css({
                position: 'fixed',
                top: 0,
                left: 0,
                opacity: 0.6,
                'max-height': '200px',
                'overflow-y': 'auto',
                'background-color': '#fff'
            }).appendTo(document.body);
        }

        $('#log').append('<div>' + msg + '</div>');
    }

    function viewport() {
        var e = window, a = 'inner';
        if (!('innerWidth' in window)) {
            a = 'client';
            e = document.documentElement || document.body;
        }
        return { width: e[a + 'Width'], height: e[a + 'Height'] };
    }

    function logViewport() {
        var vp = viewport();
        eulog('Viewport w:' + vp.width + ' h:' + vp.height);
    }

    $.log = function (msg) {
        eulog(msg);
    }

    $.viewportSize = function () {
        return viewport();
    }

    $.touchDevices.IosInsideIframe = (window.self !== window.top) && (/Ip(ad|hone|od)/i.test(navigator.userAgent));

    if ($.touchDevices.IosInsideIframe) {
        function getDocumentWidth() {
            return Math.min($(document).width(), $(document.body).width(), viewport().width);
        }

        function onresizeX(e) {
            //logViewport();
            /*$('.wrapper:first').css({
            'overflow-x': 'hidden',
            width: getDocumentWidth()
            });*/
            //$('.wrapper').hide();
        }

        //$(window).bind('resize', onresizeX);

        //$(function () {
        $('html').css('-webkit-text-size-adjust', 'none');
		
        $(document.body).css({
            padding: 0,
            margin: 0,
            width: '99.5%',
            overflow: 'hidden',
            '-webkit-text-size-adjust': 'none'
        });

        //onresizeX();
    }
})(jQuery);

/*! Highstock Fixes: Used to fix the bugs of HighStock chart library for Euroland's tools only */
(function () {
    if (typeof Highcharts ==='undefined') {
        return;
    }

    var doc = document,
	    win = window,
        HC = Highcharts,
	    Chart = HC.Chart,
	    addEvent = HC.addEvent,
        removeEvent = HC.removeEvent,
	    createElement = HC.createElement,
	    discardElement = HC.discardElement,
	    Renderer = HC.Renderer,
	    css = HC.css,
	    merge = HC.merge,
	    each = HC.each,
	    extend = HC.extend,
	    math = Math,
	    mathMax = math.max,
	    mathMin = math.min,
	    pick = HC.pick,
	    hasTouch = 'ontouchstart' in doc.documentElement,
	    timeFactor = 1, // 1 = JavaScript time, 1000 = Unix time
	    M = 'M',
	    L = 'L',
	    DIV = 'div',
	    UNDEFINED,
	    HIDDEN = 'hidden',
	    RELATIVE = 'relative',
	    NONE = 'none',
	    PREFIX = 'highcharts-',
	    ABSOLUTE = 'absolute',
	    NORMAL_STATE = '',
	    HOVER_STATE = 'hover',
	    SELECT_STATE = 'select',
	    PX = 'px',
	    userAgent = navigator.userAgent,
	    isIE = /msie/i.test(userAgent) && !win.opera,
	    defaultOptions = HC.getOptions(),
	    useUTC = defaultOptions.global.useUTC;

    function defined(obj) {
        return obj !== UNDEFINED && obj !== null;
    };
    function isString(s) {
        return typeof s === 'string';
    }

    
    extend(HC.Chart.prototype, {
        _cloneChartOption: function(){
            if (!doc.createElementNS) {
                doc.createElementNS = function (ns, tagName) {
                    var elem = doc.createElement(tagName);
                    elem.getBBox = function () {
                        return chart.renderer.Element.prototype.getBBox.apply({ element: elem });
                    };
                    return elem;
                };
            }
            var chart = this,
                seriesOptions,
				config,
				pointOptions,
				pointMarker,
                options;

            // Clone current chart data before destroying
            options = $.extend(true, {}, chart.options, {});

            // prepare for replicating the chart
            options.series = [];
            each(chart.series, function (serie) {
                seriesOptions = serie.options;

                seriesOptions.animation = false; // turn off animation
                seriesOptions.showCheckbox = false;

                // remove image markers
                if (seriesOptions && seriesOptions.marker && /^url\(/.test(seriesOptions.marker.symbol)) {
                    seriesOptions.marker.symbol = 'circle';
                }

                seriesOptions.data = [];

                each(serie.data, function (point) {

                    // extend the options by those values that can be expressed in a number or array config
                    config = point.config;
                    pointOptions = {
                        x: point.x,
                        y: point.y,
                        color: point.color,
                        name: point.name
                    };

                    if (typeof config == 'object' && point.config && config.constructor != Array) {
                        extend(pointOptions, config);
                    }

                    seriesOptions.data.push(pointOptions); // copy fresh updated data

                    // remove image markers
                    pointMarker = point.config && point.config.marker;
                    if (pointMarker && /^url\(/.test(pointMarker.symbol)) {
                        delete pointMarker.symbol;
                    }
                });

                options.series.push(seriesOptions);
            });

            return options;
        },
        cloneChart: function(opts) {
            var chart = this,
                cloneChart,
                cloneOption = chart._cloneChartOption();
            $.extend(true, cloneOption, opts);

            // generate the chart copy
            cloneChart = new Highcharts.Chart(cloneOption);

            return cloneChart;
        }
    });

    // Disable animation for chart on touch devices to improve rendering performance
    if(hasTouch){
        HC.setOptions({
            chart: {
                animation: false
                // Disable 'resize' event which fired automatically 
                // when device flip between portrait and lanscape orientation.
                // This requires developer must to redraw manually chart when
                // resize event fired
                ,reflow: true
            },
            tooltip: {
                animation: false
            }
        });
    }
    //if ($.touchDevices.IosInsideIframe) {
	if (false) {
        var originalInit = HC.Chart.prototype.init,
            originalInitReflow = HC.Chart.prototype.initReflow,
			reflowTimeoutF;

        extend(HC.Chart.prototype, {
            
            // Override initReflow to make own-call
            initReflow: function () {
                var chart = this,
					optionsChart = chart.options.chart,
					renderTo = isString(optionsChart.renderTo) ? $('#' + optionsChart.renderTo) : $(optionsChart.renderTo),
					reflowTimeout;
                function reflow(e) {
                    var width = optionsChart.width || renderTo.width(),
                        height = optionsChart.height || renderTo.height(),
                        target = e ? e.target : win; // #805 - MooTools doesn't supply e
                    if (!chart.hasUserSize && width && height && (target === win || target === doc)) {
                        $.log('--->' + renderTo.width() + ':' + renderTo.height());
						if (width !== chart.containerWidth || height !== chart.containerHeight) {
                            if (chart.container)
                                chart.container.style.display = 'none';

                            clearTimeout(reflowTimeout);
                            chart.reflowTimeout = reflowTimeout = setTimeout(function () {
                                if (chart.container) { // It may have been destroyed in the meantime (#1257)
                                    chart.cleanRedraw();
                                    chart.hasUserSize = null;
                                }
                            }, 100);
                        }
                        chart.containerWidth = width;
                        chart.containerHeight = height;
						$(win).one('resize', reflow);
                    }
                }

                $(win).one('resize', reflow);
                addEvent(chart, 'destroy', function () {
                    removeEvent(win, 'resize', reflow);
                });
            },

            // There a bug of Highstock on Iphone is when device flip
            // between portrait and lanscape, the chart (in an iframe)
            // fires 'redraw' event many times (because 'resize' event is
            // fired many times) to reduce sequently by per pixel the size of chart's container.
            // This fix is simple, copy entire the chart's options, data, and then
            // destroy current chart, then re-creat chart on the same container. 
            cleanRedraw: function () {
                var chart = this,
                    options;

                options = chart._cloneChartOption();

                chart.destroy();
                chart.init(options, chart.callback);
            }
        });
    }
	
	if(Chart.prototype.getSVG === undefined) {
		extend(Chart.prototype, {

			/**
			 * Return an SVG representation of the chart
			 *
			 * @param additionalOptions {Object} Additional chart options for the generated SVG representation
			 */
			getSVG: function (additionalOptions) {
				var chart = this,
					chartCopy,
					sandbox,
					svg,
					seriesOptions,
					sourceWidth,
					sourceHeight,
					cssWidth,
					cssHeight,
					options = merge(chart.options, additionalOptions); // copy the options and add extra options
				options.exporting = options.exporting || {};
				// IE compatibility hack for generating SVG content that it doesn't really understand
				if (!doc.createElementNS) {
					/*jslint unparam: true*//* allow unused parameter ns in function below */
					doc.createElementNS = function (ns, tagName) {
						return doc.createElement(tagName);
					};
					/*jslint unparam: false*/
				}

				// create a sandbox where a new chart will be generated
				sandbox = createElement(DIV, null, {
					position: ABSOLUTE,
					top: '-9999em',
					width: chart.chartWidth + PX,
					height: chart.chartHeight + PX
				}, doc.body);

				// get the source size
				cssWidth = chart.renderTo.style.width;
				cssHeight = chart.renderTo.style.height;
				sourceWidth = options.exporting.sourceWidth ||
					options.chart.width ||
					(/px$/.test(cssWidth) && parseInt(cssWidth, 10)) ||
					600;
				sourceHeight = options.exporting.sourceHeight ||
					options.chart.height ||
					(/px$/.test(cssHeight) && parseInt(cssHeight, 10)) ||
					400;

				// override some options
				extend(options.chart, {
					animation: false,
					renderTo: sandbox,
					forExport: true,
					width: sourceWidth,
					height: sourceHeight
				});
				options.exporting.enabled = false; // hide buttons in print

				// prepare for replicating the chart
				options.series = [];
				each(chart.series, function (serie) {
					seriesOptions = merge(serie.options, {
						animation: false, // turn off animation
						showCheckbox: false,
						visible: serie.visible
					});

					if (!seriesOptions.isInternal) { // used for the navigator series that has its own option set
						options.series.push(seriesOptions);
					}
				});

				// generate the chart copy
				chartCopy = new Highcharts.Chart(options, chart.callback);

				// reflect axis extremes in the export
				each(['xAxis', 'yAxis'], function (axisType) {
					each(chart[axisType], function (axis, i) {
						var axisCopy = chartCopy[axisType][i],
							extremes = axis.getExtremes(),
							userMin = extremes.userMin,
							userMax = extremes.userMax;

						if (axisCopy && (userMin !== UNDEFINED || userMax !== UNDEFINED)) {
							axisCopy.setExtremes(userMin, userMax, true, false);
						}
					});
				});

				// get the SVG from the container's innerHTML
				svg = chartCopy.container.innerHTML;

				// free up memory
				options = null;
				chartCopy.destroy();
				discardElement(sandbox);

				// sanitize
				svg = svg
					.replace(/zIndex="[^"]+"/g, '')
					.replace(/isShadow="[^"]+"/g, '')
					.replace(/symbolName="[^"]+"/g, '')
					.replace(/jQuery[0-9]+="[^"]+"/g, '')
					.replace(/url\([^#]+#/g, 'url(#')
					.replace(/<svg /, '<svg xmlns:xlink="http://www.w3.org/1999/xlink" ')
					.replace(/ href=/g, ' xlink:href=')
					.replace(/\n/, ' ')
					.replace(/<\/svg>.*?$/, '</svg>') // any HTML added to the container after the SVG (#894)
					/* This fails in IE < 8
					.replace(/([0-9]+)\.([0-9]+)/g, function(s1, s2, s3) { // round off to save weight
						return s2 +'.'+ s3[0];
					})*/

					// Replace HTML entities, issue #347
					.replace(/&nbsp;/g, '\u00A0') // no-break space
					.replace(/&shy;/g,  '\u00AD') // soft hyphen

					// IE specific
					.replace(/<IMG /g, '<image ')
					.replace(/height=([^" ]+)/g, 'height="$1"')
					.replace(/width=([^" ]+)/g, 'width="$1"')
					.replace(/hc-svg-href="([^"]+)">/g, 'xlink:href="$1"/>')
					.replace(/id=([^" >]+)/g, 'id="$1"')
					.replace(/class=([^" >]+)/g, 'class="$1"')
					.replace(/ transform /g, ' ')
					.replace(/:(path|rect)/g, '$1')
					.replace(/style="([^"]+)"/g, function (s) {
						return s.toLowerCase();
					});

				// IE9 beta bugs with innerHTML. Test again with final IE9.
				svg = svg.replace(/(url\(#highcharts-[0-9]+)&quot;/g, '$1')
					.replace(/&quot;/g, "'");

				return svg;
			}
			
		});
	}
	
	if($.browser.msie && $.browser.version < 9) {
		var GlobalCharts = window.GlobalCharts = {};
		
		HC.Chart.prototype.callbacks.push(function(chart) {
			if(!GlobalCharts[this.container.id]) {
				GlobalCharts[this.container.id] = chart;
			}
		});
	}
})();

// ----------------------------------------------------------------------
// <copyright file="eu.downloadpdf.js" company="Euroland.com">
//     Copyright (c) Euroland.com. All rights reserved.
// </copyright>
// <author><EMAIL></author>
// <created>Friday, July 26, 2013 15:35</created>
// <lastedit>Friday, July 26, 2013 15:35</lastedit>
// <changes>
// </changes>
// -----------------------------------------------------------------------

/*!
* PdfDownload library v2.1
* @2014 www.euroland.com
* Developed by: <EMAIL>
*/
(function () {
    'PdfDownload library v2.1 is used for euroland.com only';
    //
    // Common functions
    // 
    var specialChars = { '\b': '\\b', '\t': '\\t', '\n': '\\n', '\f': '\\f', '\r': '\\r', '"': '\\"', '\\': '\\\\' }

    function replaceChars(chr) {
        return specialChars[chr] || '\\u00' + Math.floor(chr.charCodeAt() / 16).toString(16) + (chr.charCodeAt() % 16).toString(16);
    }

    function getType(obj) {
        try {
            if (obj == undefined) return false;
            if (obj.nodeName) {
                switch (obj.nodeType) {
                    case 1: return 'element';
                    case 3: return (/\S/).test(obj.nodeValue) ? 'textnode' : 'whitespace';
                }
            } else if (typeof obj.length == 'number') {
                if (obj.callee) return 'arguments';
                else if (obj.item) return 'collection';
                else if (typeof (obj) != 'string') return 'array';
            }
            if (typeof (obj)) return (typeof (obj) == 'number' && !isFinite(obj)) ? false : typeof (obj);
            return typeof obj;
        } catch (Error) { }
    }

    function JSONEncode(obj) {
        try {
            switch (getType(obj)) {
                case 'string':
                    return '"' + obj.replace(/[\x00-\x1f\\"]/g, replaceChars) + '"';
                case 'array':
                    var parse = '[';
                    for (var i = 0; i < obj.length; i++) parse += String(JSONEncode(obj[i])) + ',';
                    parse = (parse.length > 1) ? parse.substring(0, parse.length - 1) : parse;
                    return (parse + ']');
                case 'object':
                    var string = [];
                    for (var key in obj) {
                        var json = JSONEncode(obj[key]);
                        if (json) string.push(JSONEncode(key) + ':' + json);
                    }
                    return '{' + string + '}';
                case 'number':
                case 'boolean':
                    return String(obj);
                case false:
                    return 'null';
            }
            return null;
        } catch (Error) { return Error; }
    }

    function getFont() {
        var fontFamily = '', fontSize = '';
        try {
            var fontFamily = $(document.body).css('font-family');
            if (fontFamily && fontFamily.length > 0) {
                cssStyle += 'font-family:' + fontFamily + ';';
            }
        } catch (e) { }

        try {
            var fontSize = $(document.body).css('font-size');
            if (fontSize && fontSize.length > 0) {
                cssStyle += 'font-size:' + fontSize + ';';
            }
        } catch (e) {

        }

        return [fontFamily, fontSize];
    }

    function generateHtmlPage(strHtml, f) {
        //var header = "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/ xhtml1-transitional.dtd\">";
        //header += "<html xmlns=\"http://www.w3.org/1999/xhtml\"><head>";
        var header = '<!DOCTYPE html>';
        var lang = '';
        lang = $('html').attr('lang') || '';

        header += '<html lang="' + lang + '"><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8">';
        $('meta').each(function () {
            header += $(this).clone().wrap('<div/>').parent().html();
        });
        for (i = 0; i < document.styleSheets.length; i++) {
            header += "<link href=\"" + getFullUrl(document.styleSheets[i].href);

            header += "\" rel=\"stylesheet\" type=\"text/css\" media =\"screen\"/>";
        }
		
		var inlineStyles = $('style', $('head'));
        $.each(inlineStyles, function(){
          header += '<style type="text/css" media="screen">';
          header += $(this).html();
          header += '</style>';
        });
		
        header += "</head>";
        var cssClass = $(document.body).attr('class') || '',
            cssStyle = $(document.body).attr('style') || '';

        try {
            var fontFamily = f.Font || '';
            if (fontFamily && fontFamily.length > 0) {
                if (cssStyle.length > 0) cssStyle += ';';
                cssStyle += 'font-family:' + fontFamily;
            }
        } catch (e) { }

        try {
            var fontSize = f.FontSize || '';
            if (fontSize && fontSize.length > 0) {
                if (cssStyle.length > 0) cssStyle += ';';
                cssStyle += 'font-size:' + fontSize;
            }
        } catch (e) {

        }

        var dir = '';
        dir = $('body').attr('dir') || '';
        strHtml = header + '<body ' + (dir && dir.toLowerCase() === 'rtl' ? ('dir="' + dir + '" ') : ' ') + 'class="' + cssClass + '" style="' + cssStyle + '">' + strHtml + '</body></html>';

        return formatToSendingHTML(strHtml);
    }

    function getFullUrl(url) {
        var newUrl = String(url);
		var subfix = '_' + new Date().getTime();
        if (newUrl && newUrl.indexOf('?') == -1) {
            newUrl += '?' + subfix;
        }
		else {
			newUrl += '&' + subfix;
		}

        return newUrl;
    }

    function formatToSendingHTML(strHtml) {
        /*
        Replaces in the html code some characters, leaving the string in only one line,
        with the < replaced to a left pointing arrow (\u2190) and > replaced to the right
        pointing arrow (\u2192). This values are needed to then convert the string into
        html - readeble code.
        003C -> 2190
        003E -> 2192
        0022 -> \\\"
        \n -> ""
        \r -> ""
        &%13 -> ""
        &%10 -> ""
        */

        var r = /\u0022/g;
        r = /\u003C/g;
        strHtml = strHtml.replace(r, "\u2190");
        r = /\u003E/g;
        strHtml = strHtml.replace(r, "\u2192");
        r = /&#10;|&#13;/g;
        strHtml = strHtml.replace(r, "");

        strHtml = strHtml.replace(/>[\t\s\r\n]+</g, '><'); // Replace >_white-space_|_line-break<
            //.replace(/(>)[\t\s\r\n]+([^><])|([^><])[\t\s\r\n]+(<)/g, '$1$2');
        return strHtml;
    }

    function getHtmlString(strhtml, f)//This should be called from the html pages to generate the object to send.
    {
        return generateHtmlPage(strhtml, f);
    }

    function encodeUrl(url) {
        return encodeURIComponent(url).replace(/'/g, "%27").replace(/"/g, "%22");
    }

    function decodeUrl(url) {
        return decodeURIComponent(url.replace(/\+/g, " "));
    }
    ///////////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////////

    function defaultOptions() {
        return {
            type: 'html', // Values can be: "html" or "url"
            fileType: 'pdf', // pdf or jpeg
            url: window.location.href,
            downloadUrl: 'http://wwww.euroland.com/downloadpdf2/',
            exportingDelay: 0,
            events: {
                error: function (msg) { }
            },
            documentAttributes: {
                FileName: "PDFDownload.pdf",
                MarginTop: 0,
                MarginLeft: 0,
                MarginRight: 0,
                MarginBottom: 0,
                Width: 0,
                FontSize: null,
                Font: null, // If not supply, default font of page is used
                /*
                * Language type for document, default is Unicode
                * Latin = 0,
                * Unicode = 1, 
                * Korean = 2, 
                * Japanese = 3, 
                * ChineseS (Simplified Chinese) = 4, 
                * ChineseT (Traditional Chinese) = 5
                */
                LanguageType: 1 // Latin = 0, Unicode = 1, Korean = 2, Japanese = 3, ChineseS (Simplified Chinese) = 4, ChineseT (Traditional Chinese) = 5
            }
        };
    }

    var PdfDownload = window.PdfDownload = function () {
        PdfDownload.prototype.init.apply(this, arguments);
    };
	
	PdfDownload.createIFrame = function() {
        var iframeStyle = 'border:0;position:absolute;width:0px;height:0px;left:0px;top:0px;';
		//var iframeStyle = 'border:0;width:800px;height:800px;';
        var iframe;

        try {
            iframe = document.createElement('iframe');
            document.body.appendChild(iframe);
            $(iframe).attr({ style: iframeStyle, src: "about:blank" });
            iframe.doc = null;
            iframe.doc = iframe.contentDocument ? iframe.contentDocument : (iframe.contentWindow ? iframe.contentWindow.document : iframe.document);
        }
        catch (e) { throw e + ". iframes may not be supported in this browser."; }

        if (iframe.doc == null) throw "Cannot find document.";

        iframe.destroy = function () {
            document.body.removeChild(iframe);
        };
        return iframe;
    };
	
    PdfDownload.prototype = {
        // @documentAttributes object{fileName, marginTop, marginLeft, marginBottom, marginRight, width, height}
        // @Url string,
        // @Html string,

        init: function (opts) {
            this.options = $.extend(true, {}, defaultOptions(), opts || {});

            this.options.type = this.options.type || 'html';

            this.downloadUrl = this.options.downloadUrl || 'http://wwww.euroland.com/downloadpdf';
            //this.Html = opts.Html || document.body.innerHTML;
            //this.Svg = opts.Svg || [];

            var font = getFont();
            var fontFamily = font[0],
                fontSize = font[1];

            if (fontFamily && fontFamily.length > 0)
                this.options.documentAttributes.Font = fontFamily;

            if (fontSize && fontSize.length > 0)
                this.options.documentAttributes.FontSize = fontSize;
        },
        setContent: function (html) {
            if (this.options.type.toString().toLowerCase() !== 'html') {
                throw 'Current request type is not HTML, fail to invoke setContent()';
                return;
            }
            this.Html = getHtmlString(html, this.options.documentAttributes);
        },
        sendData: function () {
            try {
				
				
				
                var form = document.createElement('form');
                var _time = (new Date()).getTime().toString();
                if (this.downloadUrl.indexOf('?') < 0)
                    form.action = this.downloadUrl + '?_' + _time;
                else
                    form.action = this.downloadUrl + '&_' + _time;

                form.method = 'post';
                form.style.display = 'none';
				//form.setAttribute('target', '_blank');
				form.setAttribute('enctype', 'multipart/form-data')
				
                //(document.documentElement || document.body).appendChild(form);

                var inputHidden = document.createElement('input');
                inputHidden.type = 'hidden';
                inputHidden.name = 'destinationPdfValue';
                inputHidden.value = JSONEncode({
                    DocumentAttributes: JSONEncode(this.options.documentAttributes),
                    Html: this.Html,
                    FullPath: window.location.href,
                    Url: this.options.url,
                    DocumentType: (this.options.type.toString().toLowerCase() === 'url') ? 1 : 0,
                    FileType: (this.options.fileType.toString().toLowerCase() === 'jpeg') ? 1 : 0
                });
                form.appendChild(inputHidden);
				//Create temporary ifame ich contain submit form
				if($.browser.msie) {
					document.body.appendChild(form);
					
					form.submit();
					//document.domain = 'euroland.com';
					document.body.removeChild(form);
				}
				else {
					var tmpIframe = $('<iframe/>').attr({
						src: 'about:blank',
						width: 1,
						height: 1,
						name: 'downloadpdfFrame'
					}).appendTo(document.body);
					
					$(form).appendTo(tmpIframe);
					$(form).submit();
					//document.domain = 'google.com';
					//remove temporary iframe after form submit successfully
					$(tmpIframe).remove();
				}
            }
            catch (Error) {
                if (typeof this.options.events.error === 'function')
                    this.options.events.error(Error);
                else
                    throw Error;
            }
        }
    };

})();

/*!
* PdfDownload extension method
*/
(function($){
    if(undefined === $) return;
	
	// [18-Dec-2014 binh.nguyen]
	// With old IE < 9, Highcharts 3rd library uses Vector Markup Language (VML) to 
	// render chart content. While euroland, however, use AbcPdf9 and EvoPdf library
	// to render PDF do not support VML. Therefore need to pre-render highcharts using
	// highcharts' function named "getSVG()" to replace VML chart before post to server.
	function generateHighChartSVG(){
		if(window.GlobalCharts)
		{
			for(var id in window.GlobalCharts){
				try{
					// Only pre-render SVG with visible charts
					if(window.GlobalCharts[id]) {
						var $chartContainer = $(window.GlobalCharts[id].container);
						if($chartContainer.is(':visible')){
							$chartContainer.siblings('.svgInner').remove();
							var svg = window.GlobalCharts[id].getSVG({
								chart: {
									width: $chartContainer.width(),
									height: $chartContainer.height()
								}
							});
							$('<div/>').addClass('svgInner').hide().html(svg).insertAfter($chartContainer);
						}
					}
					
				}catch(e){
					console && console.log && console.log(e);
				}
			}
		}
	}
	
    /**
    * write current DOM to Iframe to prepare sending HTML to server
    * @param {Function} creatingDOMFunc callback function to manipulate manually (find, delete, modify, etc. the Iframe DOM). E.g. creatingDOMFunc(iframe document)
    * @return {String} html content inside Iframe
    */
    function writeDOM2IFrame(creatingDOMFunc) {
        var iframe, html = '', writeDoc, isOldIE = $.browser.msie && $.browser.version < 9;
		if(isOldIE)
			generateHighChartSVG();
        html = document.body.innerHTML;
		if(isOldIE)
			$('.svgInner').remove();
		
		iframe = PdfDownload.createIFrame();
		writeDoc = iframe.doc;
		
        // Remove <script> tags
        html = html.replace(/<\s*script[^>]*>(?:\r|\n|.)*?(<\s*\/script[^>]*>|$)/ig, '');
        writeDoc.open();
        writeDoc.write('<!DOCTYPE html><html><head></head><body>' + html + '</body></html>');
        writeDoc.close();
		if(isOldIE) {
				$(writeDoc.body).find('.highcharts-container').each(function(){
					var $svg = $(this).siblings('.svgInner:first');
					$(this).empty().html($svg.html());
					$svg.remove();
				});
		}
		
        if(typeof(creatingDOMFunc) === 'function'){
            creatingDOMFunc(writeDoc);
        }
        html = writeDoc.body.innerHTML;
        iframe.destroy();
        //console.log(html);
        return html;
    };

    $.extend(PdfDownload.prototype, {
        selfPdfDownload: function(creatingDOMFunc){
            //writeDOM2IFrame(creatingDOMFunc);
			this.setContent(writeDOM2IFrame(creatingDOMFunc));
            this.sendData();           
        }
    });
})(jQuery);