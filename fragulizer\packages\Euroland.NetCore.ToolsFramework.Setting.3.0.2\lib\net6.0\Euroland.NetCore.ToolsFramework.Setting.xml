<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Euroland.NetCore.ToolsFramework.Setting</name>
    </assembly>
    <members>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.IRequestSettingFinder">
            <summary>
            Represents a provider for determining the setting resource of an <see cref="T:Microsoft.AspNetCore.Http.HttpRequest"/>
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.IRequestSettingFinder.DetermineProviderSettingResourceResult">
            <summary>
            Implements the provider to determine the setting resource of the given request
            </summary>
            <param name="context"><see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> for the request</param>
            <returns></returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting">
            <summary>
            Represents a set of key/value setting
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting.Item(System.String)">
            <summary>
            Gets or sets a setting value
            </summary>
            <param name="name">The setting name</param>
            <returns>The setting value</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting.GetChild(System.String)">
            <summary>
            Gets a child setting with the specified name
            </summary>
            <param name="name">The setting name</param>
            <returns>The <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem"/> </returns>
            <remarks>
                This method will never return <c>null</c>. If no matching child setting is found,
                an empty <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem"/> will be returned.
            </remarks>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting.GetChildren">
            <summary>
            Gets the immediate children of this setting
            </summary>
            <returns></returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem">
            <summary>
            Represents an item of setting values
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem.Name">
            <summary>
            Gets the name of this item occupies in its parent 
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem.Path">
            <summary>
            Gets the full path to this item within the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting"/>
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem.Value">
            <summary>
            Gets or sets the item value
            </summary>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItemRoot">
            <summary>
            Represents the root item of an <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting"/> hierarchy
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItemRoot.Updatable">
            <summary>
            Gets or sets value to indicate that this setting is updatable value
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItemRoot.Providers">
            <summary>
            Gets the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/>s for this setting
            </summary>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManager">
            <summary>
            Represents a type used to create a <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItemRoot"/>
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManager.SettingProviderFactories">
            <summary>
            Gets the factories that used to obtain <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/>s
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManager.RequestSettingFinders">
            <summary>
            Gets the list of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.IRequestSettingFinder"/> that used to find <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/> based on HTTP Request
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManager.Accept(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory)">
            <summary>
            Add a new <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory"/>
            </summary>
            <param name="factory">The <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory"/></param>
            <returns>The <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManager"/> as a chain for reusing factory</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManager.Create">
            <summary>
            Creates the root <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItemRoot"/> of application setting from 
            the set of setting provider factories registered in <see cref="P:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManager.SettingProviderFactories"/>
            </summary>
            <returns>An <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItemRoot"/></returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManagerBuilder">
            <summary>
            Represents a type used to build application setting.
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManagerBuilder.UseStaticSetting(System.String)">
            <summary>
            Uses setting value from a static file.
            </summary>
            <param name="path">Relative path that identifies a file.</param>
            <returns>Chainable of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManagerBuilder"/>.</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManagerBuilder.UseStaticSetting(System.String,System.Boolean)">
            <summary>
            Uses setting value from a static file.
            </summary>
            <param name="path">Relative path that identifies a file.</param>
            <param name="optional">True: Should not thrown exception if not found setting file. Otherwise, False: throw exception.</param>
            <returns>Chainable of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManagerBuilder"/>.</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManagerBuilder.UseHttpRequestSetting(System.String)">
            <summary>
            Creates setting base on the HTTP request. 
            </summary>
            <param name="rootSubpath">Relative path that identifies a directory which contains setting files.</param>
            <returns></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManagerBuilder.UseHttpRequestSetting(System.String,Euroland.NetCore.ToolsFramework.Setting.SettingResourceType)">
            <summary>
            Creates setting base on the HTTP request. 
            </summary>
            <param name="rootSubpath">Relative path that identifies a directory which contains setting files.</param>
            <param name="settingType">The type of setting file (xml or json). Default is both.</param>
            <returns></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManagerBuilder.UseHttpRequestSetting(System.String,System.Boolean)">
            <summary>
            Creates setting base on the HTTP request.
            </summary>
            <param name="rootSubpath">Relative path that identifies a directory which contains setting files.</param>
            <param name="optional">True: Should not thrown exception if not found setting file. Otherwise, False: throw exception.</param>
            <returns>Chainable of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManagerBuilder"/>.</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManagerBuilder.UseHttpRequestSetting(System.String,Euroland.NetCore.ToolsFramework.Setting.SettingResourceType,System.Boolean)">
            <summary>
            Creates setting base on the HTTP request.
            </summary>
            <param name="rootSubpath">Relative path that identifies a directory which contains setting files.</param>
            <param name="settingType">The type of setting file (xml or json). Default is both.</param>
            <param name="optional">True: Should not thrown exception if not found setting file. Otherwise, False: throw exception.</param>
            <returns></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManagerBuilder.UseSettingProvider(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory)">
            <summary>
            Creates setting with the specified provider of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory"/>.
            </summary>
            <param name="settingProviderFactory">The provider factory.</param>
            <returns></returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider">
            <summary>
            Represents for the setting provider
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider.GetData">
            <summary>
            Get all settings of setting provider
            </summary>
            <returns></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider.TryGet(System.String,System.String@)">
            <summary>
            Tries to get setting value for the specified name
            </summary>
            <param name="name">The setting name</param>
            <param name="value">The setting value</param>
            <returns><c>True</c> if a value for the specified name was found, otherwise <c>False</c></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider.Set(System.String,System.String)">
            <summary>
            Sets a setting value for the specified name
            </summary>
            <param name="name">The setting name</param>
            <param name="value">The setting value</param>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider.Load">
            <summary>
            Loads setting values from the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory"/>s represented by this <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/>
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider.IsLoaded">
            <summary>
            Gets value indicating that provider has already loaded
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider.GetChildKeys(System.Collections.Generic.IEnumerable{System.String},System.String)">
            <summary>
            Returns the list of names without path that this provider contains
            </summary>
            <param name="otherChildNames">The child names that other providers contain</param>
            <param name="parentPath">The path for the parent <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting"/></param>
            <returns>The list of keys for this provider.</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider.Save(System.IO.Stream)">
            <summary>
            Writes serialized setting data into a <see cref="T:System.IO.Stream"/>
            </summary>
            <param name="outputStream">The <see cref="T:System.IO.Stream"/> to write</param>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider.SaveAsync(System.IO.Stream)">
            <summary>
            Writes serialized setting data into a <see cref="T:System.IO.Stream"/>
            </summary>
            <param name="outputStream">The <see cref="T:System.IO.Stream"/> to write</param>
            <returns></returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory">
            <summary>
            Represents a type used to create a <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/>
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory.Optional">
            <summary>
            Gets or sets the value to specify the setting in this root directory
            is optional. The one is optional means that, application will not
            throw an exception if not found a particular setting or an error
            occurs at runtime.
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory.Create">
            <summary>
            Creates the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/>
            </summary>
            <returns></returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.IWritableFileInfo">
            <summary>
            Represents a file info that be writable.
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Abstractions.IWritableFileInfo.CreateWriteStream">
            <summary>
            Creates a writable stream.
            </summary>
            <returns>The write stream.</returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.SettingExtensions">
            <summary>
            Extension methods for setting classes
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingExtensions.Exists(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem)">
            <summary>
            Determines whether the setting has a <see cref="P:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem.Value"/> or has children
            </summary>
            <param name="item"></param>
            <returns></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingExtensions.AsEnumerable(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting)">
            <summary>
            Get the enumeration of key value pairs within the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting" />
            </summary>
            <param name="setting">The <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting"/> to enumerate.</param>
            <returns>An enumeration of key value pairs.</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingExtensions.AsEnumerable(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting,System.Boolean)">
            <summary>
            Get the enumeration of key value pairs within the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting" />
            </summary>
            <param name="setting">The <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting"/> to enumerate.</param>
            <param name="makePathsRelative">If true, the child keys returned will have the current setting's path trimmed from the front.</param>
            <returns>An enumeration of key value pairs.</returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.SettingBinder">
            <summary>
            Static helper class that allows binding strongly typed objects to ToolFramework's setting.
            </summary>
            <remarks>
            The code is clone from https://github.com/aspnet/Configuration/blob/dev/src/Config.Binder/ConfigurationBinder.cs
            to adopt binding to 'Euroland.NetCore.ToolsFramework.Setting' project.
            </remarks>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingBinder.Bind``1(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting)">
            <summary>
            Attempts to bind the setting instance to a new instance of type T.
            If this setting section has a value, that will be used.
            Otherwise binding by matching property names against setting keys recursively.
            </summary>
            <typeparam name="T">The type of the new instance to bind.</typeparam>
            <param name="setting">The setting instance to bind.</param>
            <returns>The new instance of <typeparamref name="T"/> if successful, default(T) otherwise.</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingBinder.Bind(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting,System.Type)">
            <summary>
            Attempts to bind the setting instance to a new instance of type T.
            If this setting section has a value, that will be used.
            Otherwise binding by matching property names against setting keys recursively.
            </summary>
            <param name="setting">The setting instance to bind.</param>
            <param name="type">The type of the new instance to bind.</param>
            <returns>The new instance if successful, null otherwise.</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingBinder.Bind(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting,System.String,System.Object)">
            <summary>
            Attempts to bind the given object instance to the setting section specified by the key by matching property names against setting keys recursively.
            </summary>
            <param name="setting">The setting instance to bind.</param>
            <param name="key">The key of the setting section to bind.</param>
            <param name="instance">The object to bind.</param>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingBinder.Bind(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting,System.Object)">
            <summary>
            Attempts to bind the given object instance to setting values by matching property names against setting keys recursively.
            </summary>
            <param name="setting">The setting instance to bind.</param>
            <param name="instance">The object to bind.</param>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingBinder.GetValue``1(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting,System.String)">
            <summary>
            Extracts the value with the specified key and converts it to type T.
            </summary>
            <typeparam name="T">The type to convert the value to.</typeparam>
            <param name="setting">The setting.</param>
            <param name="key">The key of the setting section's value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingBinder.GetValue``1(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting,System.String,``0)">
            <summary>
            Extracts the value with the specified key and converts it to type T.
            </summary>
            <typeparam name="T">The type to convert the value to.</typeparam>
            <param name="setting">The setting.</param>
            <param name="key">The key of the setting section's value to convert.</param>
            <param name="defaultValue">The default value to use if no value is found.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingBinder.GetValue(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting,System.Type,System.String)">
            <summary>
            Extracts the value with the specified key and converts it to the specified type.
            </summary>
            <param name="setting">The setting.</param>
            <param name="type">The type to convert the value to.</param>
            <param name="key">The key of the setting section's value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingBinder.GetValue(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting,System.Type,System.String,System.Object)">
            <summary>
            Extracts the value with the specified key and converts it to the specified type.
            </summary>
            <param name="setting">The setting.</param>
            <param name="type">The type to convert the value to.</param>
            <param name="key">The key of the setting section's value to convert.</param>
            <param name="defaultValue">The default value to use if no value is found.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Extensions.ApplicationBuilderExtensions.UseAppSetting(Microsoft.AspNetCore.Builder.IApplicationBuilder,System.Action{Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManagerBuilder})">
            <summary>
            Adds app setting to the middleware pipeline.
            The setting will be exposed to the application by injecting 
            the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting"/> type in the constructor.
            It's better to use <code>app.UseAppSetting()</code> right before
            <code>app.UseMvc()</code>.
            </summary>
            <param name="app"></param>
            <param name="settingBuilderAction">The action to configure setting builder.</param>
            <returns></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Extensions.ApplicationBuilderExtensions.UseAppSetting(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            Adds app setting to the middleware pipeline.
            The setting will be exposed to the application by injecting 
            the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting"/> type in the constructor.
            It's better to use <code>app.UseAppSetting()</code> right before
            <code>app.UseMvc()</code>.
            </summary>
            <param name="app"></param>
            <returns></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Extensions.ApplicationBuilderExtensions.UseDefaultAppSetting(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            Adds hierarchical app setting (including 4 levels) to the middleware pipeline.
            The setting will be exposed to the application by injecting 
            the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISetting"/> type in the constructor.
            It's better to use <code>app.UseAppSetting()</code> right before
            <code>app.UseMvc()</code>. The service <see cref="T:Microsoft.Extensions.Options.IOptions`1"/> of type <see cref="T:Euroland.NetCore.ToolsFramework.Setting.SettingOptions"/>
            must be register in <code>ConfigureServices()</code> method.
            </summary>
            <param name="app"></param>
            <returns></returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Extensions.SettingItemExtensions">
            <summary>
            An extensions class for the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem"/>.
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Extensions.SettingItemExtensions.Exist(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem)">
            <summary>
            Check whether the setting item exists.
            </summary>
            <param name="setting">The <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem"/>.</param>
            <returns>true if setting exists, false otherwise.</returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Extensions.SettingServiceCollectionExtensions">
            <summary>
            Extension methods for custom service based on Reqquest
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Extensions.SettingServiceCollectionExtensions.AddAppSetting(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Registers configuration for manipulating the Azure File Service. 
            </summary>
            <param name="services">The service collection.</param>
            <returns></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Extensions.SettingServiceCollectionExtensions.AddAppSetting``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{``0})">
            <summary>
            Registers configuration for manipulating the Azure File Service. 
            </summary>
            <param name="services">The service collection.</param>
            <param name="setupAction">The options.</param>
            <returns></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Extensions.SettingServiceCollectionExtensions.AddBackwardCompatibleAppSetting(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds backward compability so that the old implementation which has inject <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItemRoot"/>
            in constructor may access normally.
            </summary>
            <param name="services"></param>
            <returns></returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderBase">
            <summary>
            Class to handle the setting from file.
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderBase.#ctor(Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderFactoryBase)">
            <summary>
            Creates a new <see cref="T:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderBase"/>
            </summary>
            <param name="settingProviderFactory">The factory to create provider</param>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderBase.Load">
            <inheritdoc />
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderBase.Load(System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderBase.Load(System.Boolean)">
            <inheritdoc />
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderFactoryBase">
            <summary>
            An implementation of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory"/> to create the 
            <see cref="T:Microsoft.Extensions.FileProviders.PhysicalFileProvider"/> that handles setting from file
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderFactoryBase.FileProvider">
            <summary>
            Gets or sets an <see cref="T:Microsoft.Extensions.FileProviders.IFileProvider"/> for the root contains the setting file
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderFactoryBase.Optional">
            <summary>
            Gets or sets the value to specify the setting in this root directory
            is optional. The one is optional means that, application will not
            throw an exception if not found a particular setting or an error
            occurs at runtime.
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderFactoryBase.Path">
            <summary>
            Gets or sets path to setting file directory
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderFactoryBase.Create">
            <summary>
            Creates the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/>
            </summary>
            <exception cref="T:Euroland.NetCore.ToolsFramework.Setting.SettingFileNotFoundException" />
            <returns></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderFactoryBase.GetSettingFileInfo">
            <summary>
            Gets an <see cref="T:Microsoft.Extensions.FileProviders.IFileInfo"/> to be processed in <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/>
            </summary>
            <returns>The <see cref="T:Microsoft.Extensions.FileProviders.IFileInfo"/></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderFactoryBase.CreateSmartlySettingProvider">
            <summary>
            Creates a <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/> based on the type of resource file (.JSON or .XML)
            </summary>
            <returns>
            An <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/>. 
            Returns <c>null</c> if no matching provider with file's extension is found.
            </returns>
            <exception cref="T:Euroland.NetCore.ToolsFramework.Setting.SettingFileNotFoundException" />
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderFactoryBase.EnsureFileProvider">
            <summary>
            Ensures the root directory of setting files exists, or reference to <see cref="T:Microsoft.Extensions.FileProviders.NullFileProvider"/>
            to avoid exception at runtime.
            </summary>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Json.JsonSettingProvider">
            <summary>
            Class to flatten json structure to a simple <see cref="!:IDictionary&lt;string, string&gt;"/>
            Found an article flatterning json structure at http://www.bfcamara.com/post/75172803617/flatten-json-object-to-send-within-an-azure-hub
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Json.JsonSettingProvider.Load(System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Json.JsonSettingProvider.Unflatten">
            <summary>
            Converts the key/pair values of <see cref="T:System.Collections.Generic.Dictionary`2"/> 
            back to the <see cref="T:Newtonsoft.Json.Linq.JObject"/>
            </summary>
            <returns></returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Options.ConfigureSettingOptions`1">
            <summary>
            Implements the <see cref="T:Microsoft.Extensions.Options.IConfigureOptions`1"/> 
            that configures the <typeparamref name="TAppSettingType"/>.
            </summary>
            <typeparam name="TAppSettingType">The application setting type</typeparam>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Options.ConfigureSettingOptions`1.#ctor(Microsoft.Extensions.DependencyInjection.IServiceScopeFactory)">
            <summary>
            Instantiates the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Options.ConfigureSettingOptions`1"/>
            </summary>
            <param name="serviceScopeFactory"></param>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.FormRequestSettingFinder">
            <summary>
            Determines the information for a request via values in the form
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.FormRequestSettingFinder.CompanyCodeFormKey">
            <summary>
            Gets or sets key that contains the company code value
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.FormRequestSettingFinder.VersionFormKey">
            <summary>
            Gets or sets key that contains the version of setting. If version is specified,
            the <see cref="T:System.IServiceProvider"/> will use version as a main setting
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.FormRequestSettingFinder.LanguageFormKey">
            <summary>
            Gets or sets key that contains the current language
            </summary>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.QueryStringRequestSettingFinder">
            <summary>
            Determines the information for a request via values in the query string
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.QueryStringRequestSettingFinder.#ctor(System.Func{Microsoft.AspNetCore.Http.HttpContext})">
            <summary>
            Creates a <see cref="T:Euroland.NetCore.ToolsFramework.Setting.QueryStringRequestSettingFinder"/>
            </summary>
            <param name="_httpContextAccessor">Accessor to get the current <see cref="T:Microsoft.AspNetCore.Http.HttpContext"/> which holding the <see cref="T:Microsoft.AspNetCore.Http.HttpRequest"/> instance</param>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.QueryStringRequestSettingFinder.CompanyCodeQueryStringKey">
            <summary>
            Gets or sets key that contains the company code value
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.QueryStringRequestSettingFinder.VersionQueryStringKey">
            <summary>
            Gets or sets key that contains the version of setting. If version is specified,
            the <see cref="T:System.IServiceProvider"/> will use version as a main setting
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.QueryStringRequestSettingFinder.LanguageQueryStringKey">
            <summary>
            Gets or sets key that contains the current language
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.QueryStringRequestSettingFinder.DetermineProviderSettingResourceResult">
            <summary>
            Gets a value of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.SettingResourceResult"/>
            </summary>
            <returns>The <see cref="T:Euroland.NetCore.ToolsFramework.Setting.SettingResourceResult"/></returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.RequestSettingProviderFactory">
            <summary>
            Represents a factory to create <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/> 
            based on <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.IRequestSettingFinder"/> to process resource content for JSON or XML format
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.RequestSettingProviderFactory.RequestFinder">
            <summary>
            List of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.IRequestSettingFinder"/>
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.RequestSettingProviderFactory.ResourceType">
            <summary>
            Gets or sets type of format of setting resource
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.RequestSettingProviderFactory.#ctor">
            <summary>
            Create a <see cref="T:Euroland.NetCore.ToolsFramework.Setting.RequestSettingProviderFactory"/>
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.RequestSettingProviderFactory.#ctor(System.String)">
            <summary>
            Create a <see cref="T:Euroland.NetCore.ToolsFramework.Setting.RequestSettingProviderFactory"/>
            </summary>
            <param name="settingRootPath">The absolute physical path to the root directory where the setting files are located</param>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.RequestSettingProviderFactory.#ctor(System.String,System.Boolean)">
            <summary>
            Create a <see cref="T:Euroland.NetCore.ToolsFramework.Setting.RequestSettingProviderFactory"/>
            </summary>
            <param name="settingRootPath">The absolute physical path to the root directory where the setting files are located</param>
            <param name="optional"></param>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.RequestSettingProviderFactory.GetSettingFileInfo">
            <summary>
            Gets the <see cref="T:Microsoft.Extensions.FileProviders.IFileInfo"/> which hold the setting file
            </summary>
            <returns>The <see cref="T:Microsoft.Extensions.FileProviders.IFileInfo"/></returns>
            <exception cref="T:Euroland.NetCore.ToolsFramework.Setting.SettingFileNotFoundException" />
            <exception cref="T:Euroland.NetCore.ToolsFramework.Setting.InvalidSettingException" />
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.SettingChangeToken">
            <summary>
            An implementaion of <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> to propagate 
            notifications that a setting change has occurred
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingChangeToken.HasChanged">
            <summary>
            Gets a value tha indicates if a change has occurred
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingChangeToken.RegisterChangeCallback(System.Action{System.Object},System.Object)">
            <summary>
             Registers for a callback that will be invoked when the entry has changed. 
            </summary>
            <param name="callback">The <see cref="!:Action&lt;object&gt;"/> to invoke</param>
            <param name="state">The state to be passed into the callback</param>
            <returns>An <see cref="T:System.IDisposable"/> that is used to unregister the callback</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingChangeToken.OnChange">
            <summary>
            Trigger the change token when a reload occurs
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingFlatterningKeyUtil.GetSettingName(System.String)">
            <summary>
            Gets setting name from the given path
            </summary>
            <param name="path">The path to the this setting</param>
            <returns>Name of the setting</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingFlatterningKeyUtil.GetClosestSettingName(System.String,System.String)">
            <summary>
            Gets setting name closest to the path delimiter from specified path
            </summary>
            <param name="path">The setting path</param>
            <param name="parentPath">The prefix parent to look up setting name</param>
            <returns></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingFlatterningKeyUtil.GetSettingParentName(System.String)">
            <summary>
            Gets parent name from the given path
            </summary>
            <param name="path">The path to the this setting</param>
            <returns>The path to the parent</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingFlatterningKeyUtil.Join(System.String,System.String)">
            <summary>
            Combines the path segments into single path
            </summary>
            <param name="name">The name of current setting</param>
            <param name="path">The parent's path of current setting</param>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingFlatterningKeyUtil.Join(System.String,System.Int32)">
            <summary>
            Combines the path segments into single path
            </summary>
            <param name="name">The name of current setting</param>
            <param name="index">The index of item in array</param>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingFlatterningKeyUtil.GetPathSegments(System.String)">
            <summary>
            Splits out the path to an array of segments of path
            </summary>
            <param name="path">Path to split</param>
            <returns>Array of segments</returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.SettingItem">
            <summary>
            An implementation of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem"/>
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingItem.#ctor(Euroland.NetCore.ToolsFramework.Setting.SettingItemRoot,System.String)">
            <summary>
            Create a new <see cref="T:Euroland.NetCore.ToolsFramework.Setting.SettingItem"/>
            </summary>
            <param name="root">The root setting item</param>
            <param name="path">The path to this setting item</param>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.SettingItemRoot">
            <summary>
            An implmentation of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItemRoot"/>
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingItemRoot.#ctor(System.Collections.Generic.IEnumerable{Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider})">
            <summary>
            Create a new <see cref="T:Euroland.NetCore.ToolsFramework.Setting.SettingItemRoot"/> with a list of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/>
            </summary>
            <param name="settingProviders">The list of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/> for this setting</param>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingItemRoot.Item(System.String)">
            <summary>
            Gets the value of current setting item with specified setting name
            </summary>
            <param name="name">The setting name</param>
            <returns>The value of child setting</returns>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingItemRoot.Providers">
            <summary>
            Gets the list providers of <see cref="T:System.IServiceProvider"/>
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingItemRoot.Updatable">
            <summary>
            Gets or sets value to indicate that setting can be changed the value during at runtime
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingItemRoot.GetChild(System.String)">
            <summary>
            Gets a child with specific child's name
            </summary>
            <param name="name">The name/key of child</param>
            <returns>The <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem"/></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingItemRoot.GetChildren">
            <summary>
            Gets list of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem"/> of children
            </summary>
            <returns>The list of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItem"/></returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.SettingManager">
            <summary>
            An implementation of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManager"/>
            </summary>
        </member>
        <member name="F:Euroland.NetCore.ToolsFramework.Setting.SettingManager._settingProviderFactories">
            <summary>
            List of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory"/>
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingManager.SettingProviderFactories">
            <summary>
            Gets the list of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory"/> that used to create <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/>. 
            The later item added will be at the first position of list.
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingManager.RequestSettingFinders">
            <summary>
            Gets the list of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.IRequestSettingFinder"/> that used to find <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/> based on HTTP Request
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingManager.Accept(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory)">
            <summary>
            Add a new <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory"/>. The later added one will merge and replace
            the keys of earlier
            </summary>
            <param name="factory">The <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory"/></param>
            <returns>The <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManager"/> as a chain for reusing factory</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingManager.Create">
            <summary>
            Creates the root <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItemRoot"/> of application setting from 
            the set of setting provider factories registered in <see cref="P:Euroland.NetCore.ToolsFramework.Setting.SettingManager.SettingProviderFactories"/>
            </summary>
            <returns>An <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingItemRoot"/></returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.SettingManagerBuilder">
            <summary>
            Defauts builder manager.
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingManagerBuilder.#ctor">
            <summary>
            Creates an instance of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.SettingManagerBuilder"/>
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingManagerBuilder.SettingManager">
            <summary>
            The manager which holds the list of registered <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/>.
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingManagerBuilder.UseHttpRequestSetting(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingManagerBuilder.UseHttpRequestSetting(System.String,Euroland.NetCore.ToolsFramework.Setting.SettingResourceType)">
            <inheritdoc/>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingManagerBuilder.UseHttpRequestSetting(System.String,System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingManagerBuilder.UseHttpRequestSetting(System.String,Euroland.NetCore.ToolsFramework.Setting.SettingResourceType,System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingManagerBuilder.UseStaticSetting(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingManagerBuilder.UseStaticSetting(System.String,System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingManagerBuilder.UseSettingProvider(Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProviderFactory)">
            <inheritdoc/>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.SettingMiddleware">
            <summary>
            Enables serving company setting for a given request path with query string companyCode
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingManagerBuilder)">
            <summary>
            Creates a new instance of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.SettingMiddleware"/>
            </summary>
            <param name="nextDelegate">The next middleware in the pipeline.</param>
            <param name="settingManagerBuilder"></param>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.SettingOptions">
            <summary>
            Class contains the options for <see cref="T:Euroland.NetCore.ToolsFramework.Setting.SettingMiddleware"/>
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingOptions.AppConfigurationFileName">
            <summary>
            Gets or sets the main configuration file name (including file extension) of application.
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingOptions.GeneralSettingRootDirectory">
            <summary>
            Gets or sets path of directory where where the general setting files are located.
            </summary>
            <example>
            <code>1. wwwroot/Tools/Config</code>
            </example>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingOptions.ToolSettingRootDirectory">
            <summary>
            Gets or sets the path of directory where where the setting files of a specified application are located.
            For Euroland's tools, have two root directory for a application.
            </summary>
            <example>
            <code>2. wwwroot/Tools/{APPLICATION}/Config</code>
            </example>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingOptions.ReloadOnChange">
            <summary>
            Gets value to indicate that whether the setting should be reloaded if the file changes.
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingOptions.ExcludePaths">
            <summary>
            Gets or sets list of request paths that should be excluded out of serving Setting module.
            </summary>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.SettingProviderBase">
            <summary>
            A base class implements the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/> provides the common task of setting
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingProviderBase.#ctor">
            <summary>
            Creates a new <see cref="T:Euroland.NetCore.ToolsFramework.Setting.SettingProviderBase"/>
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingProviderBase.Data">
            <summary>
            The priority level of setting.
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingProviderBase.Load">
            <inheritdoc />
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingProviderBase.GetData">
            <inheritdoc />
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingProviderBase.Set(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingProviderBase.TryGet(System.String,System.String@)">
            <inheritdoc />
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingProviderBase.GetChildKeys(System.Collections.Generic.IEnumerable{System.String},System.String)">
            <inheritdoc />
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingProviderBase.Save(System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.SettingProviderBase.SaveAsync(System.IO.Stream)">
            <inheritdoc />
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.SettingResourceResult">
            <summary>
            Details about the setting resource obtained from <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.IRequestSettingFinder"/>
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingResourceResult.CompanyCode">
            <summary>
            Gets or sets the company code
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingResourceResult.Version">
            <summary>
            Gets or sets the version setting of company
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.SettingResourceResult.Language">
            <summary>
            Gets or sets the request language
            </summary>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.SettingResourceType">
            <summary>
            Types of setting resource should hangled by system
            </summary>
        </member>
        <member name="F:Euroland.NetCore.ToolsFramework.Setting.SettingResourceType.Json">
            <summary>
            Handles JSON format resource only
            </summary>
        </member>
        <member name="F:Euroland.NetCore.ToolsFramework.Setting.SettingResourceType.Xml">
            <summary>
            Handles XML format resource only
            </summary>
        </member>
        <member name="F:Euroland.NetCore.ToolsFramework.Setting.SettingResourceType.Json_Xml">
            <summary>
            Detects type of format of resource automatically 
            </summary>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.StaticFileContentTypeHelper">
            <summary>
            A helper class to determine a request is to static resource by looking up
            the MIME types given a file path.
            </summary>
        </member>
        <member name="P:Euroland.NetCore.ToolsFramework.Setting.StaticFileContentTypeHelper.Mappings">
            <summary>
            The cross reference table of file extensions and content-types.
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.StaticFileContentTypeHelper.TryGetContentType(System.String,System.String@)">
            <summary>
            Given a file path, determine the MIME type
            </summary>
            <param name="subpath">A file path</param>
            <param name="contentType">The resulting MIME type</param>
            <returns>True if MIME type could be determined</returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.StaticFileSettingProviderFactory">
            <summary>
            Implementation of <see cref="T:Euroland.NetCore.ToolsFramework.Setting.FileSettingProviderFactoryBase"/> 
            to create <see cref="T:Euroland.NetCore.ToolsFramework.Setting.Abstractions.ISettingProvider"/> for static physical file
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.StaticFileSettingProviderFactory.#ctor">
            <summary>
            Instantiates the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.StaticFileSettingProviderFactory"/>
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.StaticFileSettingProviderFactory.#ctor(System.String)">
            <summary>
            Instantiates the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.StaticFileSettingProviderFactory"/>
            </summary>
            <param name="staticFilePath">The absolute path to a specified physical file</param>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.StaticFileSettingProviderFactory.#ctor(System.String,System.Boolean)">
            <summary>
            Instantiates the <see cref="T:Euroland.NetCore.ToolsFramework.Setting.StaticFileSettingProviderFactory"/>
            </summary>
            <param name="staticFilePath">The absolute path to a specified physical file</param>
            <param name="optional">Whether this resource is optional</param>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.TypeHelper">
            <summary>
            Helper class to convert a string value to appropriate primitive types.
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.TypeHelper.StringToObject(System.String,System.Type@)">
            <summary>
            Converts a string value to a <see cref="T:System.Object"/>. Supports only primitive types: 
            <see cref="T:System.Int32"/>, 
            <see cref="T:System.Decimal"/>, 
            <see cref="T:System.Double"/>,
            <see cref="T:System.Boolean"/>,
            <see cref="T:System.String"/>
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="T:Euroland.NetCore.ToolsFramework.Setting.Xml.XmlSettingProvider">
            <summary>
            Class to flatten XML structure to a simple <see cref="!:IDictionary&lt;string, string&gt;"/>
            </summary>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Xml.XmlSettingProvider.Unflatten">
            <summary>
            Converts the key/pair values of <see cref="T:System.Collections.Generic.Dictionary`2"/> 
            back to the <see cref="T:System.Xml.Linq.XDocument"/>
            </summary>
            <returns></returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Xml.XmlSettingProvider.GetChild(System.Xml.Linq.XElement,System.String,System.Int32)">
            <summary>
            Gets child element in array of <see cref="T:System.Xml.Linq.XElement"/>.
            </summary>
            <param name="parent">The parrent node.</param>
            <param name="childElementName">Element name.</param>
            <param name="childIndex">Zero-based index of element in array.</param>
            <returns>Element if found. Otherwise, Null.</returns>
        </member>
        <member name="M:Euroland.NetCore.ToolsFramework.Setting.Xml.XmlSettingProvider.GetAttribute(System.Xml.Linq.XElement,System.String)">
            <summary>
            Gets attribute of a specific element.
            </summary>
            <param name="element"></param>
            <param name="attributeName"></param>
            <returns></returns>
        </member>
    </members>
</doc>
