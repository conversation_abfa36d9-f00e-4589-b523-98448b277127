﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Fragulizer.Common;

namespace Fragulizer.Models.Partials
{
    public class MarketInfo
    {
        public short MarketNumber { get; set; }
        public int TranslationId { get; set; }
        public string MarketOpenTimeLocal { get; set; }
        public string MarketCloseTimeLocal { get; set; }
        public string DataSource { get; set; }
        public string Delay { get; set; }
        public string TimeZone { get; set; }
        public TimeSpan OpenTime
        {
            get
            {
                return Utility.ConvertTimeSpan(TimeSpan.Parse(MarketOpenTimeLocal),
                    TimeZone, Tool.Settings.timezone);
            }
            set
            {
            }
        }
        public TimeSpan CloseTime
        {
            get
            {
                return Utility.ConvertTimeSpan(TimeSpan.Parse(MarketCloseTimeLocal),
                    TimeZone, Tool.Settings.timezone);
            }
            set
            {
            }
        }
        public string MarketName { get; set; }
    }
}