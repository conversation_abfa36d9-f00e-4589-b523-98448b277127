﻿<#
    var currentDirectory = Path.GetDirectoryName(Host.TemplateFile);
    // customize these parameters for your own project
    var trParams = new TranslationEnumeratorParams
        {   
            XmlFileName = currentDirectory + "\\translations.xml",
			// Developers never use this configuration, don't uncomment it. It's used for deployment only
			DbConnString = "Data Source=************;Initial Catalog=Language;User=test;Password=Test"

			// Developers, please use bellow configuration
			//DbConnString = "Data Source=**********;Initial Catalog=LanguageTest;User=test;Password=******"
        };
#>
<#@ include file="$(ProjectDir)..\..\Generators\TranslationsGeneratorNewSolution.tt" #>
