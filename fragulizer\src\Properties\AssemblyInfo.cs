﻿using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

// General Information about an assembly is controlled through the following 
// set of attributes. Change these attribute values to modify the information
// associated with an assembly.
[assembly: AssemblyTitle("Fragulizer")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("Euroland.com")]
[assembly: AssemblyProduct("Fragulizer")]
[assembly: AssemblyCopyright("Copyright Euroland.com ©  2013")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

// Setting ComVisible to false makes the types in this assembly not visible 
// to COM components.  If you need to access a type in this assembly from 
// COM, set the ComVisible attribute to true on that type.
[assembly: ComVisible(false)]

// The following GUID is for the ID of the typelib if this project is exposed to COM
[assembly: Guid("3f228af8-1662-455a-b1de-9bde9e2ca061")]

// Version information for an assembly consists of the following four values:
//
//      Major Version
//      Minor Version 
//      Build Number
//      Revision
//
// You can specify all the values or you can default the Revision and Build Numbers 
// by using the '*' as shown below:
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
// v1.0.1.1 Update the latest highstock.js 
// v1.0.1.2 Due to the reason that custom stylesheets and xml settings are now getting 
//          only from Azure Virtual Machine, and the Blob Storage is no longer in use.
//          Therefore, all the configuration code that to custom to use on Azure are commented
// v1.0.2.0 Upgrade code to new version of ToolsFramework
// v1.0.3.0 Fixed Excel could not export with other language than english
/*
 * v1.0.4.0:
 *	+ Update google analytics script.
 *	+ change width of calendar. issue: https://trello.com/c/xLYuPPza/199-fragulizer-layout-break
 */
/*
 * v1.0.5.0:
 *  + Fix wrong colors of pie-chart when select custom period
 */
// v*******: Breaking changes
// + Upgrade jQuery to version 3.5.1
// + fix bug export excel without sharename
