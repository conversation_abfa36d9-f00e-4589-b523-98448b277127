﻿<!--
***********************************************************************************************
Microsoft.Web.Publishing.Deploy.FPSE.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your web deploy projects from the command-line or the IDE.

This file defines the steps in the standard package/publish process for Deploy 
Currently

Copyright (C) Microsoft Corporation. All rights reserved.
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Import task from our dll-->
  <UsingTask TaskName="GetPublishingLocalizedString" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="CopyPipelineFiles"  AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>


  <!--Append WebFTPPublish to the supported list.-->
  <PropertyGroup>
    <_WPPWebPublishMethodSupports>$(_WPPWebPublishMethodSupports);WebFileSystemPublish</_WPPWebPublishMethodSupports>
  </PropertyGroup>



  <!--********************************************************************-->
  <!--Target WebFileSystemPublish -->
  <!--********************************************************************-->
  <PropertyGroup>
    <WebFileSystemPublishDependsOn>
      $(OnBeforeWebFileSystemPublish);
      $(WebFileSystemPublishDependsOn);
    </WebFileSystemPublishDependsOn>
    <WebFileSystemPublishDependsOn>
      $(WebFileSystemPublishDependsOn);
      PipelinePreDeployCopyAllFilesToOneFolder;
    </WebFileSystemPublishDependsOn>
  </PropertyGroup>

  <Target Name="WebFileSystemPublish"
          DependsOnTargets="$(WebFileSystemPublishDependsOn)"
          Condition="'$(WebFileSystemPublish)' != 'False'">

    <PropertyGroup>
      <_DoWebFileSystemPublishDeleteExtraFiles>False</_DoWebFileSystemPublishDeleteExtraFiles>
      <_DoWebFileSystemPublishDeleteExtraFiles  Condition= "'$(DeleteExistingFiles)' == 'True'" >True</_DoWebFileSystemPublishDeleteExtraFiles>
      <_HttpHeader>http://</_HttpHeader>
      <_DoWebFileSystemPublish>False</_DoWebFileSystemPublish>
      <_DoWebFileSystemPublish Condition="'$(PublishUrl)'!='' And '$(PublishUrl.StartsWith($(_HttpHeader),  StringComparison.OrdinalIgnoreCase))' == 'False' And '$([System.IO.Path]::GetFullPath($(PublishUrl)))' != ''">True</_DoWebFileSystemPublish>
    </PropertyGroup>

    <!--This is not yet implemented through the command line for now error it out-->
    <GetPublishingLocalizedString
      Condition="!$(_DoWebFileSystemPublish)"
      ID="PublishLocalizedString_WebPublishMethodIsNotSupportedInCmdLine"
       ArgumentCount="1"
       Arguments="$(WebPublishMethod)"
       LogType="Error" />

    <Error  Text ="Target WebFileSystemPublish Failed"
      Condition="!$(_DoWebFileSystemPublish)"
            />

    <ItemGroup Condition="$(_DoWebFileSystemPublish)">
      <FilesForFileCopy Remove="$(FilesForFileCopy)" />
    </ItemGroup>

    <CollectFilesinFolder RootPath="$(WPPAllFilesInSingleFolder)"
                          Condition="$(_DoWebFileSystemPublish)" >
      <Output TaskParameter="Result" ItemName="_AllFilesUnder_WPPAllFilesInSingleFolder" />
    </CollectFilesinFolder>

    <ItemGroup>
      <FilesForFileCopy Include="@(_AllFilesUnder_WPPAllFilesInSingleFolder)" >
        <DestinationRelativePath>%(_AllFilesUnder_WPPAllFilesInSingleFolder.Identity)</DestinationRelativePath>
        <FromTarget>WebFileSystemPublish</FromTarget>
        <Category>AllFilesInFolder</Category>
      </FilesForFileCopy>
    </ItemGroup>


    <!-- In the case of the incremental Packaging/Publish, we need to find out the extra file and delee them-->
    <ItemGroup  Condition="'$(DeleteExistingFiles)' == 'True'">
      <_AllExtraFilesUnderPublishUrl Include="$(PublishUrl)\**" />
      <_AllExtraFilesUnderPublishUrl
        Remove="@(FilesForFileCopy->'$(PublishUrl)\%(DestinationRelativePath)')" />
    </ItemGroup>
    <!--Remove all extra files in the temp folder that's not in the @(FilesForPackagingFromProject-->
    <Delete Files="@(_AllExtraFilesUnderPublishUrl)" />
    <!-- Make sure the folder exist -->
    <MakeDir Directories="$(PublishUrl)" Condition="$(_DoWebFileSystemPublish) And !Exists('$(PublishUrl)')"/>

    <!--Force Copy Of all file to the WPPAllFilesInSingleFolder if needed-->
    <CopyPipelineFiles Condition="$(_DoWebFileSystemPublish)"
                           PipelineItems="@(FilesForFileCopy)"
                           SourceDirectory="$(WPPAllFilesInSingleFolder)"
                           TargetDirectory="$(PublishUrl)"
                           SkipMetadataExcludeTrueItems="False"
                           UpdateItemSpec="True"
                           DeleteItemsMarkAsExcludeTrue ="False">
      <Output TaskParameter="ResultPipelineItems" ItemName="_FilesForFileCopyAfterFileSystemPublish"/>
    </CopyPipelineFiles>


    <CallTarget Targets="$(OnAfterWebFileSystemPublish)" RunEachTargetSeparately="False" />
  </Target>

  <!--ImportAfter Extension-->
  <PropertyGroup>
    <ImportByWildcardAfterMicrosoftWebPublishingDeployFileSystemTargets Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingDeployFileSystemTargets)'==''">true</ImportByWildcardAfterMicrosoftWebPublishingDeployFileSystemTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter\*" Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingDeployFileSystemTargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter')"/>

</Project>
