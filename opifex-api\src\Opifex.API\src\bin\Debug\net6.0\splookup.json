{"AppSettings": {"SPLookupToolSettingMappers": [{"XmlPath": "StyleURI", "DtoPath": "StyleUri"}, {"XmlPath": "Template", "DtoPath": "Template", "NotEmpty": true, "IsString": true, "ValidValues": ["Quadrata", "Victor"]}, {"XmlPath": "ShowAnnualClosePrices", "DtoPath": "ShowAnnualClosePrices", "NotNull": true, "IsBool": true}, {"XmlPath": "NumberOfYearOnChart", "DtoPath": "NumberOfYearOnChart", "NotNull": true, "IsPositiveInteger": true}, {"XmlPath": "UseMarchFiscalYearEnd", "DtoPath": "UseMarchFiscalYearEnd", "NotNull": true, "IsBool": true}, {"XmlPath": "EnabledColorBlindMode", "DtoPath": "EnableColorBlindMode", "NotNull": true, "IsBool": true}, {"XmlPath": "EnableExcelDownload", "DtoPath": "EnableExcelDownload", "NotNull": true, "IsBool": true}, {"XmlPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DtoPath": "EnablePeers", "NotNull": true, "IsBool": true}]}}