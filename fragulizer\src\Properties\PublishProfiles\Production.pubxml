﻿<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the
behavior of this process
by editing this MSBuild file. In order to learn more about this please visit
https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <LastUsedBuildConfiguration>Production</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://tools.eurolandir.com/tools/fragulizer/?companycode=s-volv</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>False</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <!-- <MSDeployServiceURL>https://ee-v-webcat151.euroland.com:8172/msdeploy.axd</MSDeployServiceURL> -->
    <DeployIisAppPath>production-site/tools/fragulizer</DeployIisAppPath>
    <!-- <UserName>EE-V-WEBCAT151\WDeployAdmin2</UserName> -->
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>True</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>True</EnableMSDeployBackup>
    <_SavePWD>True</_SavePWD>
    <AllowUntrustedCertificate>True</AllowUntrustedCertificate>
    <DeployEnv>Production</DeployEnv>
    <EnvironmentName>Production</EnvironmentName>
  </PropertyGroup>
  <ItemGroup>
    <MsDeploySkipRules Include="CustomSkipFolder">
      <ObjectName>dirPath</ObjectName>
      <AbsolutePath>Config</AbsolutePath>
    </MsDeploySkipRules>
    <MsDeploySkipRules Include="CustomSkipFolder">
      <ObjectName>dirPath</ObjectName>
      <AbsolutePath>Styles\\Company</AbsolutePath>
    </MsDeploySkipRules>
    <MsDeploySkipRules Include="CustomSkipFile">
      <ObjectName>filePath</ObjectName>
      <AbsolutePath>.*\.config$</AbsolutePath>
    </MsDeploySkipRules>
  </ItemGroup>
</Project>
