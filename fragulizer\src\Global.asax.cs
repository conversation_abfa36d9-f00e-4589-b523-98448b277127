﻿using System;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;
using Fragulizer.Common;
using ToolsFramework.Mvc;

namespace Fragulizer
{
    // Note: For instructions on enabling IIS6 or IIS7 classic mode, 
    // visit http://go.microsoft.com/?LinkId=9394801

    public class MvcApplication : ToolsFrameworkHttpApplicationBase
    {
        public static void RegisterGlobalFilters(GlobalFilterCollection filters)
        {
            filters.Add(new HandleErrorAttribute());
        }

        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");

            routes.MapRoute(
                "Default", // Route name
                "{controller}/{action}", // URL with parameters
                new { controller = "Home", action = "Index" } // Parameter defaults
            );

        }
        private static Action<string, Func<string, bool>> GetConfiguration()
        {
            return (configName, configSetter)
                => configSetter(Euroland.Azure.Helpers.AzureConfiguration.GetConfigurationSetting(
                        configName,
                        string.Empty,
                        true
                   )
            );
        }
        protected void Application_Start()
        {
            // Remove whole default registered view engines by .NET MVC
            // Tools solution uses only Razor View Engine
            ViewEngines.Engines.Clear();
            ViewEngines.Engines.Add(new RazorViewEngine());

            //Microsoft.WindowsAzure.CloudStorageAccount.SetConfigurationSettingPublisher(GetConfiguration());
            
            AreaRegistration.RegisterAllAreas();

            RegisterGlobalFilters(GlobalFilters.Filters);
            RegisterRoutes(RouteTable.Routes);
            UnityObject.Register();

            ToolsFramework.Mvc.AuditSettingInitialization.Init(() => Tool.Settings);
        }

        protected void Application_Error()
        {
            Exception exception = Server.GetLastError();

            if (exception != null && typeof(Euroland.NetCore.ToolsFramework.Setting.SettingFileNotFoundException).IsInstanceOfType(exception))
            {
                Server.ClearError();
                Response.Write("Not found company setting");
            }
        }
    }
}