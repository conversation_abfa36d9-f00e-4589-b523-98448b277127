export const isObjectEmpty = (objectName) => {
  return (
    Object.keys(objectName).length === 0 && objectName.constructor === Object
  );
};

export const isArrayEmpty = (array) => {
  return array.length === 0;
};

export const isObjectAndNotArray = (variable) =>
  typeof variable === "object" && variable !== null && !Array.isArray(variable);

export const addBorderSolidDefaultStyle = ({ borderWidth, borderColor }) => {
  return borderWidth && borderColor
    ? {
        borderWidth: borderWidth,
        borderColor: borderColor,
        borderStyle: "solid",
      }
    : { border: "unset" };
};

export const addBorderEdgeSolidDefaultStyle = ({
  edge,
  borderWidth,
  borderColor,
}) => {
  return edge && borderWidth && borderColor
    ? {
        [`border${edge}Width`]: borderWidth,
        [`border${edge}Color`]: borderColor,
        [`border${edge}Style`]: "solid",
      }
    : edge
    ? { [`border${edge}`]: "unset" }
    : {};
};
// borderLeftWidth: 11px
export const addBorderRadiusDefaultStyle = ({
  borderTopLeftRadius,
  borderTopRightRadius,
  borderBottomLeftRadius,
  borderBottomRightRadius,
}) => {
  return {
    borderBottomLeftRadius: borderBottomLeftRadius ?? "unset",
    borderBottomRightRadius: borderBottomRightRadius ?? "unset",
    borderTopLeftRadius: borderTopLeftRadius ?? "unset",
    borderTopRightRadius: borderTopRightRadius ?? "unset",
  };
};

export const markImportantCssProperty = (property) => {
  if (!property) return {};

  const result = {};
  Object.entries(property).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      result[key] = `${value} !important`;
    }
  });
  return result;
};

export function parseSizeUnit(input) {
  if (typeof input !== "string") return null;
  const match = input.match(/^(\d+(\.\d+)?)([a-zA-Z%]+)$/);
  if (match) {
    return {
      number: parseFloat(match[1]),
      unit: match[3],
    };
  }
  return null;
}
