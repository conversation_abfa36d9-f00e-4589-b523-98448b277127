﻿using System;

namespace Fragulizer.Models.Partials
{
    public class InstrumentInfo
    {
        private DateTime _Time;

        public int InstrumentID { get; set; }

        public short MarketId { get; set; }

        public string MarketName { get; set; }

        public Decimal Last { get; set; }

        public decimal Change { get; set; }

        public decimal ChangePro { get; set; }

        public Nullable<decimal> High { get; set; }

        public Nullable<decimal> Low { get; set; }

        public Nullable<long> Volume { get; set; }

        public string CurrencyCode { get; set; }

        public DateTime Time
        {
            get
            {
                return _Time;
            }
            set
            {
                _Time = DateTime.SpecifyKind(value, DateTimeKind.Unspecified);
            }
        }
        public int MarketTranslationID { get; set; }
    }
}