﻿using Euroland.Azure;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Web;
using ToolsFramework.Settings;

namespace Fragulizer.Common
{
    public static class HttpContextExtensions
    {
        public static bool IsPreviewMode(HttpRequest request)
        {
            var isPreview = request.QueryString != null ? request.QueryString.ToString().Contains("isPreview") : false;
            var enablePreview = ConfigurationManager.AppSettings["Opifex.PreviewEnabled"];
            return (isPreview == true && enablePreview == "true");

        }


        public static ICollection<string> GetToolsStyleUri(HttpRequest request)
        {
            var enablePreview = IsPreviewMode(request);

            if (!enablePreview)
            {
                return new List<string>() {
                    Tool.Settings.getStyleFileUri(ESETTINGS_LEVEL.GENERAL),
                    Tool.Settings.getStyleFileUri(ESETTINGS_LEVEL.TOOL),
                    Tool.Settings.getStyleFileUri(ESETTINGS_LEVEL.COMPANY),
                    Tool.Settings.getStyleFileUri(ESETTINGS_LEVEL.TOOL_COMPANY)
                };

                
            }
            else
            {
                var genPath = System.Configuration.ConfigurationManager.AppSettings["Opifex.ToolGeneralStyleSheetPath"];
                var toolPath = System.Configuration.ConfigurationManager.AppSettings["Opifex.ToolCompanyStyleSheetPath"];

                var toolFile = Path.Combine(toolPath, "Company", Tool.Settings.NameCompany, Tool.Settings.NameCompany + ".css").Replace(@"\", "/");


                //var opifexToolCompanyStyle = 
                return new List<string>() {
                    Tool.Settings.getStyleFileUri(ESETTINGS_LEVEL.GENERAL),
                    Tool.Settings.getStyleFileUri(ESETTINGS_LEVEL.TOOL),
                    Tool.Settings.getStyleFileUri(ESETTINGS_LEVEL.COMPANY),
                    Tool.Settings.getStyleFileUri(ESETTINGS_LEVEL.TOOL_COMPANY),
                    toolFile
                };
            }
        }
    }
     

}