﻿using System.Configuration;
using System.Web;
using Euroland.Azure;
using Euroland.Azure.Helpers;
using Euroland.Azure.Shared.ClientResource;
using Euroland.Azure.Shared.Logger;
using Fragulizer.Models;
using ToolsFramework;
using ToolsFramework.Cache;
using ToolsFramework.Data;
using ToolsFramework.Settings;
using Fragulizer.SettingService;
using Fragulizer.Common.NewToolsFramework;

namespace Fragulizer.Common
{
    public class UnityObject
    {
        public static void Register()
        {
            bool isOnCloud = Euroland.Azure.Utilities.AzureEnvironment.IsAvailable;

            Services.RegisterInstance<ICache>(new HttpRequestCacheResolver());//HttpSessionStateCacheResolver());

            /*if (isOnCloud)
            {
                string errorBlobAcc = AzureConfiguration.GetConfigurationSetting("AzureStorageAccountConn.Error", "");
                if (!string.IsNullOrEmpty(errorBlobAcc))
                {
                    Services.RegisterInstance<ILogger>(new BlobLogger(
                        errorBlobAcc,
                        AzureConfiguration.GetConfigurationSetting("ErrorBlobContainerName", ""),
                        "Fragulizer")
                    );
                }
            }
            else
            {
                Services.RegisterInstance<ILogger>(new MailServiceLogger("Fragulizer"));
            }*/

            Services.RegisterInstance<ILogger>(new MailServiceLogger("Fragulizer"));

            string languageConnString = ConfigurationManager.ConnectionStrings["LanguageConnectionString"].ConnectionString;
            string europeConnString = ConfigurationManager.ConnectionStrings["EuropeConnectionString"].ConnectionString;
            
            if (string.IsNullOrEmpty(languageConnString))
                throw new System.NullReferenceException("@languageConnString cannot be null or empty");
            if (string.IsNullOrEmpty(europeConnString))
                throw new System.NullReferenceException("@europeConnString cannot be null or empty");

            // [25-April-2012][binh.nguyen]: New multi-language solution
            Services.RegisterType<ILanguageService>(c => new LanguageService(
                new HttpApplicationStateCacheResolver(),
                () => RequestHelper.lang,
                languageConnString,
                (phrase) => SettingsUtility.GetCustomPhrases(phrase, Tool.Settings))
            );

            Services.RegisterType<IDatabase>(c => new EuropeDatabase(europeConnString));

            Services.RegisterType<IUrlResolver>(c => new ResoureUrlResolver());
                string genPath = HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["GeneralSettingsPath"];
                string toolPath = HttpContext.Current.Server.MapPath("~/Config/");
            //Services.RegisterType<ISettingsService>(c => new SettingsService(c.Resolve<ICache>(), "Fragulizer", genPath, toolPath));
            Services.RegisterType<ISettingsService>(c => new SettingsServiceWithPreviewMode("Fragulizer", genPath, toolPath));


            Services.RegisterType<IInstrumentGroupSettingService>(c => new InstrumentGroupSettingService(c.Resolve<ICache>(), "Fragulizer", genPath, toolPath));
                //XmlConfigProvider.Initialize(new ToolsFramework.Cache.HttpRuntimeCacheResolver("Fragulizer"));

            Services.RegisterType<IFragulizerRepository>(c => new FragulizerRepository(
                c.Resolve<IDatabase>(),
                c.Resolve<ICache>())
                );

            ClientResourceRegistrationExtension.Initialize(
                () => Services.Get<IUrlResolver>(),
                () => AzureConfiguration.GetConfigurationSetting("EnableCssJsDebuging", "False").ToLower() == "true",
                false
            );

            /* 
             * [04-Apr-2014 binh.nguyen]
             * Due to the reason that custom stylesheets and xml settings are now getting 
             * only from Azure Virtual Machine, and the Blob Storage is no longer in use.
             * Therefore, all the configuration code that to custom to use on Azure are commented
             */
            #region [binh.nguyen] Comment out
            /*AspectF.Define
            .Do(() =>
            {
                bool isOnCloud = Euroland.Azure.Utilities.AzureEnvironment.IsAvailable;

                Services.RegisterInstance<ICache>(new HttpRequestCacheResolver());//HttpSessionStateCacheResolver());

                if (isOnCloud)
                {
                    string errorBlobAcc = AzureConfiguration.GetConfigurationSetting("AzureStorageAccountConn.Error", "");
                    if (!string.IsNullOrEmpty(errorBlobAcc))
                    {
                        Services.RegisterInstance<ILogger>(new BlobLogger(
                            errorBlobAcc,
                            AzureConfiguration.GetConfigurationSetting("ErrorBlobContainerName", ""),
                            "Fragulizer")
                        );
                    }
                }
                else
                {
                    Services.RegisterInstance<ILogger>(new MailServiceLogger("Fragulizer"));
                }

                string languageConnString = AzureConfiguration.GetConfigurationSetting("LanguageConnectionString", "");
                string europeConnString = AzureConfiguration.GetConfigurationSetting("AzureSharkConnectionString", "");
                if (!isOnCloud)
                {
                    languageConnString = ConfigurationManager.ConnectionStrings["LanguageConnectionString"].ConnectionString;
                    europeConnString = ConfigurationManager.ConnectionStrings["EuropeConnectionString"].ConnectionString;
                }

                if (string.IsNullOrEmpty(languageConnString))
                    throw new System.NullReferenceException("@languageConnString cannot be null or empty");
                if (string.IsNullOrEmpty(europeConnString))
                    throw new System.NullReferenceException("@europeConnString cannot be null or empty");

                // [25-April-2012][binh.nguyen]: New multi-language solution
                Services.RegisterType<ILanguageService>(c => new LanguageService(
                    new HttpApplicationStateCacheResolver(),
                    () => RequestHelper.lang,
                    languageConnString,
                    (phrase) => SettingsUtility.GetCustomPhrases(phrase, Tool.Settings))
                );

                Services.RegisterType<IDatabase>(c => new EuropeDatabase(europeConnString));

                if (isOnCloud)
                {
                    var resourceAccount = Microsoft.WindowsAzure.CloudStorageAccount.FromConfigurationSetting("AzureStorageAccountConn.ClientResource");
                    var toolsSettingAccount = Microsoft.WindowsAzure.CloudStorageAccount.FromConfigurationSetting("AzureStorageAccountConn.ToolsSetting");

                    string cdnHost = AzureConfiguration.GetConfigurationSetting("StaticResourceCDNHost", "");
                    Services.RegisterType<IUrlResolver>(c =>
                    {
                        if (!string.IsNullOrEmpty(cdnHost))
                            return new BlobResoureUrlResolver(resourceAccount,
                                AzureConfiguration.GetConfigurationSetting(
                                    "BlobResourceContainer",
                                    "Fragulizer"),
                                cdnHost
                            );
                        else
                            return new BlobResoureUrlResolver(resourceAccount,
                                AzureConfiguration.GetConfigurationSetting(
                                    "BlobResourceContainer",
                                    "Fragulizer")
                            );
                    }
                    );

                    string xmlConfigurationBlobContainer = AzureConfiguration.GetConfigurationSetting(
                        "ToolsSettingBlobContainerName",
                        "ToolsSetting" // Default blob container name
                    );

                    Services.RegisterType<ISettingsService>(
                       c => new CloudSettingsService(
                           c.Resolve<ICache>(),
                           "Fragulizer",
                           new AzureBlob(toolsSettingAccount, xmlConfigurationBlobContainer)
                       )
                   );

                    Services.RegisterType<IInstrumentGroupSettingService>(
                       c => new CloudInstrumentGroupSettingService(
                           c.Resolve<ICache>(),
                           "Fragulizer",
                           new AzureBlob(toolsSettingAccount, xmlConfigurationBlobContainer)
                       )
                    );

                    //Euroland.Azure.AzureBlockBlob configBlob = new Euroland.Azure.AzureBlockBlob(toolsSettingAccount, xmlConfigurationBlobContainer);

                    //XmlConfigProvider.Initialize(new ToolsFramework.Cache.HttpRuntimeCacheResolver("Fragulizer"), configBlob, true);
                }
                else
                {
                    Services.RegisterType<IUrlResolver>(c => new ResoureUrlResolver());
                    string genPath = HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["GeneralSettingsPath"];
                    string toolPath = HttpContext.Current.Server.MapPath("~/Config/");
                    Services.RegisterType<ISettingsService>(c => new SettingsService(c.Resolve<ICache>(), "Fragulizer", genPath, toolPath));

                    Services.RegisterType<IInstrumentGroupSettingService>(c => new InstrumentGroupSettingService(c.Resolve<ICache>(), "Fragulizer", genPath, toolPath));
                    //XmlConfigProvider.Initialize(new ToolsFramework.Cache.HttpRuntimeCacheResolver("Fragulizer"));
                }


                Services.RegisterType<IFragulizerRepository>(c => new FragulizerRepository(
                    c.Resolve<IDatabase>(),
                    c.Resolve<ICache>())
                    );

                ClientResourceRegistrationExtension.Initialize(
                    () => Services.Get<IUrlResolver>(),
                    () => AzureConfiguration.GetConfigurationSetting("EnableCssJsDebuging", "False").ToLower() == "true"
                );
            });*/
            #endregion
        }
    }
}