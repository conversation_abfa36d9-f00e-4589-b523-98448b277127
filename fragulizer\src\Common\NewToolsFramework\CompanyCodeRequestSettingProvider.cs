﻿using Euroland.NetCore.ToolsFramework.Setting;
using Euroland.NetCore.ToolsFramework.Setting.Abstractions;
using System;

namespace Fragulizer.Common.NewToolsFramework
{
    public class CompanyCodeRequestSettingProvider : IRequestSettingFinder
    {
        // Backing fields
        private string _companyCodeQueryStringKey = "companycode";
        private string _versionQueryStringKey = "v";
        private string _languageQueryStringKey = "lang";

        //
        // Summary:
        //     Gets or sets key that contains the company code value
        public string CompanyCodeQueryStringKey
        {
            get { return _companyCodeQueryStringKey; }
            set { _companyCodeQueryStringKey = value; }
        }

        //
        // Summary:
        //     Gets or sets key that contains the version of setting. If version is specified,
        //     the System.IServiceProvider will use version as a main setting
        public string VersionQueryStringKey
        {
            get { return _versionQueryStringKey; }
            set { _versionQueryStringKey = value; }
        }

        //
        // Summary:
        //     Gets or sets key that contains the current language
        public string LanguageQueryStringKey
        {
            get { return _languageQueryStringKey; }
            set { _languageQueryStringKey = value; }
        }

        //
        // Summary:
        //     Gets a value of Euroland.NetCore.ToolsFramework.Setting.SettingResourceResult
        //
        // Returns:
        //     The Euroland.NetCore.ToolsFramework.Setting.SettingResourceResult
        public SettingResourceResult DetermineProviderSettingResourceResult()
        {
            var request = System.Web.HttpContext.Current.Request;

            if (!request.QueryString.HasKeys())
            {
                return null;
            }

            string companyCode = null;
            string version = null;
            string language = null;
            
            if (!string.IsNullOrWhiteSpace(CompanyCodeQueryStringKey))
            {
                companyCode = request.QueryString[CompanyCodeQueryStringKey];
            }

            if (!string.IsNullOrWhiteSpace(VersionQueryStringKey))
            {
                version = request.QueryString[VersionQueryStringKey];
            }

            if (!string.IsNullOrWhiteSpace(LanguageQueryStringKey))
            {
                language = request.QueryString[LanguageQueryStringKey];
            }

            if (string.IsNullOrEmpty(companyCode) && IsPOST(request))
            {
                companyCode = request.Form["CompanyCodeQueryStringKey"];
            }

            if (string.IsNullOrEmpty(version) && IsPOST(request))
            {
                version = request.Form["VersionQueryStringKey"];
            }

            if (!string.IsNullOrWhiteSpace(companyCode))
            {
                return new SettingResourceResult
                {
                    CompanyCode = companyCode,
                    Version = version,
                    Language = (string.IsNullOrWhiteSpace(language) ? "en-GB" : language)
                };
            }

            return null;
        }

        private bool IsPOST(System.Web.HttpRequest request)
        {
            return string.Equals(request.HttpMethod, "POST", StringComparison.InvariantCultureIgnoreCase);
        }
    }
}
