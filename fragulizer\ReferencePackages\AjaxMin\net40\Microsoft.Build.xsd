<?xml version="1.0" encoding="utf-8"?>
<xs:schema targetNamespace="http://schemas.microsoft.com/developer/msbuild/2003" 
xmlns:xs="http://www.w3.org/2001/XMLSchema"
xmlns:msb="http://schemas.microsoft.com/developer/msbuild/2003"
elementFormDefault="qualified">

    <!-- =================== IMPORT COMMON SCHEMA =========================== -->
    <xs:include schemaLocation="MSBuild\Microsoft.Build.CommonTypes.xsd"/>

    <!-- ========= ADD CUSTOM ITEMS, PROPERTIES, AND TASKS BELOW ======= -->
    <!-- Note that these will be in the msbuild namespace. A future version of
        msbuild may require that custom itemtypes, properties, and tasks be in a 
        custom namespace, but currently msbuild only supports the msbuild namespace. -->
    
    <!-- example custom itemtype with particular meta-data required-->
    <!--<xs:element name="MyItem" substitutionGroup="msb:Item">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="msb:SimpleItemType">
                    <xs:sequence maxOccurs="1">
                        <xs:choice>
                            <xs:element name="MyMetaData" type="xs:string"/>
                        </xs:choice>
                    </xs:sequence>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>-->
    
    <!-- Example custom itemtype with NO meta-data -->
    <!--<xs:element name="MySimpleItem" type="msb:SimpleItemType" substitutionGroup="msb:Item"/>-->
    
    <!-- Example custom itemtype with ANY meta-data -->
    <!--<xs:element name="MyFlexibleItem" type="msb:GenericItemType" substitutionGroup="msb:Item"/>-->

    <!-- example custom property that allows string content only-->
    <!--<xs:element name="MySimpleProperty" type="msb:StringPropertyType" substitutionGroup="msb:Property"/>-->

    <!-- example custom task with single required parameter-->
    <!--<xs:element name="MyTask" substitutionGroup="msb:Task">
        <xs:complexType>
            <xs:complexContent>
                <xs:extension base="msb:TaskType">
                    <xs:attribute name="MyParameter" type="xs:boolean" use="required"/>
                </xs:extension>
            </xs:complexContent>
        </xs:complexType>
    </xs:element>-->
    
</xs:schema>
