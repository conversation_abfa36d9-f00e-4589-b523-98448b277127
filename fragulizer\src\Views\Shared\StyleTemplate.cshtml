﻿@using Fragulizer.Common
@{
    Layout = null;
}
<style type="text/css">
    .increase-value
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.sharepricevalueincreasecolor))
        {
            @:color: @Tool.Settings.sharepricevalueincreasecolor;
        }
    }

    .decrease-value
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.sharepricevaluedecreasecolor))
        {
            @:color: @Tool.Settings.sharepricevaluedecreasecolor;
        }
    }

    .page-font-style
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.maintextfont))
        {
            @:font-family: @Tool.Settings.maintextfont !important;
        }
        @if (!String.IsNullOrEmpty(Tool.Settings.maintextfontsize))
        {
            @:font-size: @Tool.Settings.maintextfontsize !important;
        }
        @if (!String.IsNullOrEmpty(Tool.Settings.maintextcolor))
        {
            @:color: @Tool.Settings.maintextcolor !important;
        }
    }

    .main-heading
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.mainheadingfont))
        {
            @:font-family:@Tool.Settings.mainheadingfont !important;
        }
        @if (!String.IsNullOrEmpty(Tool.Settings.mainheadingfontsize))
        {
            @:font-size: @Tool.Settings.mainheadingfontsize !important;
        }
        @if (!String.IsNullOrEmpty(Tool.Settings.mainheadingcolor))
        {
            @:color: @Tool.Settings.mainheadingcolor !important;
        }
    }

    .second-heading
    {
         @if (!String.IsNullOrEmpty(Tool.Settings.secondheadingfont))
         {
            @:font-family:@Tool.Settings.secondheadingfont !important;
        }
        @if (!String.IsNullOrEmpty(Tool.Settings.secondheadingfont))
        {
            @:font-size: @Tool.Settings.secondheadingfontsize !important;
        }
        @if (!String.IsNullOrEmpty(Tool.Settings.secondheadingcolor))
        {
            @:color: @Tool.Settings.secondheadingcolor !important;
        }
    }

    .table-header
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.tableheadfont))
        {
            @:font-family: @Tool.Settings.tableheadfont !important;
        }
        @if (!String.IsNullOrEmpty(Tool.Settings.tableheadfontsize))
        {
            @:font-size: @Tool.Settings.tableheadfontsize !important;
        }
        @if (!String.IsNullOrEmpty(Tool.Settings.tableheadcolor))
        {
            @:color: @Tool.Settings.tableheadcolor !important;
        }
        @if (!String.IsNullOrEmpty(Tool.Settings.tableheadbackgroundcolor))
        {
            @:background-color: @Tool.Settings.tableheadbackgroundcolor !important;
        }
        @if (Tool.Settings.tableoutlineborderenable)
        {
            @:border:none !important;
            @:border-bottom-style:solid !important;
            @:border-bottom-width:1px !important;
            @:border-color: @Tool.Settings.tableoutlinebordercolor !important;
        }
    }

    .border-outline
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.tableoutlinebordercolor))
        {
            @:border-color: @Tool.Settings.tableoutlinebordercolor !important;
        }
        @if (Tool.Settings.tableoutlineborderenable)
        {
            @:border-width: 1px !important;
            @:border-style: solid !important;
        }
    }

    .border-inline
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.tableinlinebordercolor))
        {
            @:border-bottom-color: @Tool.Settings.tableinlinebordercolor !important;
        }
        @if (Tool.Settings.tableinlineborderenable)
        {
            @:border-bottom-width: 1px !important;
            @:border-bottom-style: solid !important;
        }
    }

    .vertical-line
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.tableinlinebordercolor))
        {
            @:border-right-color: @Tool.Settings.tableinlinebordercolor !important;
        }
    }

    .table-share-row-odd
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.tableoddrowcolor))
        {
            @:background-color: @Tool.Settings.tableoddrowcolor !important;
        }
    }

    .table-share-row-even
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.tableevenrowcolor))
        {
            @:background-color: @Tool.Settings.tableevenrowcolor !important;
        }
    }

    .row-header
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.tableheadcolor))
        {
            @:color: @Tool.Settings.tableheadcolor !important;
        }
        @if (!String.IsNullOrEmpty(Tool.Settings.tableheadfont))
        {
            @:font-family: @Tool.Settings.tableheadfont !important;
        }
        @if (!String.IsNullOrEmpty(Tool.Settings.tableheadfontsize))
        {
            @:font-size: @Tool.Settings.tableheadfontsize !important;
        }
    }

    .hyperlink, .disclaimer-box a, .cookies-box a
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.linkcolor))
        {
            @:color: @Tool.Settings.linkcolor !important;
        }
        @if (Tool.Settings.linkunderline.Trim()!=string.Empty)
        {
            if (Tool.Settings.linkunderline.ToLower() == bool.TrueString.ToLower())
            {
                @:text-decoration:underline !important;
            }
            else
            {
                @:text-decoration:none !important;
            }
        }
    }

    .hyperlink:hover, .disclaimer-box a:hover, .cookies-box a:hover
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.linkhovercolor))
        {
            @:color:@Tool.Settings.linkhovercolor !important;
        }
    }

    input.textbox-base 
    {
        @if (!String.IsNullOrEmpty(Tool.Settings.maintextcolor))
        {
            @:color: @Tool.Settings.maintextcolor;
        }
    }
</style>
