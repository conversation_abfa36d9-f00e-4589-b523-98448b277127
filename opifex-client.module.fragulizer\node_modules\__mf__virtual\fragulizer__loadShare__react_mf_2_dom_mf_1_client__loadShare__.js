
    
    ;() => import("__mf__virtual/fragulizer__prebuild__react_mf_2_dom_mf_1_client__prebuild__.js").catch(() => {});
    // dev uses dynamic import to separate chunks
    ;() => import("react-dom/client").catch(() => {});
    const {loadShare} = require("@module-federation/runtime")
    const {initPromise} = require("__mf__virtual/fragulizer__mf_v__runtimeInit__mf_v__.js")
    const res = initPromise.then(_ => loadShare("react-dom/client", {
    customShareInfo: {shareConfig:{
      singleton: true,
      strictVersion: false,
      requiredVersion: "^19.1.0"
    }}}))
    const exportModule = /*mf top-level-await placeholder replacement mf*/res.then(factory => factory())
    module.exports = exportModule
  