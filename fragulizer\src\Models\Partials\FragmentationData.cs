﻿using System;
using System.Collections.Generic;

namespace Fragulizer.Models.Partials
{
    /// <summary>
    /// mapping data return from database
    /// </summary>
    public class FragmentationData
    {
        private DateTime _Time;
        public int MarketID { get; set; }
        public string MarketName { get; set; }
        public int MarketTranslationID { get; set; }
        public DateTime Time
        {
            get
            {
                return _Time;
            }
            set
            {
                _Time = DateTime.SpecifyKind(value, DateTimeKind.Unspecified);
            }
        }
        public long Volume { get; set; }
    }

    /// <summary>
    /// use as json object
    /// </summary>
    public class Fragmentation
    {
        public int MarketID { get; set; }
        public string MarketName { get; set; }
        public int MarketTranslationID { get; set; }
        public List<DataItem> Data { get; set; }
    }

    public class DataItem
    {
        private DateTime _Time;
        public DateTime Time
        {
            get
            {
                return _Time;
            }
            set
            {
                _Time = DateTime.SpecifyKind(value, DateTimeKind.Unspecified);
            }
        }
        public long Volume { get; set; }
    }

    public class LiveData
    {
        public List<Fragmentation> FragmentationData { get; set; }
        public List<MarketShareData> MarketShareData { get; set; }
    }
}