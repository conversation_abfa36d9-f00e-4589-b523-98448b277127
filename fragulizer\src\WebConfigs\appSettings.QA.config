<appSettings xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">

	<add key="tools:Version" value="ground"  xdt:Locator="Match(key)" xdt:Transform="SetAttributes"/>
	<add key="GeneralSettingsPath" value="../Config/" xdt:Locator="Match(key)" xdt:Transform="SetAttributes" />
	<add key="EnableCssJsDebuging" value="False" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />

	<add key="Opifex.PreviewEnabled" value="true" xdt:Locator="Match(key)" xdt:Transform="Insert" />
	<!--XML Config-->
	<add key="Opifex.ToolGeneralXmlPath" value="..\Opifex2-API\wwwroot\Config" xdt:Locator="Match(key)" xdt:Transform="Insert"  />
	<add key="Opifex.ToolCompanyXmlPath" value="..\Opifex2-API\wwwroot\IC\Config\Company" xdt:Locator="Match(key)" xdt:Transform="Insert"  />
	<!--CSS config-->
	<add key="Opifex.ToolGeneralStyleSheetPath" value="https://dev.vn.euroland.com/tools/opifex2/Config/" xdt:Locator="Match(key)" xdt:Transform="Insert" />
	<add key="Opifex.ToolCompanyStyleSheetPath" value="https://dev.vn.euroland.com/tools/opifex2/IC/Config/" xdt:Locator="Match(key)" xdt:Transform="Insert" />

</appSettings>