<appSettings xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <add key="tools:Version" value="ground"  xdt:Locator="Match(key)" xdt:Transform="SetAttributes"/>
  <add key="GeneralSettingsPath" value="../Config/" xdt:Locator="Match(key)" xdt:Transform="SetAttributes" />
  <add key="EnableCssJsDebuging" value="False" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />

	<add key="Opifex.PreviewEnabled" value="false" />
	<!--XML Config-->
	<add key="Opifex.ToolGeneralXmlPath" value="..\Opifex2-API\wwwroot\Config" />
	<add key="Opifex.ToolCompanyXmlPath" value="..\Opifex2-API\wwwroot\IC\Config\Company" />
	<!--CSS config-->
	<add key="Opifex.ToolGeneralStyleSheetPath" value="https://gamma.euroland.com/tools/opifex2/Config/" />
	<add key="Opifex.ToolCompanyStyleSheetPath" value="https://gamma.euroland.com/tools/opifex2/IC/Config/" />

</appSettings>