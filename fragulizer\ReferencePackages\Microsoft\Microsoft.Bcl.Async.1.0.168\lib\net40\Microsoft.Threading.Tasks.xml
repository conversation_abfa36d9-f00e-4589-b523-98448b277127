<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Threading.Tasks</name>
    </assembly>
    <members>
        <member name="T:AwaitExtensions">
            <summary>
                Provides extension methods for threading-related types.
            </summary>
        </member>
        <member name="M:AwaitExtensions.CancelAfter(System.Threading.CancellationTokenSource,System.Int32)">
            <summary>Cancels the <see cref="T:System.Threading.CancellationTokenSource"/> after the specified duration.</summary>
            <param name="source">The CancellationTokenSource.</param>
            <param name="dueTime">The due time in milliseconds for the source to be canceled.</param>
        </member>
        <member name="M:AwaitExtensions.CancelAfter(System.Threading.CancellationTokenSource,System.TimeSpan)">
            <summary>Cancels the <see cref="T:System.Threading.CancellationTokenSource"/> after the specified duration.</summary>
            <param name="source">The CancellationTokenSource.</param>
            <param name="dueTime">The due time for the source to be canceled.</param>
        </member>
        <member name="M:AwaitExtensions.GetAwaiter(System.Threading.Tasks.Task)">
            <summary>Gets an awaiter used to await this <see cref="T:System.Threading.Tasks.Task"/>.</summary>
            <param name="task">The task to await.</param>
            <returns>An awaiter instance.</returns>
        </member>
        <member name="M:AwaitExtensions.GetAwaiter``1(System.Threading.Tasks.Task{``0})">
            <summary>Gets an awaiter used to await this <see cref="T:System.Threading.Tasks.Task"/>.</summary>
            <typeparam name="TResult">Specifies the type of data returned by the task.</typeparam>
            <param name="task">The task to await.</param>
            <returns>An awaiter instance.</returns>
        </member>
        <member name="M:AwaitExtensions.ConfigureAwait(System.Threading.Tasks.Task,System.Boolean)">
            <summary>Creates and configures an awaitable object for awaiting the specified task.</summary>
            <param name="task">The task to be awaited.</param>
            <param name="continueOnCapturedContext">
            true to automatic marshag back to the original call site's current SynchronizationContext
            or TaskScheduler; otherwise, false.
            </param>
            <returns>The instance to be awaited.</returns>
        </member>
        <member name="M:AwaitExtensions.ConfigureAwait``1(System.Threading.Tasks.Task{``0},System.Boolean)">
            <summary>Creates and configures an awaitable object for awaiting the specified task.</summary>
            <param name="task">The task to be awaited.</param>
            <param name="continueOnCapturedContext">
            true to automatic marshag back to the original call site's current SynchronizationContext
            or TaskScheduler; otherwise, false.
            </param>
            <returns>The instance to be awaited.</returns>
        </member>
        <member name="T:Microsoft.ProgressEventHandler`1">
            <summary>Event handler for progress reports.</summary>
            <typeparam name="T">Specifies the type of data for the progress report.</typeparam>
            <param name="sender">The sender of the report.</param>
            <param name="value">The reported value.</param>
        </member>
        <member name="T:Microsoft.Progress`1">
            <summary>
            Provides an IProgress{T} that invokes callbacks for each reported progress value.
            </summary>
            <typeparam name="T">Specifies the type of the progress report value.</typeparam>
            <remarks>
            Any handler provided to the constructor or event handlers registered with
            the <see cref="E:Microsoft.Progress`1.ProgressChanged"/> event are invoked through a 
            <see cref="T:System.Threading.SynchronizationContext"/> instance captured
            when the instance is constructed.  If there is no current SynchronizationContext
            at the time of construction, the callbacks will be invoked on the ThreadPool.
            </remarks>
        </member>
        <member name="F:Microsoft.Progress`1.m_synchronizationContext">
            <summary>The synchronization context captured upon construction.  This will never be null.</summary>
        </member>
        <member name="F:Microsoft.Progress`1.m_handler">
            <summary>The handler specified to the constructor.  This may be null.</summary>
        </member>
        <member name="F:Microsoft.Progress`1.m_invokeHandlers">
            <summary>A cached delegate used to post invocation to the synchronization context.</summary>
        </member>
        <member name="M:Microsoft.Progress`1.#ctor">
            <summary>Initializes the <see cref="T:Microsoft.Progress`1"/>.</summary>
        </member>
        <member name="M:Microsoft.Progress`1.#ctor(System.Action{`0})">
            <summary>Initializes the <see cref="T:Microsoft.Progress`1"/> with the specified callback.</summary>
            <param name="handler">
            A handler to invoke for each reported progress value.  This handler will be invoked
            in addition to any delegates registered with the <see cref="E:Microsoft.Progress`1.ProgressChanged"/> event.
            </param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="handler"/> is null (Nothing in Visual Basic).</exception>
        </member>
        <member name="M:Microsoft.Progress`1.OnReport(`0)">
            <summary>Reports a progress change.</summary>
            <param name="value">The value of the updated progress.</param>
        </member>
        <member name="M:Microsoft.Progress`1.System#IProgress{T}#Report(`0)">
            <summary>Reports a progress change.</summary>
            <param name="value">The value of the updated progress.</param>
        </member>
        <member name="M:Microsoft.Progress`1.InvokeHandlers(System.Object)">
            <summary>Invokes the action and event callbacks.</summary>
            <param name="state">The progress value.</param>
        </member>
        <member name="E:Microsoft.Progress`1.ProgressChanged">
            <summary>Raised for each reported progress value.</summary>
            <remarks>
            Handlers registered with this event will be invoked on the 
            <see cref="T:System.Threading.SynchronizationContext"/> captured when the instance was constructed.
            </remarks>
        </member>
        <member name="T:Microsoft.ProgressStatics">
            <summary>Holds static values for <see cref="T:Microsoft.Progress`1"/>.</summary>
            <remarks>This avoids one static instance per type T.</remarks>
        </member>
        <member name="F:Microsoft.ProgressStatics.DefaultContext">
            <summary>A default synchronization context that targets the ThreadPool.</summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.AsyncServices.ThrowAsync(System.Exception,System.Threading.SynchronizationContext)">
            <summary>Throws the exception on the ThreadPool.</summary>
            <param name="exception">The exception to propagate.</param>
            <param name="targetContext">The target context on which to propagate the exception.  Null to use the ThreadPool.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.AsyncServices.PrepareExceptionForRethrow(System.Exception)">
            <summary>Copies the exception's stack trace so its stack trace isn't overwritten.</summary>
            <param name="exc">The exception to prepare.</param>
        </member>
        <member name="T:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable">
            <summary>Provides an awaitable object that allows for configured awaits on <see cref="T:System.Threading.Tasks.Task"/>.</summary>
            <remarks>This type is intended for compiler use only.</remarks>
        </member>
        <member name="F:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable.m_configuredTaskAwaiter">
            <summary>The task being awaited.</summary>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable.#ctor(System.Threading.Tasks.Task,System.Boolean)">
            <summary>Initializes the <see cref="T:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable"/>.</summary>
            <param name="task">The awaitable <see cref="T:System.Threading.Tasks.Task"/>.</param>
            <param name="continueOnCapturedContext">
            true to attempt to marshal the continuation back to the original context captured; otherwise, false.
            </param>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable.GetAwaiter">
            <summary>Gets an awaiter for this awaitable.</summary>
            <returns>The awaiter.</returns>
        </member>
        <member name="T:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter">
            <summary>Provides an awaiter for a <see cref="T:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable"/>.</summary>
            <remarks>This type is intended for compiler use only.</remarks>
        </member>
        <member name="F:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.m_task">
            <summary>The task being awaited.</summary>
        </member>
        <member name="F:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.m_continueOnCapturedContext">
            <summary>Whether to attempt marshaling back to the original context.</summary>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.#ctor(System.Threading.Tasks.Task,System.Boolean)">
            <summary>Initializes the <see cref="T:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter"/>.</summary>
            <param name="task">The <see cref="T:System.Threading.Tasks.Task"/> to await.</param>
            <param name="continueOnCapturedContext">
            true to attempt to marshal the continuation back to the original context captured
            when BeginAwait is called; otherwise, false.
            </param>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
            <summary>Schedules the continuation onto the <see cref="T:System.Threading.Tasks.Task"/> associated with this <see cref="T:Microsoft.Runtime.CompilerServices.TaskAwaiter"/>.</summary>
            <param name="continuation">The action to invoke when the await operation completes.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="continuation"/> argument is null (Nothing in Visual Basic).</exception>
            <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
            <remarks>This method is intended for compiler user rather than use directly in code.</remarks>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
            <summary>Schedules the continuation onto the <see cref="T:System.Threading.Tasks.Task"/> associated with this <see cref="T:Microsoft.Runtime.CompilerServices.TaskAwaiter"/>.</summary>
            <param name="continuation">The action to invoke when the await operation completes.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="continuation"/> argument is null (Nothing in Visual Basic).</exception>
            <exception cref="T:System.InvalidOperationException">The awaiter was not properly initialized.</exception>
            <remarks>This method is intended for compiler user rather than use directly in code.</remarks>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.GetResult">
            <summary>Ends the await on the completed <see cref="T:System.Threading.Tasks.Task"/>.</summary>
            <returns>The result of the completed <see cref="T:System.Threading.Tasks.Task`1"/>.</returns>
            <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
            <exception cref="T:System.InvalidOperationException">The task was not yet completed.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task was canceled.</exception>
            <exception cref="T:System.Exception">The task completed in a Faulted state.</exception>
        </member>
        <member name="P:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter.IsCompleted">
            <summary>Gets whether the task being awaited is completed.</summary>
            <remarks>This property is intended for compiler user rather than use directly in code.</remarks>
            <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
        </member>
        <member name="T:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1">
            <summary>Provides an awaitable object that allows for configured awaits on <see cref="T:System.Threading.Tasks.Task`1"/>.</summary>
            <remarks>This type is intended for compiler use only.</remarks>
        </member>
        <member name="F:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.m_configuredTaskAwaiter">
            <summary>The underlying awaitable on whose logic this awaitable relies.</summary>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.#ctor(System.Threading.Tasks.Task{`0},System.Boolean)">
            <summary>Initializes the <see cref="T:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1"/>.</summary>
            <param name="task">The awaitable <see cref="T:System.Threading.Tasks.Task`1"/>.</param>
            <param name="continueOnCapturedContext">
            true to attempt to marshal the continuation back to the original context captured; otherwise, false.
            </param>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.GetAwaiter">
            <summary>Gets an awaiter for this awaitable.</summary>
            <returns>The awaiter.</returns>
        </member>
        <member name="T:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter">
            <summary>Provides an awaiter for a <see cref="T:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1"/>.</summary>
            <remarks>This type is intended for compiler use only.</remarks>
        </member>
        <member name="F:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.m_task">
            <summary>The task being awaited.</summary>
        </member>
        <member name="F:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.m_continueOnCapturedContext">
            <summary>Whether to attempt marshaling back to the original context.</summary>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.#ctor(System.Threading.Tasks.Task{`0},System.Boolean)">
            <summary>Initializes the <see cref="T:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter"/>.</summary>
            <param name="task">The awaitable <see cref="T:System.Threading.Tasks.Task`1"/>.</param>
            <param name="continueOnCapturedContext">
            true to attempt to marshal the continuation back to the original context captured; otherwise, false.
            </param>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.OnCompleted(System.Action)">
            <summary>Schedules the continuation onto the <see cref="T:System.Threading.Tasks.Task"/> associated with this <see cref="T:Microsoft.Runtime.CompilerServices.TaskAwaiter"/>.</summary>
            <param name="continuation">The action to invoke when the await operation completes.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="continuation"/> argument is null (Nothing in Visual Basic).</exception>
            <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
            <remarks>This method is intended for compiler user rather than use directly in code.</remarks>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.UnsafeOnCompleted(System.Action)">
            <summary>Schedules the continuation onto the <see cref="T:System.Threading.Tasks.Task"/> associated with this <see cref="T:Microsoft.Runtime.CompilerServices.TaskAwaiter"/>.</summary>
            <param name="continuation">The action to invoke when the await operation completes.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="continuation"/> argument is null (Nothing in Visual Basic).</exception>
            <exception cref="T:System.InvalidOperationException">The awaiter was not properly initialized.</exception>
            <remarks>This method is intended for compiler user rather than use directly in code.</remarks>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.GetResult">
            <summary>Ends the await on the completed <see cref="T:System.Threading.Tasks.Task`1"/>.</summary>
            <returns>The result of the completed <see cref="T:System.Threading.Tasks.Task`1"/>.</returns>
            <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
            <exception cref="T:System.InvalidOperationException">The task was not yet completed.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task was canceled.</exception>
            <exception cref="T:System.Exception">The task completed in a Faulted state.</exception>
        </member>
        <member name="P:Microsoft.Runtime.CompilerServices.ConfiguredTaskAwaitable`1.ConfiguredTaskAwaiter.IsCompleted">
            <summary>Gets whether the task being awaited is completed.</summary>
            <remarks>This property is intended for compiler user rather than use directly in code.</remarks>
            <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
        </member>
        <member name="T:Microsoft.Runtime.CompilerServices.TaskAwaiter">
            <summary>Provides an awaiter for awaiting a <see cref="T:System.Threading.Tasks.Task"/>.</summary>
            <remarks>This type is intended for compiler use only.</remarks>
        </member>
        <member name="F:Microsoft.Runtime.CompilerServices.TaskAwaiter.CONTINUE_ON_CAPTURED_CONTEXT_DEFAULT">
            <summary>The default value to use for continueOnCapturedContext.</summary>
        </member>
        <member name="F:Microsoft.Runtime.CompilerServices.TaskAwaiter.InvalidOperationException_TaskNotCompleted">
            <summary>Error message for GetAwaiter.</summary>
        </member>
        <member name="F:Microsoft.Runtime.CompilerServices.TaskAwaiter.m_task">
            <summary>The task being awaited.</summary>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter.#ctor(System.Threading.Tasks.Task)">
            <summary>Initializes the <see cref="T:Microsoft.Runtime.CompilerServices.TaskAwaiter"/>.</summary>
            <param name="task">The <see cref="T:System.Threading.Tasks.Task"/> to be awaited.</param>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter.OnCompleted(System.Action)">
            <summary>Schedules the continuation onto the <see cref="T:System.Threading.Tasks.Task"/> associated with this <see cref="T:Microsoft.Runtime.CompilerServices.TaskAwaiter"/>.</summary>
            <param name="continuation">The action to invoke when the await operation completes.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="continuation"/> argument is null (Nothing in Visual Basic).</exception>
            <exception cref="T:System.InvalidOperationException">The awaiter was not properly initialized.</exception>
            <remarks>This method is intended for compiler user rather than use directly in code.</remarks>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter.UnsafeOnCompleted(System.Action)">
            <summary>Schedules the continuation onto the <see cref="T:System.Threading.Tasks.Task"/> associated with this <see cref="T:Microsoft.Runtime.CompilerServices.TaskAwaiter"/>.</summary>
            <param name="continuation">The action to invoke when the await operation completes.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="continuation"/> argument is null (Nothing in Visual Basic).</exception>
            <exception cref="T:System.InvalidOperationException">The awaiter was not properly initialized.</exception>
            <remarks>This method is intended for compiler user rather than use directly in code.</remarks>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter.GetResult">
            <summary>Ends the await on the completed <see cref="T:System.Threading.Tasks.Task"/>.</summary>
            <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
            <exception cref="T:System.InvalidOperationException">The task was not yet completed.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task was canceled.</exception>
            <exception cref="T:System.Exception">The task completed in a Faulted state.</exception>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter.ValidateEnd(System.Threading.Tasks.Task)">
            <summary>
            Fast checks for the end of an await operation to determine whether more needs to be done
            prior to completing the await.
            </summary>
            <param name="task">The awaited task.</param>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccess(System.Threading.Tasks.Task)">
            <summary>Handles validations on tasks that aren't successfully completed.</summary>
            <param name="task">The awaited task.</param>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess(System.Threading.Tasks.Task)">
            <summary>Throws an exception to handle a task that completed in a state other than RanToCompletion.</summary>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter.OnCompletedInternal(System.Threading.Tasks.Task,System.Action,System.Boolean)">
            <summary>Schedules the continuation onto the <see cref="T:System.Threading.Tasks.Task"/> associated with this <see cref="T:Microsoft.Runtime.CompilerServices.TaskAwaiter"/>.</summary>
            <param name="task">The awaited task.</param>
            <param name="continuation">The action to invoke when the await operation completes.</param>
            <param name="continueOnCapturedContext">Whether to capture and marshal back to the current context.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="continuation"/> argument is null (Nothing in Visual Basic).</exception>
            <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
            <remarks>This method is intended for compiler user rather than use directly in code.</remarks>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter.RunNoException(System.Action)">
            <summary>Invokes the delegate in a try/catch that will propagate the exception asynchronously on the ThreadPool.</summary>
            <param name="continuation"></param>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter.PrepareExceptionForRethrow(System.Exception)">
            <summary>Copies the exception's stack trace so its stack trace isn't overwritten.</summary>
            <param name="exc">The exception to prepare.</param>
        </member>
        <member name="P:Microsoft.Runtime.CompilerServices.TaskAwaiter.IsCompleted">
            <summary>Gets whether the task being awaited is completed.</summary>
            <remarks>This property is intended for compiler user rather than use directly in code.</remarks>
            <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
        </member>
        <member name="P:Microsoft.Runtime.CompilerServices.TaskAwaiter.IsValidLocationForInlining">
            <summary>Whether the current thread is appropriate for inlining the await continuation.</summary>
        </member>
        <member name="T:Microsoft.Runtime.CompilerServices.TaskAwaiter`1">
            <summary>Provides an awaiter for awaiting a <see cref="T:System.Threading.Tasks.Task`1"/>.</summary>
            <remarks>This type is intended for compiler use only.</remarks>
        </member>
        <member name="F:Microsoft.Runtime.CompilerServices.TaskAwaiter`1.m_task">
            <summary>The task being awaited.</summary>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter`1.#ctor(System.Threading.Tasks.Task{`0})">
            <summary>Initializes the <see cref="T:Microsoft.Runtime.CompilerServices.TaskAwaiter`1"/>.</summary>
            <param name="task">The <see cref="T:System.Threading.Tasks.Task`1"/> to be awaited.</param>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter`1.OnCompleted(System.Action)">
            <summary>Schedules the continuation onto the <see cref="T:System.Threading.Tasks.Task"/> associated with this <see cref="T:Microsoft.Runtime.CompilerServices.TaskAwaiter"/>.</summary>
            <param name="continuation">The action to invoke when the await operation completes.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="continuation"/> argument is null (Nothing in Visual Basic).</exception>
            <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
            <remarks>This method is intended for compiler user rather than use directly in code.</remarks>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter`1.UnsafeOnCompleted(System.Action)">
            <summary>Schedules the continuation onto the <see cref="T:System.Threading.Tasks.Task"/> associated with this <see cref="T:Microsoft.Runtime.CompilerServices.TaskAwaiter"/>.</summary>
            <param name="continuation">The action to invoke when the await operation completes.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="continuation"/> argument is null (Nothing in Visual Basic).</exception>
            <exception cref="T:System.InvalidOperationException">The awaiter was not properly initialized.</exception>
            <remarks>This method is intended for compiler user rather than use directly in code.</remarks>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.TaskAwaiter`1.GetResult">
            <summary>Ends the await on the completed <see cref="T:System.Threading.Tasks.Task`1"/>.</summary>
            <returns>The result of the completed <see cref="T:System.Threading.Tasks.Task`1"/>.</returns>
            <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
            <exception cref="T:System.InvalidOperationException">The task was not yet completed.</exception>
            <exception cref="T:System.Threading.Tasks.TaskCanceledException">The task was canceled.</exception>
            <exception cref="T:System.Exception">The task completed in a Faulted state.</exception>
        </member>
        <member name="P:Microsoft.Runtime.CompilerServices.TaskAwaiter`1.IsCompleted">
            <summary>Gets whether the task being awaited is completed.</summary>
            <remarks>This property is intended for compiler user rather than use directly in code.</remarks>
            <exception cref="T:System.NullReferenceException">The awaiter was not properly initialized.</exception>
        </member>
        <member name="T:Microsoft.Runtime.CompilerServices.YieldAwaitable">
            <summary>Provides an awaitable context for switching into a target environment.</summary>
            <remarks>This type is intended for compiler use only.</remarks>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.YieldAwaitable.GetAwaiter">
            <summary>Gets an awaiter for this <see cref="T:Microsoft.Runtime.CompilerServices.YieldAwaitable"/>.</summary>
            <returns>An awaiter for this awaitable.</returns>
            <remarks>This method is intended for compiler user rather than use directly in code.</remarks>
        </member>
        <member name="T:Microsoft.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter">
            <summary>Provides an awaiter that switches into a target environment.</summary>
            <remarks>This type is intended for compiler use only.</remarks>
        </member>
        <member name="F:Microsoft.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.s_completed">
            <summary>A completed task.</summary>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.OnCompleted(System.Action)">
            <summary>Posts the <paramref name="continuation"/> back to the current context.</summary>
            <param name="continuation">The action to invoke asynchronously.</param>
            <exception cref="T:System.InvalidOperationException">The awaiter was not properly initialized.</exception>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.UnsafeOnCompleted(System.Action)">
            <summary>Posts the <paramref name="continuation"/> back to the current context.</summary>
            <param name="continuation">The action to invoke asynchronously.</param>
            <exception cref="T:System.InvalidOperationException">The awaiter was not properly initialized.</exception>
        </member>
        <member name="M:Microsoft.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.GetResult">
            <summary>Ends the await operation.</summary>
        </member>
        <member name="P:Microsoft.Runtime.CompilerServices.YieldAwaitable.YieldAwaiter.IsCompleted">
            <summary>Gets whether a yield is not required.</summary>
            <remarks>This property is intended for compiler user rather than use directly in code.</remarks>
        </member>
        <member name="T:System.Threading.Tasks.TaskEx">
            <summary>Provides methods for creating and manipulating tasks.</summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.Run(System.Action)">
            <summary>Creates a task that runs the specified action.</summary>
            <param name="action">The action to execute asynchronously.</param>
            <returns>A task that represents the completion of the action.</returns>
            <exception cref="T:System.ArgumentNullException">The <paramref name="action"/> argument is null.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.Run(System.Action,System.Threading.CancellationToken)">
            <summary>Creates a task that runs the specified action.</summary>
            <param name="action">The action to execute.</param>
            <param name="cancellationToken">The CancellationToken to use to request cancellation of this task.</param>
            <returns>A task that represents the completion of the action.</returns>
            <exception cref="T:System.ArgumentNullException">The <paramref name="action"/> argument is null.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.Run``1(System.Func{``0})">
            <summary>Creates a task that runs the specified function.</summary>
            <param name="function">The function to execute asynchronously.</param>
            <returns>A task that represents the completion of the action.</returns>
            <exception cref="T:System.ArgumentNullException">The <paramref name="function"/> argument is null.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.Run``1(System.Func{``0},System.Threading.CancellationToken)">
            <summary>Creates a task that runs the specified function.</summary>
            <param name="function">The action to execute.</param>
            <param name="cancellationToken">The CancellationToken to use to cancel the task.</param>
            <returns>A task that represents the completion of the action.</returns>
            <exception cref="T:System.ArgumentNullException">The <paramref name="function"/> argument is null.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.Run(System.Func{System.Threading.Tasks.Task})">
            <summary>Creates a task that runs the specified function.</summary>
            <param name="function">The action to execute asynchronously.</param>
            <returns>A task that represents the completion of the action.</returns>
            <exception cref="T:System.ArgumentNullException">The <paramref name="function"/> argument is null.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.Run(System.Func{System.Threading.Tasks.Task},System.Threading.CancellationToken)">
            <summary>Creates a task that runs the specified function.</summary>
            <param name="function">The function to execute.</param>
            <param name="cancellationToken">The CancellationToken to use to request cancellation of this task.</param>
            <returns>A task that represents the completion of the function.</returns>
            <exception cref="T:System.ArgumentNullException">The <paramref name="function"/> argument is null.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.Run``1(System.Func{System.Threading.Tasks.Task{``0}})">
            <summary>Creates a task that runs the specified function.</summary>
            <param name="function">The function to execute asynchronously.</param>
            <returns>A task that represents the completion of the action.</returns>
            <exception cref="T:System.ArgumentNullException">The <paramref name="function"/> argument is null.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.Run``1(System.Func{System.Threading.Tasks.Task{``0}},System.Threading.CancellationToken)">
            <summary>Creates a task that runs the specified function.</summary>
            <param name="function">The action to execute.</param>
            <param name="cancellationToken">The CancellationToken to use to cancel the task.</param>
            <returns>A task that represents the completion of the action.</returns>
            <exception cref="T:System.ArgumentNullException">The <paramref name="function"/> argument is null.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.Delay(System.Int32)">
            <summary>Starts a Task that will complete after the specified due time.</summary>
            <param name="dueTime">The delay in milliseconds before the returned task completes.</param>
            <returns>The timed Task.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            The <paramref name="dueTime"/> argument must be non-negative or -1 and less than or equal to Int32.MaxValue.
            </exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.Delay(System.TimeSpan)">
            <summary>Starts a Task that will complete after the specified due time.</summary>
            <param name="dueTime">The delay before the returned task completes.</param>
            <returns>The timed Task.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            The <paramref name="dueTime"/> argument must be non-negative or -1 and less than or equal to Int32.MaxValue.
            </exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.Delay(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>Starts a Task that will complete after the specified due time.</summary>
            <param name="dueTime">The delay before the returned task completes.</param>
            <param name="cancellationToken">A CancellationToken that may be used to cancel the task before the due time occurs.</param>
            <returns>The timed Task.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            The <paramref name="dueTime"/> argument must be non-negative or -1 and less than or equal to Int32.MaxValue.
            </exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.Delay(System.Int32,System.Threading.CancellationToken)">
            <summary>Starts a Task that will complete after the specified due time.</summary>
            <param name="dueTime">The delay in milliseconds before the returned task completes.</param>
            <param name="cancellationToken">A CancellationToken that may be used to cancel the task before the due time occurs.</param>
            <returns>The timed Task.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            The <paramref name="dueTime"/> argument must be non-negative or -1 and less than or equal to Int32.MaxValue.
            </exception>
        </member>
        <member name="F:System.Threading.Tasks.TaskEx.s_preCompletedTask">
            <summary>An already completed task.</summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.WhenAll(System.Threading.Tasks.Task[])">
            <summary>Creates a Task that will complete only when all of the provided collection of Tasks has completed.</summary>
            <param name="tasks">The Tasks to monitor for completion.</param>
            <returns>A Task that represents the completion of all of the provided tasks.</returns>
            <remarks>
            If any of the provided Tasks faults, the returned Task will also fault, and its Exception will contain information
            about all of the faulted tasks.  If no Tasks fault but one or more Tasks is canceled, the returned
            Task will also be canceled.
            </remarks>
            <exception cref="T:System.ArgumentNullException">The <paramref name="tasks"/> argument is null.</exception>
            <exception cref="T:System.ArgumentException">The <paramref name="tasks"/> argument contains a null reference.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.WhenAll``1(System.Threading.Tasks.Task{``0}[])">
            <summary>Creates a Task that will complete only when all of the provided collection of Tasks has completed.</summary>
            <param name="tasks">The Tasks to monitor for completion.</param>
            <returns>A Task that represents the completion of all of the provided tasks.</returns>
            <remarks>
            If any of the provided Tasks faults, the returned Task will also fault, and its Exception will contain information
            about all of the faulted tasks.  If no Tasks fault but one or more Tasks is canceled, the returned
            Task will also be canceled.
            </remarks>
            <exception cref="T:System.ArgumentNullException">The <paramref name="tasks"/> argument is null.</exception>
            <exception cref="T:System.ArgumentException">The <paramref name="tasks"/> argument contains a null reference.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.WhenAll(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
            <summary>Creates a Task that will complete only when all of the provided collection of Tasks has completed.</summary>
            <param name="tasks">The Tasks to monitor for completion.</param>
            <returns>A Task that represents the completion of all of the provided tasks.</returns>
            <remarks>
            If any of the provided Tasks faults, the returned Task will also fault, and its Exception will contain information
            about all of the faulted tasks.  If no Tasks fault but one or more Tasks is canceled, the returned
            Task will also be canceled.
            </remarks>
            <exception cref="T:System.ArgumentNullException">The <paramref name="tasks"/> argument is null.</exception>
            <exception cref="T:System.ArgumentException">The <paramref name="tasks"/> argument contains a null reference.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.WhenAll``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
            <summary>Creates a Task that will complete only when all of the provided collection of Tasks has completed.</summary>
            <param name="tasks">The Tasks to monitor for completion.</param>
            <returns>A Task that represents the completion of all of the provided tasks.</returns>
            <remarks>
            If any of the provided Tasks faults, the returned Task will also fault, and its Exception will contain information
            about all of the faulted tasks.  If no Tasks fault but one or more Tasks is canceled, the returned
            Task will also be canceled.
            </remarks>
            <exception cref="T:System.ArgumentNullException">The <paramref name="tasks"/> argument is null.</exception>
            <exception cref="T:System.ArgumentException">The <paramref name="tasks"/> argument contains a null reference.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.WhenAllCore``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task},System.Action{System.Threading.Tasks.Task[],System.Threading.Tasks.TaskCompletionSource{``0}})">
            <summary>Creates a Task that will complete only when all of the provided collection of Tasks has completed.</summary>
            <param name="tasks">The Tasks to monitor for completion.</param>
            <param name="setResultAction">
            A callback invoked when all of the tasks complete successfully in the RanToCompletion state.
            This callback is responsible for storing the results into the TaskCompletionSource.
            </param>
            <returns>A Task that represents the completion of all of the provided tasks.</returns>
            <exception cref="T:System.ArgumentNullException">The <paramref name="tasks"/> argument is null.</exception>
            <exception cref="T:System.ArgumentException">The <paramref name="tasks"/> argument contains a null reference.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.WhenAny(System.Threading.Tasks.Task[])">
            <summary>Creates a Task that will complete when any of the tasks in the provided collection completes.</summary>
            <param name="tasks">The Tasks to be monitored.</param>
            <returns>
            A Task that represents the completion of any of the provided Tasks.  The completed Task is this Task's result.
            </returns>
            <remarks>Any Tasks that fault will need to have their exceptions observed elsewhere.</remarks>
            <exception cref="T:System.ArgumentNullException">The <paramref name="tasks"/> argument is null.</exception>
            <exception cref="T:System.ArgumentException">The <paramref name="tasks"/> argument contains a null reference.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.WhenAny(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task})">
            <summary>Creates a Task that will complete when any of the tasks in the provided collection completes.</summary>
            <param name="tasks">The Tasks to be monitored.</param>
            <returns>
            A Task that represents the completion of any of the provided Tasks.  The completed Task is this Task's result.
            </returns>
            <remarks>Any Tasks that fault will need to have their exceptions observed elsewhere.</remarks>
            <exception cref="T:System.ArgumentNullException">The <paramref name="tasks"/> argument is null.</exception>
            <exception cref="T:System.ArgumentException">The <paramref name="tasks"/> argument contains a null reference.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.WhenAny``1(System.Threading.Tasks.Task{``0}[])">
            <summary>Creates a Task that will complete when any of the tasks in the provided collection completes.</summary>
            <param name="tasks">The Tasks to be monitored.</param>
            <returns>
            A Task that represents the completion of any of the provided Tasks.  The completed Task is this Task's result.
            </returns>
            <remarks>Any Tasks that fault will need to have their exceptions observed elsewhere.</remarks>
            <exception cref="T:System.ArgumentNullException">The <paramref name="tasks"/> argument is null.</exception>
            <exception cref="T:System.ArgumentException">The <paramref name="tasks"/> argument contains a null reference.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.WhenAny``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
            <summary>Creates a Task that will complete when any of the tasks in the provided collection completes.</summary>
            <param name="tasks">The Tasks to be monitored.</param>
            <returns>
            A Task that represents the completion of any of the provided Tasks.  The completed Task is this Task's result.
            </returns>
            <remarks>Any Tasks that fault will need to have their exceptions observed elsewhere.</remarks>
            <exception cref="T:System.ArgumentNullException">The <paramref name="tasks"/> argument is null.</exception>
            <exception cref="T:System.ArgumentException">The <paramref name="tasks"/> argument contains a null reference.</exception>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.FromResult``1(``0)">
            <summary>Creates an already completed <see cref="T:System.Threading.Tasks.Task`1"/> from the specified result.</summary>
            <param name="result">The result from which to create the completed task.</param>
            <returns>The completed task.</returns>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.Yield">
            <summary>Creates an awaitable that asynchronously yields back to the current context when awaited.</summary>
            <returns>
            A context that, when awaited, will asynchronously transition back into the current context.
            If SynchronizationContext.Current is non-null, that is treated as the current context.
            Otherwise, TaskScheduler.Current is treated as the current context.
            </returns>
        </member>
        <member name="M:System.Threading.Tasks.TaskEx.AddPotentiallyUnwrappedExceptions(System.Collections.Generic.List{System.Exception}@,System.Exception)">
            <summary>Adds the target exception to the list, initializing the list if it's null.</summary>
            <param name="targetList">The list to which to add the exception and initialize if the list is null.</param>
            <param name="exception">The exception to add, and unwrap if it's an aggregate.</param>
        </member>
        <member name="M:System.Threading.Tasks.TaskServices.FromCancellation(System.Threading.CancellationToken)">
            <summary>Returns a canceled task.</summary>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>The canceled task.</returns>
        </member>
        <member name="M:System.Threading.Tasks.TaskServices.FromCancellation``1(System.Threading.CancellationToken)">
            <summary>Returns a canceled task.</summary>
            <typeparam name="TResult">Specifies the type of the result.</typeparam>
            <param name="cancellationToken">The cancellation token.</param>
            <returns>The canceled task.</returns>
        </member>
        <member name="M:System.Threading.Tasks.TaskServices.HandleEapCompletion``1(System.Threading.Tasks.TaskCompletionSource{``0},System.Boolean,System.ComponentModel.AsyncCompletedEventArgs,System.Func{``0},System.Action)">
            <summary>
            Completes the Task if the user state matches the TaskCompletionSource.
            </summary>
            <typeparam name="T">Specifies the type of data returned by the Task.</typeparam>
            <param name="tcs">The TaskCompletionSource.</param>
            <param name="e">The completion event arguments.</param>
            <param name="requireMatch">Whether we require the tcs to match the e.UserState.</param>
            <param name="getResult">A function that gets the result with which to complete the task.</param>
            <param name="unregisterHandler">An action used to unregister work when the operaiton completes.</param>
        </member>
    </members>
</doc>
