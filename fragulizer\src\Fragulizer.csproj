﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\MSBuild.Microsoft.VisualStudio.Web.targets.********\build\MSBuild.Microsoft.VisualStudio.Web.targets.props" Condition="Exists('..\packages\MSBuild.Microsoft.VisualStudio.Web.targets.********\build\MSBuild.Microsoft.VisualStudio.Web.targets.props')" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <UsingTask AssemblyFile="..\ReferencePackages\AjaxMin\net40\AjaxMinTask.dll" TaskName="AjaxMin" />
  <UsingTask AssemblyFile="..\ReferencePackages\AjaxMin\net40\AjaxMinTask.dll" TaskName="AjaxMinBundleTask" />
  <UsingTask AssemblyFile="..\ReferencePackages\AjaxMin\net40\AjaxMinTask.dll" TaskName="AjaxMinManifestTask" />
  <UsingTask AssemblyFile="..\ReferencePackages\AjaxMin\net40\AjaxMinTask.dll" TaskName="AjaxMinManifestCleanTask" />
  <UsingTask TaskName="TransformXml" AssemblyFile="..\packages\MSBuild.Microsoft.VisualStudio.Web.targets.********\tools\VSToolsPath\Web\Microsoft.Web.Publishing.Tasks.dll" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{D2BC5003-4A18-4766-88F4-390AC2707C53}</ProjectGuid>
    <ProjectTypeGuids>{E53F8FEA-EAE0-44A6-8774-FFD645390401};{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Fragulizer</RootNamespace>
    <AssemblyName>Fragulizer</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <MvcProjectUpgradeChecked>true</MvcProjectUpgradeChecked>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>4.0</OldToolsVersion>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="itextsharp">
      <HintPath>..\ReferencePackages\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="netstandard" />
    <Reference Include="Euroland.Azure, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\ReferencePackages\ToolsSolutionReferences\Euroland.Azure.1.0.0.0\Euroland.Azure.dll</HintPath>
    </Reference>
    <Reference Include="Euroland.Azure.Shared, Version=1.2.1.5, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\ReferencePackages\ToolsSolutionReferences\Euroland.Azure.Shared\net40\Euroland.Azure.Shared.dll</HintPath>
    </Reference>
    <Reference Include="Euroland.NetCore.ToolsFramework.Setting, Version=3.0.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Euroland.NetCore.ToolsFramework.Setting.3.0.2\lib\netstandard2.0\Euroland.NetCore.ToolsFramework.Setting.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.2.0.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Abstractions, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Abstractions.2.0.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Features, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Features.2.0.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Features.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.WebUtilities, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.WebUtilities.2.0.0\lib\netstandard2.0\Microsoft.AspNetCore.WebUtilities.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.2.0.0\lib\netstandard2.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Abstractions, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Abstractions.2.0.0\lib\netstandard2.0\Microsoft.Extensions.FileProviders.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Physical, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Physical.2.0.0\lib\netstandard2.0\Microsoft.Extensions.FileProviders.Physical.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileSystemGlobbing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileSystemGlobbing.2.0.0\lib\netstandard2.0\Microsoft.Extensions.FileSystemGlobbing.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.ObjectPool, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.ObjectPool.2.0.0\lib\netstandard2.0\Microsoft.Extensions.ObjectPool.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.2.0.0\lib\netstandard2.0\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.2.0.0\lib\netstandard2.0\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Net.Http.Headers, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.Headers.2.0.0\lib\netstandard2.0\Microsoft.Net.Http.Headers.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.WindowsAzure.TransientFaultHandling">
      <HintPath>..\ReferencePackages\Microsoft.Practices.EnterpriseLibrary.WindowsAzure.TransientFaultHandling.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.TransientFaultHandling.Core">
      <HintPath>..\ReferencePackages\Microsoft.Practices.TransientFaultHandling.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <Private>True</Private>
      <HintPath>..\ReferencePackages\Microsoft\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Diagnostics, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\ReferencePackages\Microsoft.WindowsAzure.Diagnostics.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.ServiceRuntime">
      <HintPath>..\ReferencePackages\Microsoft.WindowsAzure.ServiceRuntime.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.StorageClient, Version=1.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\ReferencePackages\Microsoft.WindowsAzure.StorageClient.dll</HintPath>
    </Reference>
    <Reference Include="Munq.IocContainer">
      <HintPath>..\ReferencePackages\Munq.IocContainer.dll</HintPath>
    </Reference>
    <Reference Include="Munq.MVC3">
      <HintPath>..\ReferencePackages\Munq.MVC3.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.10.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI">
      <HintPath>..\ReferencePackages\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML">
      <HintPath>..\ReferencePackages\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net">
      <HintPath>..\ReferencePackages\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats">
      <HintPath>..\ReferencePackages\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="Svg">
      <HintPath>..\ReferencePackages\Svg.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.4.0\lib\netstandard2.0\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.4.0\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.4.4.0\lib\netstandard2.0\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Helpers, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\ReferencePackages\Microsoft\Microsoft.AspNet.WebPages.2.0.30506.0\lib\net40\System.Web.Helpers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\ReferencePackages\Microsoft\Microsoft.AspNet.Mvc.4.0.30506.0\lib\net40\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Providers">
      <HintPath>..\ReferencePackages\Microsoft\Microsoft.AspNet.Providers.Core.1.0\lib\net40\System.Web.Providers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Razor, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\ReferencePackages\Microsoft\Microsoft.AspNet.Razor.2.0.30506.0\lib\net40\System.Web.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\ReferencePackages\Microsoft\Microsoft.AspNet.WebPages.2.0.30506.0\lib\net40\System.Web.WebPages.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\ReferencePackages\Microsoft\Microsoft.AspNet.WebPages.2.0.30506.0\lib\net40\System.Web.WebPages.Deployment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\ReferencePackages\Microsoft\Microsoft.AspNet.WebPages.2.0.30506.0\lib\net40\System.Web.WebPages.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ToolsFramework, Version=1.6.1.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\ReferencePackages\ToolsSolutionReferences\ToolsFramework.1.6.1.0\NET40\ToolsFramework.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\Exporter.cs" />
    <Compile Include="Common\HttpContextExtensions.cs" />
    <Compile Include="Common\NewToolsFramework\CompanyCodeRequestSettingProvider.cs" />
    <Compile Include="Common\NewToolsFramework\SettingsServiceWithPreviewMode.cs" />
    <Compile Include="Common\NewToolsFramework\ToolCompanySettings2.cs" />
    <Compile Include="Common\RenderPartialViewExtension.cs" />
    <Compile Include="Common\RequestHelper.cs" />
    <Compile Include="Common\SpeedWatch.cs" />
    <Compile Include="Common\Translation\Translations.generated.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Translations.tt</DependentUpon>
    </Compile>
    <Compile Include="Common\UnityObject.cs" />
    <Compile Include="Common\Utility.cs" />
    <Compile Include="Common\WebsiteToImage.cs" />
    <Compile Include="Config\Settings.generated.cs">
      <DependentUpon>Settings.tt</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Controllers\ExportController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Models\FragulizerRepository.cs" />
    <Compile Include="Models\Partials\ActivityTrendData.cs" />
    <Compile Include="Models\Partials\FragmentationData.cs" />
    <Compile Include="Models\Partials\MarketInfo.cs" />
    <Compile Include="Models\Partials\MarketShareData.cs" />
    <Compile Include="Models\Partials\InstrumentInfo.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SettingService\InstrumentGroupSettingService.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Common\Translation\Translations.xml">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Config\Company\default.xml">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Config\Company\dk-cbg.xml">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Config\Company\s-nda\s-nda.xml" />
    <Content Include="Config\Company\sf-fum.xml" />
    <Content Include="Config\Company\sf-kon.xml" />
    <Content Include="Config\Company\sf-rtr.xml" />
    <Content Include="Config\Company\sf-wrt.xml" />
    <Content Include="Config\Fragulizer.xml">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Content\eucalendar.css" />
    <Content Include="Content\Images\18x18_38x38.gif" />
    <Content Include="Content\Images\18x18_38x38.png" />
    <Content Include="Content\Images\Calendar\cal.gif" />
    <Content Include="Content\Images\Calendar\img\cool-bg-hard-inv.png" />
    <Content Include="Content\Images\Calendar\img\cool-bg-hard.png" />
    <Content Include="Content\Images\Calendar\img\cool-bg-inv.png" />
    <Content Include="Content\Images\Calendar\img\cool-bg.png" />
    <Content Include="Content\Images\Calendar\img\drop-down.gif" />
    <Content Include="Content\Images\Calendar\img\drop-up.gif" />
    <Content Include="Content\Images\Calendar\img\nav-left-x2.gif" />
    <Content Include="Content\Images\Calendar\img\nav-left.gif" />
    <Content Include="Content\Images\Calendar\img\nav-right-x2.gif" />
    <Content Include="Content\Images\Calendar\img\nav-right.gif" />
    <Content Include="Content\Images\Calendar\img\time-down.png" />
    <Content Include="Content\Images\Calendar\img\time-up.png" />
    <Content Include="Content\Images\Chinesepdf.png" />
    <Content Include="Content\Images\ChinesePrint.png" />
    <Content Include="Content\Images\ChinesXLSs.png" />
    <Content Include="Content\Images\close.gif" />
    <Content Include="Content\Images\jpg.png" />
    <Content Include="Content\Images\loading.gif" />
    <Content Include="Content\Images\pdf.png" />
    <Content Include="Content\Images\print.png" />
    <Content Include="Content\Images\PrintTWS.png" />
    <Content Include="Content\Images\xls.png" />
    <Content Include="Content\tooltip\arrows.png" />
    <Content Include="Content\tooltip\etipstyle.css" />
    <Content Include="Default.aspx" />
    <Content Include="appsettings.config" />
    <Content Include="connections.config" />
    <None Include="Deployment\configuration.xml" />
    <Content Include="Global.asax" />
    <Content Include="Content\global.css" />
    <Content Include="Scripts\Fragulizer.js" />
    <Content Include="Scripts\JsExtentions.js" />
    <Content Include="Scripts\lib\eu.responsive.js" />
    <Content Include="Scripts\lib\eucalendar.js" />
    <Content Include="Scripts\lib\euroland-iframefix.js" />
    <Content Include="Scripts\lib\euroland.etools.js" />
    <Content Include="Scripts\lib\exporting.js" />
    <Content Include="Scripts\lib\highstock.js" />
    <Content Include="Scripts\lib\jquery.js" />
    <Content Include="Scripts\lib\respond.js" />
    <Content Include="Styles\default.css" />
    <Content Include="Styles\dk-cbg.css" />
    <Content Include="Styles\sf-fum.css" />
    <Content Include="Styles\sf-kon.css" />
    <Content Include="Styles\sf-kon\header_bgr.gif" />
    <Content Include="Styles\sf-rtr.css" />
    <Content Include="Styles\sf-wrt.css" />
    <Content Include="Styles\sf-wrt\heading.gif" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="WebConfigs\appSettings.Debug.config" />
    <Content Include="WebConfigs\appSettings.Gamma.config" />
    <Content Include="WebConfigs\appSettings.Production.config" />
    <Content Include="WebConfigs\appSettings.QA.config" />
    <Content Include="WebConfigs\connections.Alpha.config" />
    <Content Include="WebConfigs\connections.Debug.config" />
    <Content Include="WebConfigs\connections.Production.config" />
    <Content Include="WebConfigs\connections.QA.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <Content Include="Views\Home\Index.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Common\Translation\Translations.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>Translations.generated.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="Config\Settings.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>Settings.generated.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="Views\Home\ShareTypeSelection.cshtml" />
    <Content Include="Views\Shared\StyleTemplate.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="..\packages\MSBuild.Microsoft.VisualStudio.Web.targets.********\tools\VSToolsPath\WebApplications\Microsoft.WebApplication.targets" Condition="('$(VSToolsPath)' == '' Or !Exists('$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets')) And Exists('..\packages\MSBuild.Microsoft.VisualStudio.Web.targets.********\tools\VSToolsPath\WebApplications\Microsoft.WebApplication.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\MSBuild.Microsoft.VisualStudio.Web.targets.********\build\MSBuild.Microsoft.VisualStudio.Web.targets.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\MSBuild.Microsoft.VisualStudio.Web.targets.********\build\MSBuild.Microsoft.VisualStudio.Web.targets.props'))" />
  </Target>
  <Target Name="BuildAjaxMinManifests" AfterTargets="Build">
    <Message Text="Processing AjaxMin Manifests" Importance="high" />
    <CreateItem Include="@(None)" Condition="'%(Extension)'=='.ajaxmin'">
      <Output TaskParameter="Include" ItemName="AjaxMinManifests" />
    </CreateItem>
    <AjaxMinManifestTask ProjectDefaultSwitches="-define:$(DefineConstants))" Configuration="$(Configuration)" TreatWarningsAsErrors="false" InputFolder="$(ProjectDir)" OutputFolder="$(ProjectDir)Content\" Manifests="@(AjaxMinManifests)" />
  </Target>
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <Target Name="MinifyJsCssForPackage">
    <Message Text="Compressing JS and CSS..." Importance="high" />
    <ItemGroup>
      <JS Include="$(_PackageTempDir)\**\*.js" Exclude="$(_PackageTempDir)\**\*.min.js" />
      <CSS Include="$(_PackageTempDir)\**\*.css" Exclude="$(_PackageTempDir)\**\*.min.css" />
    </ItemGroup>
    <AjaxMin JsKnownGlobalNames="jQuery,$" JsSourceFiles="@(JS)" JsSourceExtensionPattern="\.js$" JsTargetExtension=".min.js" CssSourceFiles="@(CSS)" CssSourceExtensionPattern="\.css$" CssTargetExtension=".min.css" />
  </Target>
  <Target Name="TransformWebConfigForPackage">
    <Message Text="Transforming Web.config for environment=$(EnvironmentName)..." Importance="high" />
    <!--<TransformXml Source="$(ProjectDir)\Web.config" Transform="$(ProjectDir)\WebConfigs\Web.Release.config" Destination="$(_PackageTempDir)\Web.config" Condition="Exists('$(ProjectDir)\WebConfigs\Web.Release.config')" />-->
    <TransformXml Source="$(ProjectDir)\appSettings.config" Transform="$(ProjectDir)\WebConfigs\appSettings.$(EnvironmentName).config" Destination="$(_PackageTempDir)\appSettings.config" Condition="Exists('$(ProjectDir)\WebConfigs\appSettings.$(EnvironmentName).config')" />
    <TransformXml Source="$(ProjectDir)\connections.config" Transform="$(ProjectDir)\WebConfigs\connections.$(EnvironmentName).config" Destination="$(_PackageTempDir)\connections.config" Condition="Exists('$(ProjectDir)\WebConfigs\connections.$(EnvironmentName).config')" />
    <TransformXml Source="$(ProjectDir)\session.config" Transform="$(ProjectDir)\WebConfigs\session.$(EnvironmentName).config" Destination="$(_PackageTempDir)\session.config" Condition="Exists('$(ProjectDir)\WebConfigs\session.$(EnvironmentName).config')" />
    <TransformXml Source="$(ProjectDir)\sessions.config" Transform="$(ProjectDir)\WebConfigs\sessions.$(EnvironmentName).config" Destination="$(_PackageTempDir)\sessions.config" Condition="Exists('$(ProjectDir)\WebConfigs\sessions.$(EnvironmentName).config')" />
    <TransformXml Source="$(ProjectDir)\outputCacheSettings.config" Transform="$(ProjectDir)\WebConfigs\outputCacheSettings.$(EnvironmentName).config" Destination="$(_PackageTempDir)\outputCacheSettings.config" Condition="Exists('$(ProjectDir)\WebConfigs\outputCacheSettings.$(EnvironmentName).config')" />
  </Target>
  <Target Name="PrepareForPackage" AfterTargets="CopyAllFilesToSingleFolderForPackage">
    <Message Text="Preparing minification for JS and CSS and config transformation..." Importance="high" />
    <ItemGroup>
      <FileToDelete Include="$(_PackageTempDir)\WebConfigs\**; $(_PackageTempDir)\**\*.css; $(_PackageTempDir)\**\*.js" Exclude="$(_PackageTempDir)\**\*.min.css; $(_PackageTempDir)\**\*.min.js;" />
    </ItemGroup>
    <CallTarget Targets="TransformWebConfigForPackage" />
    <CallTarget Targets="MinifyJsCssForPackage" />
    <Delete Files="@(FileToDelete)" />
    <RemoveDir Directories="$(_PackageTempDir)\WebConfigs" />
  </Target>
  <Target Name="PrepareForMsdeploy" AfterTargets="CopyAllFilesToSingleFolderForMsdeploy">
    <Message Text="Preparing minification for JS and CSS and config transformation..." Importance="high" />
    <ItemGroup>
      <FileToDelete Include="$(_PackageTempDir)\WebConfigs\**; $(_PackageTempDir)\**\*.css; $(_PackageTempDir)\**\*.js" Exclude="$(_PackageTempDir)\**\*.min.css; $(_PackageTempDir)\**\*.min.js;" />
    </ItemGroup>
    <CallTarget Targets="TransformWebConfigForPackage" />
    <CallTarget Targets="MinifyJsCssForPackage" />
    <Delete Files="@(FileToDelete)" />
    <RemoveDir Directories="$(_PackageTempDir)\WebConfigs" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>0</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:59146/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>