.etooltip {
	z-index:1000;
	text-align:left;
	border:1px solid #C3D1EC;
	border-radius:4px;
	-moz-border-radius:4px;
	-webkit-border-radius:4px;
	min-width:50px;
	max-width:300px;
	background: white;
	box-shadow: 0 1px 5px #CCC;
	-moz-box-shadow: 0 1px 5px #CCC;
	-webkit-box-shadow: 0 1px 5px #CCC;
	/**
	 * - If you set a background-image, border/padding/background-color will be ingnored.
	 *   You can set any padding to .tip-inner instead if you need.
	 * - If you want a tiled background-image and border/padding for the tip,
	 *   set the background-image to .tip-inner instead.
	 */
}
.etooltip .tip-inner {	
	padding:6px 8px;
}

/* Configure an arrow image - the script will automatically position it on the correct side of the tip */
.etooltip .tip-arrow-top {
	margin-top:-6px;
	margin-left:-5px; /* approx. half the width to center it */
	top:0;
	left:50%;
	width:9px;
	height:6px;
	background:url(arrows.png) no-repeat;
}
.etooltip .tip-arrow-right {
	margin-top:-4px; /* approx. half the height to center it */
	margin-left:0;
	top:50%;
	left:100%;
	width:6px;
	height:9px;
	background:url(arrows.png) no-repeat -9px 0;
}
.etooltip .tip-arrow-bottom {
	margin-top:0;
	margin-left:-5px; /* approx. half the width to center it */
	top:100%;
	left:50%;
	width:9px;
	height:6px;
	background:url(arrows.png) no-repeat -18px 0;
}
.etooltip .tip-arrow-bottom-left {
	left: 5%;
}
.etooltip .tip-arrow-bottom-right {
	left: 95%;
}
.etooltip .tip-arrow-left {
	margin-top:-4px; /* approx. half the height to center it */
	margin-left:-6px;
	top:50%;
	left:0;
	width:6px;
	height:9px;
	background:url(arrows.png) no-repeat -27px 0;
}
.etooltip .tip-arrow-top-left,
.etooltip .tip-arrow-bottom-left {
	left: 5%;
}
.etooltip .tip-arrow-top-right,
.etooltip .tip-arrow-bottom-right {
	left: 95%;
}
.etooltip .tip-arrow-left-top,
.etooltip .tip-arrow-right-top{
	top:5%;
}
.etooltip .tip-arrow-left-bottom,
.etooltip .tip-arrow-right-bottom{
	top:95%;
}

div.etooltip{visibility:hidden;position:absolute;top:0;left:0;}div.etooltip table, div.etooltip td{margin:0;}div.etooltip td.tip-bg-image span{display:block;height:10px;width:10px;overflow:hidden;}div.etooltip td.tip-right{background-position:100% 0;}div.etooltip td.tip-bottom{background-position:100% 100%;}div.etooltip td.tip-left{background-position:0 100%;}div.etooltip div.tip-inner{background-position:-10px -10px;}div.etooltip div.tip-arrow{visibility:hidden;position:absolute;overflow:hidden;}