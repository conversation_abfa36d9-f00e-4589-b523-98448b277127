body{
	margin: 0px;
}
.hyperlink, .disclaimer-box a 
{
    color:#0066d7;
    text-decoration: none;
}
.hyperlink:hover, .disclaimer-box a:hover 
{
    color:#0066d7;
    text-decoration: underline;
}
td.table-header{
    border-left: 2px solid #fff !important;
    line-height: 28px !important;
    vertical-align: middle;
}
td.table-row{
    line-height: 38px !important;
    vertical-align: middle;
    border-left: 2px solid #fff !important;
    border-top: 2px solid #fff !important;
}
span.title, .activity-trend-heading{
    font-weight: bold !important;
}
div.wrapper{
    width: 670px;
}
#columnChartArea{
    width: 670px;
}
.EUCalendar-day-selected
{
    background-color:#0066d7 !important;
}
.custom-range-calendar
{
    border: 1px solid #0066d7 !important;
}
.pie-chart-container
{
    width:400px;
}