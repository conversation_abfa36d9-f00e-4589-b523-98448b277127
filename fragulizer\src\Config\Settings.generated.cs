﻿using System;
using ToolsFramework.Cache;
using System.Web.Caching;

namespace ToolsFramework.Settings
{
    public interface ISettingsService
    {
		ToolCompanySettings GetSettings(string aCompanyCode);
    }

    public class SettingsService : ISettingsService
	{
		private ICache _cache;
    	private string _toolName;
        private string _GeneralPath;
        private string _ToolPath;

        public SettingsService(ICache cache, string toolName, string aGeneralPath, string aToolPath)
		{
			_cache = cache;
        	_toolName = toolName;
            _GeneralPath = aGeneralPath;
            _ToolPath = aToolPath;
		}

		public ToolCompanySettings GetSettings(string aCompanyCode)
        {
			// NOTE: [16-March-2012][<EMAIL>]: Instantiate ToolCompanySettings cause reading xml files every time 
			/*var a = new ToolCompanySettings(aCompanyCode, _toolName, _GeneralPath, _ToolPath);
            return AspectF.Define
				.Cache<ToolCompanySettings>(_cache, "setting_" + aCompanyCode, new CacheDependency(a.DependOnFiles.ToArray()))
				.Return(() => new ToolCompanySettings(aCompanyCode, _toolName, _GeneralPath, _ToolPath));*/

            var cacheKey = "setting_" + aCompanyCode;
            var cacheObj = this._cache.Get(cacheKey);
            if (cacheObj == null)
            {
                cacheObj = new ToolCompanySettings(aCompanyCode, _toolName, _GeneralPath, _ToolPath);
                this._cache.Add(cacheKey, cacheObj);
            }

            return (ToolCompanySettings)cacheObj;
        }
	}

	public class CloudSettingsService : ISettingsService
    {
        private readonly ICache cache;
        private readonly string toolName;
        private readonly Euroland.Azure.IAzureBlob azureBlob;

        public CloudSettingsService(ICache cache, string toolName, Euroland.Azure.IAzureBlob azureBlob)
		{
			this.cache = cache;
        	this.toolName = toolName;
            this.azureBlob = azureBlob;
		}
        #region ISettingsService Members

        public ToolCompanySettings GetSettings(string aCompanyCode)
        {
             //var a = new ToolCompanySettings(aCompanyCode, this.toolName, this.azureBlob);
            var cacheKey = "setting_" + aCompanyCode;
            var cacheObj = this.cache.Get(cacheKey);
            if (cacheObj == null)
            {
                cacheObj = new ToolCompanySettings(aCompanyCode, this.toolName, this.azureBlob);
                this.cache.Add(cacheKey, cacheObj);
            }
            return (ToolCompanySettings)cacheObj;
            /*return AspectF.Define
                .Cache<ToolCompanySettings>(this.cache, "setting_" + aCompanyCode)
                .Return(() => new ToolCompanySettings(aCompanyCode, this.toolName, this.azureBlob));*/
        }

        #endregion
    }

	[Serializable]
    public class InnerBase
    {
        private Setting _Setting;
        public InnerBase(Setting aSetting)
        {
            _Setting = aSetting;
        }

        public Setting Setting
        {
            get {return _Setting;}
        }
        
        public Setting this[string aKey]
        {
            get {return _Setting[aKey];}
            private set {}
        }
    }    

	[Serializable]
    public class ToolCompanySettings : ToolCompanySettingsBase
    {
        public ToolCompanySettings(string aCompanyName, string aToolName, string aGeneralPath, string aToolPath) : base(aCompanyName, aToolName, aGeneralPath, aToolPath)
        {
        }

		public ToolCompanySettings(string aCompanyName, 
            string aToolName,
            Euroland.Azure.IAzureBlob azureBlob)
            : base(aCompanyName, aToolName, azureBlob)
        {
        }
		public String colorbackground { get { return this["color-background"].ConvertTo<String>();}}
		public String fontfamily { get { return this["font-family"].ConvertTo<String>();}}
		public String fontsize { get { return this["font-size"].ConvertTo<String>();}}
		public String defaultculture { get { return this["defaultculture"].ConvertTo<String>();}}
		public Boolean googleanalyticsenabled { get { return this["googleanalyticsenabled"].ConvertTo<Boolean>();}}
		public Boolean enableheading { get { return this["enableheading"].ConvertTo<Boolean>();}}
		public Boolean enableinstrumentstable { get { return this["enableinstrumentstable"].ConvertTo<Boolean>();}}
		public Boolean showcurrencycolumn { get { return this["showcurrencycolumn"].ConvertTo<Boolean>();}}
		public Boolean enableactivitytrend { get { return this["enableactivitytrend"].ConvertTo<Boolean>();}}
		public String defaultperiod { get { return this["defaultperiod"].ConvertTo<String>();}}
		public String instrumentgroups { get { return this["instrumentgroups"].ConvertTo<String>();}}
		public String custommarketname { get { return this["custommarketname"].ConvertTo<String>();}}
        public class formatClass : InnerBase
        {
            public formatClass(Setting parentSetting) : base(parentSetting)
            {
            }
	        public class engbClass : InnerBase
	        {
	            public engbClass(Setting parentSetting) : base(parentSetting)
	            {
	            }
				public String shortdate { get { return this["shortdate"].ConvertTo<String>();}}
				public String longdate { get { return this["longdate"].ConvertTo<String>();}}
				public String shorttime { get { return this["shorttime"].ConvertTo<String>();}}
				public String longtime { get { return this["longtime"].ConvertTo<String>();}}
				public String decimalseparator { get { return this["decimalseparator"].ConvertTo<String>();}}
				public String thousandsseparator { get { return this["thousandsseparator"].ConvertTo<String>();}}
				public String negativenumberformat { get { return this["negativenumberformat"].ConvertTo<String>();}}
	        }
	        public engbClass engb { get { return new engbClass(this["en-gb"]); } }
        }
        public formatClass format { get { return new formatClass(this["format"]); } }
		public String timezone { get { return this["timezone"].ConvertTo<String>();}}
		public Int32 sharepricenumberdecimaldigits { get { return this["sharepricenumberdecimaldigits"].ConvertTo<Int32>();}}
		public Int32 sharepricepercentagedecimaldigits { get { return this["sharepricepercentagedecimaldigits"].ConvertTo<Int32>();}}
		public String sharepricevalueincreasecolor { get { return this["sharepricevalueincreasecolor"].ConvertTo<String>();}}
		public String sharepricevaluedecreasecolor { get { return this["sharepricevaluedecreasecolor"].ConvertTo<String>();}}
		public Boolean enableexceldownload { get { return this["enableexceldownload"].ConvertTo<Boolean>();}}
		public Boolean enableprint { get { return this["enableprint"].ConvertTo<Boolean>();}}
		public Boolean enablejpgdownload { get { return this["enablejpgdownload"].ConvertTo<Boolean>();}}
		public Boolean enablepdfdownload { get { return this["enablepdfdownload"].ConvertTo<Boolean>();}}
		public Boolean showdataproviderinfo { get { return this["showdataproviderinfo"].ConvertTo<Boolean>();}}
		public Boolean showdatadelayinfo { get { return this["showdatadelayinfo"].ConvertTo<Boolean>();}}
		public Boolean showsupplierinfo { get { return this["showsupplierinfo"].ConvertTo<Boolean>();}}
		public Boolean showsupplierinfolink { get { return this["showsupplierinfolink"].ConvertTo<Boolean>();}}
		public Boolean showdisclaimerinfo { get { return this["showdisclaimerinfo"].ConvertTo<Boolean>();}}
		public Boolean showcookiepolicyinfo { get { return this["showcookiepolicyinfo"].ConvertTo<Boolean>();}}
		public Int32 numberofyearoncolumnchart { get { return this["numberofyearoncolumnchart"].ConvertTo<Int32>();}}
		public String chartcolors { get { return this["chartcolors"].ConvertTo<String>();}}
		public String chartbgcolor { get { return this["chartbgcolor"].ConvertTo<String>();}}
		public Int32 pieborderwidth { get { return this["pieborderwidth"].ConvertTo<Int32>();}}
		public Boolean uselatinnumbers { get { return this["uselatinnumbers"].ConvertTo<Boolean>();}}
		public String maintextfont { get { return this["maintextfont"].ConvertTo<String>();}}
		public String maintextfontsize { get { return this["maintextfontsize"].ConvertTo<String>();}}
		public String maintextcolor { get { return this["maintextcolor"].ConvertTo<String>();}}
		public String mainheadingfont { get { return this["mainheadingfont"].ConvertTo<String>();}}
		public String mainheadingfontsize { get { return this["mainheadingfontsize"].ConvertTo<String>();}}
		public String mainheadingcolor { get { return this["mainheadingcolor"].ConvertTo<String>();}}
		public String secondheadingfont { get { return this["secondheadingfont"].ConvertTo<String>();}}
		public String secondheadingfontsize { get { return this["secondheadingfontsize"].ConvertTo<String>();}}
		public String secondheadingcolor { get { return this["secondheadingcolor"].ConvertTo<String>();}}
		public String tableheadfont { get { return this["tableheadfont"].ConvertTo<String>();}}
		public String tableheadfontsize { get { return this["tableheadfontsize"].ConvertTo<String>();}}
		public String tableheadcolor { get { return this["tableheadcolor"].ConvertTo<String>();}}
		public String tableheadbackgroundcolor { get { return this["tableheadbackgroundcolor"].ConvertTo<String>();}}
		public String tableinlinebordercolor { get { return this["tableinlinebordercolor"].ConvertTo<String>();}}
		public Boolean tableinlineborderenable { get { return this["tableinlineborderenable"].ConvertTo<Boolean>();}}
		public String tableoutlinebordercolor { get { return this["tableoutlinebordercolor"].ConvertTo<String>();}}
		public Boolean tableoutlineborderenable { get { return this["tableoutlineborderenable"].ConvertTo<Boolean>();}}
		public String tableoddrowcolor { get { return this["tableoddrowcolor"].ConvertTo<String>();}}
		public String tableevenrowcolor { get { return this["tableevenrowcolor"].ConvertTo<String>();}}
		public String linkcolor { get { return this["linkcolor"].ConvertTo<String>();}}
		public String linkhovercolor { get { return this["linkhovercolor"].ConvertTo<String>();}}
		public String linkunderline { get { return this["linkunderline"].ConvertTo<String>();}}
		public String customphrases { get { return this["customphrases"].ConvertTo<String>();}}
		public String customcurrencysign { get { return this["customcurrencysign"].ConvertTo<String>();}}
    }
}
