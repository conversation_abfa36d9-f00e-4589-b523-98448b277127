﻿<!--
***********************************************************************************************
Microsoft.Web.Publishing.Deploy.MSDeploy.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your web deploy projects from the command-line or the IDE.

This file defines the steps in the standard package/publish process for Deploy 
Currently

Copyright (C) Microsoft Corporation. All rights reserved.
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Import task from our dll-->
  <UsingTask TaskName="GetPublishingLocalizedString" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>

  <!--ImportBefore Extension-->
  <PropertyGroup>
    <ImportByWildcardBeforeMicrosoftWebPublishingDeployMSDeployTargets Condition="'$(ImportByWildcardBeforeMicrosoftWebPublishingDeployMSDeployTargets)'==''">true</ImportByWildcardBeforeMicrosoftWebPublishingDeployMSDeployTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportBefore\*" Condition="'$(ImportByWildcardBeforeMicrosoftWebPublishingDeployMSDeployTargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportBefore')"/>

  <!--This only support new /t:WebPublish with the 'WebPublishMethod'=='MSDeploy'-->


  <PropertyGroup>
    <Microsoft_Web_Publishing_MSDeploy_Common_targets Condition="'$(Microsoft_Web_Publishing_MSDeploy_Common_targets)' == ''">Microsoft.Web.Publishing.MSDeploy.Common.targets</Microsoft_Web_Publishing_MSDeploy_Common_targets>
  </PropertyGroup>
  <Import Project="$(Microsoft_Web_Publishing_MSDeploy_Common_targets)" Condition="'$(Microsoft_Web_Publishing_MSDeploy_Common_targets_Imported)' != 'true' And Exists($(Microsoft_Web_Publishing_MSDeploy_Common_targets))"/>

  <PropertyGroup>
    <Microsoft_Web_Publishing_Deploy_MSDeploy_targets_Imported>True</Microsoft_Web_Publishing_Deploy_MSDeploy_targets_Imported>
    <PipelineDeployPhaseDependsOn>
      $(PipelineDeployPhaseDependsOn);
      PipelineMsdeploySpecificTransformPhase;
    </PipelineDeployPhaseDependsOn>
  </PropertyGroup>



  <!--ImportAfter Extension-->
  <PropertyGroup>
    <ImportByWildcardAfterMicrosoftWebPublishingDeployMSDeployTargets Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingDeployMSDeployTargets)'==''">true</ImportByWildcardAfterMicrosoftWebPublishingDeployMSDeployTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter\*" Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingDeployMSDeployTargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter')"/>

</Project>
