﻿// ----------------------------------------------------------------------
// <copyright file="InstrumentGroupSettingService.cs" company="Euroland.com">
//     Copyright (c) Euroland.com. All rights reserved.
// </copyright>
// <author><EMAIL></author>
// <created>Wednesday, May 15, 2013 17:57</created>
// <lastedit>Wednesday, May 15, 2013 17:57</lastedit>
// <changes>
// </changes>
// -----------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using ToolsFramework.Settings;
using Euroland.Azure;
using System.IO;
using System.Xml;
using ToolsFramework.Cache;

namespace Fragulizer.SettingService
{
    public interface IInstrumentGroupSettingService
    {
        InstrumentGroupToolCompanySetting GetSettings(string aCompanyCode);
    }

    public class InstrumentGroupSettingService : IInstrumentGroupSettingService
    {
        private ICache _cache;
        private string _toolName;
        private string _GeneralPath;
        private string _ToolPath;

        public InstrumentGroupSettingService(ICache cache, string toolName, string aGeneralPath, string aToolPath)
        {
            _cache = cache;
            _toolName = toolName;
            _GeneralPath = aGeneralPath;
            _ToolPath = aToolPath;
        }

        public InstrumentGroupToolCompanySetting GetSettings(string aCompanyCode)
        {
            var cacheKey = "instrumentgroup_setting_" + aCompanyCode;
            var cacheObj = this._cache.Get(cacheKey);
            if (cacheObj == null)
            {
                cacheObj = new InstrumentGroupToolCompanySetting(aCompanyCode, _toolName, _GeneralPath, _ToolPath);
                this._cache.Add(cacheKey, cacheObj);
            }

            return (InstrumentGroupToolCompanySetting)cacheObj;
        }
    }

    public class CloudInstrumentGroupSettingService : IInstrumentGroupSettingService
    {
        private readonly ICache cache;
        private readonly string toolName;
        private readonly Euroland.Azure.IAzureBlob azureBlob;

        public CloudInstrumentGroupSettingService(ICache cache, string toolName, Euroland.Azure.IAzureBlob azureBlob)
        {
            this.cache = cache;
            this.toolName = toolName;
            this.azureBlob = azureBlob;
        }
        #region ISettingsService Members

        public InstrumentGroupToolCompanySetting GetSettings(string aCompanyCode)
        {
            var cacheKey = "instrumentgroup_setting_" + aCompanyCode;
            var cacheObj = this.cache.Get(cacheKey);
            if (cacheObj == null)
            {
                cacheObj = new InstrumentGroupToolCompanySetting(aCompanyCode, this.toolName, this.azureBlob);
                this.cache.Add(cacheKey, cacheObj);
            }
            return (InstrumentGroupToolCompanySetting)cacheObj;
        }

        #endregion
    }

    public class InstrumentGroupToolCompanySetting : ToolCompanySettingsBase
    {
        private List<InstrumentgroupClass> _InstrumentGroupClass = new List<InstrumentgroupClass>();
        
        private ESETTINGS_LEVEL SettingsLevel = ESETTINGS_LEVEL.TOOL_COMPANY;

        public InstrumentGroupToolCompanySetting(string aNameCompany, string aNameTool, string aPath, string aToolPath)
            : base(aNameCompany, aNameTool, aPath, aToolPath)
        {
        }

        public InstrumentGroupToolCompanySetting(string companyCode, string toolName, IAzureBlob azureBlob)
            : base(companyCode, toolName, azureBlob)
        {
        }

        protected override bool Read()
        {
            return Parse();
        }

        private bool isLanguageSpecificOrGeneralTag(XmlNode node)
        {
            if (node.Attributes == null)
                return true;

            XmlAttribute attribute = node.Attributes["lang"];
            if (attribute == null)
                return true;
            return attribute.InnerText == System.Globalization.CultureInfo.CurrentCulture.Name;
        }

        private Setting FillElement(XmlNode aNode = null, Setting aSetting = null)
        {
            foreach (XmlNode Node in aNode.ChildNodes)
            {
                if (!isLanguageSpecificOrGeneralTag(Node))
                    continue;

                if (Node.NodeType == XmlNodeType.Text)
                {
                    aSetting.Set(Node.InnerText);
                    continue;
                }
                else if (Node.NodeType == XmlNodeType.Comment) 
                    continue;

                Setting SettingPointer = aSetting.Create(Node.Name);
                SettingPointer.Set(Node.InnerText);
                SettingPointer.Set(this.SettingsLevel);
                //SettingPointer.Key = Node.Name;
                FillElement(Node, SettingPointer);
            }
            return aSetting;
        }

        /// <summary>
        /// Parse and read children of ShareType element into a list
        /// </summary>
        /// <returns></returns>
        private bool Parse()
        {
            bool success = false;
                try
                {
                    XmlDocument XmlDoc = this.CreateXmlDocument(this.ToolCompanySettingsFileName, this.SettingsLevel);
                    if (XmlDoc != null)
                    {
                        XmlNode shareTypeNode = XmlDoc.DocumentElement.SelectSingleNode(
                            "/*[translate(local-name(),'ABCDEFGHIJKLMNOPQRSTUVWXYZ','abcdefghijklmnopqrstuvwxyz')='settings']/*[translate(local-name(),'ABCDEFGHIJKLMNOPQRSTUVWXYZ','abcdefghijklmnopqrstuvwxyz')='instrumentgroups']");
                        if (shareTypeNode == null)
                            throw new InvalidDataException(string.Concat("There is no \"InstrumentGroups\" node in setting file at: ", this.ToolCompanySettingsFileName));
                        
                        foreach (XmlNode Node in shareTypeNode.ChildNodes)
                        {
                            if (Node.NodeType == XmlNodeType.Comment || !isLanguageSpecificOrGeneralTag(Node)) continue;

                            Setting setting = new Setting(Node.Name);
                            _InstrumentGroupClass.Add(new InstrumentgroupClass(setting));
                            FillElement(Node, setting);
                        }
                    }
                    success = true;
                }
                catch (ToolsFramework.SettingFileNotFoundException fEx)
                {
                    Euroland.Azure.Helpers.TraceHelper.TraceError("{0}", fEx.Message);
                    throw fEx;
                }
                catch (Exception ex)
                {
                    Euroland.Azure.Helpers.TraceHelper.TraceError("{0}", ex.Message + System.Environment.NewLine + ex.StackTrace);
                }
            return success;
        }

        public List<InstrumentgroupClass> InstrumentGroups
        {
            get
            {
                return _InstrumentGroupClass;
            }
        }
       
        public class InstrumentgroupClass: InnerBase
        {
            public InstrumentgroupClass(Setting parentSetting)
                : base(parentSetting)
            {
            }
            public string Name
            {
                get
                {
                    if (this["name"].Contains(Fragulizer.Common.RequestHelper.Lang))
                        return this["name"][Fragulizer.Common.RequestHelper.Lang].Value;
                    else
                        return this["name"]["en-gb"].Value;
                }
            }
            public String InstrumentIds 
            { 
                get 
                {
                    return this["instrumentids"].ConvertTo<String>(); 
                } 
            }
        }
    }
}