﻿<!--
***********************************************************************************************
Microsoft.Web.Publishing.AllFilesInProjectFolder.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your web deploy projects from the command-line or the IDE.

This file defines the steps in the standard package/publish process for collecting all files in the project folder.

Copyright (C) Microsoft Corporation. All rights reserved.
***********************************************************************************************
-->
<Project  xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Import task from our dll-->
  <UsingTask TaskName="CollectFilesinFolder" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="EscapeTextForRegularExpressions" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="FilterByItems" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="GetPublishingLocalizedString" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>

  <!--ImportBefore Extension-->
  <PropertyGroup>
    <ImportByWildcardBeforeMicrosoftWebPublishingAllFilesInProjectFolderTargets Condition="'$(ImportByWildcardBeforeMicrosoftWebPublishingAllFilesInProjectFolderTargets)'==''">true</ImportByWildcardBeforeMicrosoftWebPublishingAllFilesInProjectFolderTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportBefore\*" Condition="'$(ImportByWildcardBeforeMicrosoftWebPublishingAllFilesInProjectFolderTargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportBefore')"/>


  <PropertyGroup>
    <PublishPipelineCollectFilesCore>
      $(PublishPipelineCollectFilesCore);
      CollectFilesFromProjectFolder;
     </PublishPipelineCollectFilesCore>
    <_CollectFiles_IncludeIgnorableFile Condition="'$(_CollectFiles_IncludeIgnorableFile)'==''">False</_CollectFiles_IncludeIgnorableFile>

    <!--Overwrite the default behavior.  Don't remove the web.debug.config -->
    <ExcludeTransformAssistFilesFromPublish Condition="'$(ExcludeTransformAssistFilesFromPublish)'==''">False</ExcludeTransformAssistFilesFromPublish>
    <CollectFilesFromProjectFolder_ExcludeSccFiles Condition="'$(CollectFilesFromProjectFolder_ExcludeSccFiles)'==''">True</CollectFilesFromProjectFolder_ExcludeSccFiles>
  </PropertyGroup>

  <!--********************************************************************-->
  <!-- Task SetExcludeFromProjectFolder -->
  <!--********************************************************************-->
  <PropertyGroup>
    <SetExcludeFromProjectFolderDependsOn Condition="'$(SetExcludeFromProjectFolderDependsOn)'==''">
    </SetExcludeFromProjectFolderDependsOn>
  </PropertyGroup>

  <Target Name="SetExcludeFromProjectFolder"
          DependsOnTargets="$(SetExcludeFromProjectFolderDependsOn)">

    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_DefaultExcludeIntermediateOutputFolderMessage"
      LogType="Message" />
      
    <GetPublishingLocalizedString
          ID="PublishLocalizedString_DefaultExcludeFileExtentionOutMessage"
          LogType="Message" />
    <GetPublishingLocalizedString
          ID="PublishLocalizedString_DefaultExcludeSourceControlItems"
          LogType="Message" />

    <!--Maybe we need to introduce @(_ExcludeFromProjectFolder)-->
    <ItemGroup>

      <!--Split item to _KnowProjectItems is to keep the metadata for item collection. 
      Please see  http://msdn.microsoft.com/en-us/library/bb629388.aspx for known Project item collection.-->
      <_KnownProjectItems Include="@(EmbeddedResource)" />
      <_KnownProjectItems Include="@(None)" />
      <_KnownProjectItems Include="@(Content)" />
      <_KnownProjectItems Include="@(Compile)" />


      <!--Explicit exclude all files under the obj folder -->
      <_ProjectBaseIntermediateOutputPath Include="$(BaseIntermediateOutputPath)"/>
      
    </ItemGroup>

    <!--Create the rgulare expression to exclude all root folder *.out, all folder *.scc and all folder*.vspscc file-->
    <!--Note split by semicolon (;)-->
    <PropertyGroup>
      <_ExcludeRelativePathFromProjectFolder>^[^\\]*\.out$</_ExcludeRelativePathFromProjectFolder>
    </PropertyGroup>

    <!--Create the rgulare expression to exclude all Intermedidate output path.-->
    <EscapeTextForRegularExpressions Text="@(_ProjectBaseIntermediateOutputPath->'%(FullPath)')">
      <Output TaskParameter="Result" PropertyName="_ExcludeFullPathFromProjectFolder" />
    </EscapeTextForRegularExpressions>

  </Target>


  <!--********************************************************************-->
  <!-- Task CollectFilesFromProjectFolder_ExcludeSccFiles -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFromProjectFolder_ExcludeSccFilesDependsOn>
      $(OnBeforeCollectFilesFromProjectFolder_ExcludeSccFiles);
      $(CollectFilesFromProjectFolder_ExcludeSccFilesDependsOn);
      SetExcludeFromProjectFolder;
    </CollectFilesFromProjectFolder_ExcludeSccFilesDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFromProjectFolder_ExcludeSccFiles"
          DependsOnTargets="$(CollectFilesFromProjectFolder_ExcludeSccFilesDependsOn)"
          Condition="$(CollectFilesFromProjectFolder_ExcludeSccFiles)">
    <!--Note split by semicolon (;)-->
    <PropertyGroup>
      <_ExcludeRelativePathFromProjectFolder>$(_ExcludeRelativePathFromProjectFolder);[^\\]*\.scc$;[^\\]*\.vssscc$;[^\\]*\.vspscc$</_ExcludeRelativePathFromProjectFolder>
    </PropertyGroup>

    <CallTarget Targets="$(OnAfterCollectFilesFromProjectFolder_ExcludeSccFiles)" RunEachTargetSeparately="false" />
  </Target>
  
  
  
  <!--********************************************************************-->
  <!-- Task CollectFilesFromProjectFolder -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFromProjectFolderDependsOn>
      $(OnBeforeCollectFilesFromProjectFolder);
      $(CollectFilesFromProjectFolderDependsOn);
      SetExcludeFromProjectFolder;
      CollectFilesFromProjectFolder_ExcludeSccFiles;
    </CollectFilesFromProjectFolderDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFromProjectFolder"
          DependsOnTargets="$(CollectFilesFromProjectFolderDependsOn)" >

    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherAllFilesInProjectFolderExceptExclusion"
      LogType="Message" />

    <CollectFilesinFolder RootPath="$(WebPublishPipelineProjectDirectory)"
                          ExcludeFullPathMatchs="$(_ExcludeFullPathFromProjectFolder)"
                          ExcludeRelativePathMatchs="$(_ExcludeRelativePathFromProjectFolder)" 
                          SkipHiddenItems="$(_SkipHiddenItems)">
      <Output TaskParameter="Result" ItemName="_AllFilesUnderProjectFolderNoMetadata" />
    </CollectFilesinFolder>
    

    <!--The first two lines keeps the _KnowProjectItem's item metadata-->
    <FilterByItems PipelineItems="@(_AllFilesUnderProjectFolderNoMetadata)"
                   Filter="@(_KnownProjectItems)">
      <Output TaskParameter="OutFilter" ItemName="_AllFilesUnderProjectFolderExceptKnownProjectItems"/>
    </FilterByItems>
    
    <ItemGroup>
      <!--Readd  _KnowProjectItem's to keep  item metadata. First for non-Link file -->
      <FilesForPackagingFromProject Include="@(_KnownProjectItems)" Condition="'%(_KnownProjectItems.Link)'==''">
        <DestinationRelativePath>%(_KnownProjectItems.Identity)</DestinationRelativePath>
        <FromTarget>CollectFilesFromProjectFolder.KonwnProjectItems</FromTarget>
        <Category>AllFilesInProjectFolder</Category>
      </FilesForPackagingFromProject>
      <FilesForPackagingFromProject Include="@(_KnownProjectItems)"  Condition="'%(_KnownProjectItems.Link)'!='' And $(EnableCollectLinkFilesInProject)">
        <DestinationRelativePath>%(_KnownProjectItems.Link)</DestinationRelativePath>
        <FromTarget>CollectFilesFromProjectFolder.KonwnProjectItems</FromTarget>
        <Category>AllFilesInProjectFolder</Category>
        <Exclude>$(ExcludeLinkFilesInProject)</Exclude>
        <ProjectFileType>Link</ProjectFileType>
      </FilesForPackagingFromProject>
      
      <FilesForPackagingFromProject Include="@(_AllFilesUnderProjectFolderExceptKnownProjectItems)" >
        <DestinationRelativePath>%(_AllFilesUnderProjectFolderExceptKnownProjectItems.Identity)</DestinationRelativePath>
        <FromTarget>CollectFilesFromProjectFolder.ExceptKonwnProjectItems</FromTarget>
        <Category>AllFilesInProjectFolder</Category>
      </FilesForPackagingFromProject>

    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFromProjectFolder)" RunEachTargetSeparately="false" />
  </Target>

  <!--ImportAfter Extension-->
  <PropertyGroup>
    <ImportByWildcardAfterMicrosoftWebPublishingAllFilesInProjectFolderTargets Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingAllFilesInProjectFolderTargets)'==''">true</ImportByWildcardAfterMicrosoftWebPublishingAllFilesInProjectFolderTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter\*" Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingAllFilesInProjectFolderTargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter')"/>

</Project>

