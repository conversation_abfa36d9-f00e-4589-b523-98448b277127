﻿/*
 * Binh Nguyen
 * 
 * Email: <EMAIL>
 * Created Date: 08/03/2010
 * Last Update: 
 */


using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.UI;
using System.IO;
using System.Text;

namespace Fragulizer.Common
{
    public static class RenderPartialViewExtension
    {
        /// <summary> 
        /// Render a partial as a string. 
        /// </summary> 
        public static string RenderPartialAsString(this Controller controller, string partialPath)
        {
            //var controller = ctx.Controller;
            return RenderPartialAsString(controller.ControllerContext, partialPath, controller.ViewData, controller.TempData);
        }

        public static string RenderPartialAsString(this Controller controller, string partialPath, object model)
        {
            ViewDataDictionary viewData = null;
            if (model != null)
                viewData = new ViewDataDictionary(model);
            else
                viewData = controller.ViewData;
            return RenderPartialAsString(controller.ControllerContext, partialPath, viewData, controller.TempData);
        }

        /// <summary> 
        /// Render a partial as a string. 
        /// </summary> 
        static string RenderPartialAsString(ControllerContext context, string partialPath, ViewDataDictionary viewData, TempDataDictionary tempData)
        {
            ViewEngineResult result = ViewEngines.Engines.FindPartialView(context, partialPath);

            if (result.View != null)
            {
                StringBuilder sb = new StringBuilder();
                using (StringWriter sw = new StringWriter(sb))
                {
                    using (HtmlTextWriter output = new HtmlTextWriter(sw))
                    {
                        ViewContext viewContext = new ViewContext(context, result.View, viewData, tempData, output);
                        result.View.Render(viewContext, output);
                    }
                }

                return sb.ToString();
            }

            return String.Empty;
        } 
    }
}
