﻿<!--
***********************************************************************************************
Microsoft.Web.Publishing.OnlyFilesToRunTheApp.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your web deploy projects from the command-line or the IDE.

This file defines the steps in the standard package/publish process for collecting only files to run the web appliation.

Copyright (C) Microsoft Corporation. All rights reserved.
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Import task from our dll-->
  <UsingTask TaskName="GetPublishingLocalizedString" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>
  <UsingTask TaskName="FilterByItems" AssemblyFile="..\Microsoft.Web.Publishing.Tasks.dll"/>

  <!--ImportBefore Extension-->
  <PropertyGroup>
    <ImportByWildcardBeforeMicrosoftWebPublishingOnlyFilesToRunTheAppTargets Condition="'$(ImportByWildcardBeforeMicrosoftWebPublishingOnlyFilesToRunTheAppTargets)'==''">true</ImportByWildcardBeforeMicrosoftWebPublishingOnlyFilesToRunTheAppTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportBefore\*" Condition="'$(ImportByWildcardBeforeMicrosoftWebPublishingOnlyFilesToRunTheAppTargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportBefore')"/>

  <PropertyGroup>
    <PublishPipelineCollectFilesCore>
      $(PublishPipelineCollectFilesCore);
      CollectFilesFromIntermediateAssembly;
      CollectFilesFromContent;
      CollectFilesFromAddModules;
      CollectFilesFrom_SGenDllCreated;
      CollectFilesFromIntermediateSatelliteAssembliesWithTargetPath;
      CollectFilesFromReference;
      CollectFilesFromAllExtraReferenceFiles;
      CollectFilesFrom_SourceItemsToCopyToOutputDirectory;
      CollectFilesFromDocFileItem;
      CollectFilesFrom_WebApplicationSilverlightXapFiles;
      CollectFilesFrom_binDeployableAssemblies;
    </PublishPipelineCollectFilesCore>
    <ExcludeTransformAssistFilesFromPublish Condition="'$(ExcludeTransformAssistFilesFromPublish)'==''">True</ExcludeTransformAssistFilesFromPublish>
  </PropertyGroup>
  
  <!--***************************************************************-->
  <!-- Task CollectFilesFromIntermediateAssembly                     -->
  <!-- Note $(OutDir) can be not in the bin like in Team build system -->
  <!--***************************************************************-->
  <PropertyGroup>
    <CollectFilesFromIntermediateAssemblyDependsOn>
      $(OnBeforeCollectFilesFromIntermediateAssembly);
      $(CollectFilesFromIntermediateAssemblyDependsOn);
    </CollectFilesFromIntermediateAssemblyDependsOn>
  </PropertyGroup>

  <Target Name="CollectFilesFromIntermediateAssembly"
          DependsOnTargets="$(CollectFilesFromIntermediateAssemblyDependsOn)"
          Condition="'@(IntermediateAssembly)'!=''">

    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificItemsFromProject"
      ArgumentCount="1"
      Arguments="IntermediateAssembly"
      LogType="Message" />
    <Message Text="@(IntermediateAssembly->'$(OutDir)%(FileName)%(Extension) to bin\%(FileName)%(Extension)')" />
    
    <ItemGroup>
      <FilesForPackagingFromProject
        Include="@(IntermediateAssembly->'$(OutDir)%(FileName)%(Extension)')">
        <DestinationRelativePath>bin\%(FileName)%(Extension)</DestinationRelativePath>
        <FromTarget>CollectFilesFromIntermediateAssembly</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <Message Text="@(IntermediateAssembly->'$(OutDir)%(FileName).pdb to bin\%(FileName).pdb')"
             Condition="$(_DebugSymbolsProduced) AND !$(ExcludeGeneratedDebugSymbol) AND Exists(@(IntermediateAssembly->'$(OutDir)%(FileName).pdb')) "/>
    <ItemGroup Condition="$(_DebugSymbolsProduced) AND !$(ExcludeGeneratedDebugSymbol) AND Exists(@(IntermediateAssembly->'$(OutDir)%(FileName).pdb'))">
      <FilesForPackagingFromProject
        Include="@(IntermediateAssembly->'$(OutDir)%(FileName).pdb')"
        Condition="Exists(@(IntermediateAssembly->'$(OutDir)%(FileName).pdb'))">
        <DestinationRelativePath>bin\%(FileName).pdb</DestinationRelativePath>
        <FromTarget>CollectFilesFromIntermediateAssembly</FromTarget>
        <Category>Debug</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>

    <CallTarget Targets="$(OnAfterCollectFilesFromIntermediateAssembly)" RunEachTargetSeparately="false" />
  </Target>

  <!--********************************************************************-->
  <!-- Task CollectFilesFromContent -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFromContentDependsOn>
      $(OnBeforeCollectFilesFromContent);
      $(CollectFilesFromContentDependsOn);
    </CollectFilesFromContentDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFromContent"
          DependsOnTargets="$(CollectFilesFromContentDependsOn)"
          Condition="'@(Content)'!=''">
    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificItemsFromProject"
      ArgumentCount="1"
      Arguments="Content"
      LogType="Message" />
    <Message Text="@(Content)" />
    
    <ItemGroup>
      <FilesForPackagingFromProject Include="@(Content)" Condition="'%(Content.Link)'==''">
        <DestinationRelativePath>%(Content.Identity)</DestinationRelativePath>
        <FromTarget>CollectFilesFromContent</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
      <FilesForPackagingFromProject Include="@(Content)" Condition="'%(Content.Link)'!='' And $(EnableCollectLinkFilesInProject)">
        <DestinationRelativePath>%(Content.Link)</DestinationRelativePath>
        <FromTarget>CollectFilesFromContent</FromTarget>
        <Category>Run</Category>
        <Exclude>$(ExcludeLinkFilesInProject)</Exclude>
        <ProjectFileType>Link</ProjectFileType>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFromContent)" RunEachTargetSeparately="false" />
  </Target>


  <!--********************************************************************-->
  <!-- Task CollectFilesFromAddModules -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFromAddModulesDependsOn>
      $(OnBeforeCollectFilesFromAddModulesDependsOn);
      $(CollectFilesFromAddModulesDependsOn);
    </CollectFilesFromAddModulesDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFromAddModules"
          DependsOnTargets="$(CollectFilesFromAddModulesDependsOn)"
          Condition="'@(AddModules)'!=''">

    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificOutputsFromProject"
      ArgumentCount="1"
      Arguments="AddModules"
      LogType="Message" />
    <Message Text="@(AddModules->'$(OutDir)%(FileName)%(Extension) to bin\%(FileName)%(Extension)')"/>
    <ItemGroup>
      <FilesForPackagingFromProject  Include="@(AddModules->'$(OutDir)%(FileName)%(Extension)')">
        <DestinationRelativePath>bin\%(FileName)%(Extension)</DestinationRelativePath>
        <FromTarget>CollectFilesFromAddModules</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFromAddModulesDependsOn)" RunEachTargetSeparately="false" />
  </Target>

  <!--********************************************************************-->
  <!-- Task CollectFilesFrom_SGenDllCreated -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFrom_SGenDllCreatedDependsOn>
      $(OnBeforeCollectFilesFrom_SGenDllCreated);
      $(CollectFilesFrom_SGenDllCreatedDependsOn);
    </CollectFilesFrom_SGenDllCreatedDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFrom_SGenDllCreated"
          DependsOnTargets="$(CollectFilesFrom_SGenDllCreatedDependsOn)"
          Condition="$(_SGenDllCreated)">
    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificOutputsFromProject"
      ArgumentCount="1"
      Arguments="_SGenDllName"
      LogType="Message" />
    <Message Text="bin\$(_SGenDllName)" />
    
    <ItemGroup>
      <FilesForPackagingFromProject  Include="$(OutDir)$(_SGenDllName)">
        <DestinationRelativePath>bin\$(_SGenDllName)</DestinationRelativePath>
        <FromTarget>CollectFilesFrom_SGenDllCreated</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFrom_SGenDllCreated)" RunEachTargetSeparately="false" />
  </Target>

  <!--********************************************************************-->
  <!-- Task CollectFilesFromIntermediateSatelliteAssembliesWithTargetPath -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFromIntermediateSatelliteAssembliesWithTargetPathDependsOn>
      $(OnBeforeCollectFilesFromIntermediateSatelliteAssembliesWithTargetPath);
      $(CollectFilesFromIntermediateSatelliteAssembliesWithTargetPathDependsOn);
    </CollectFilesFromIntermediateSatelliteAssembliesWithTargetPathDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFromIntermediateSatelliteAssembliesWithTargetPath"
          DependsOnTargets="$(CollectFilesFromIntermediateSatelliteAssembliesWithTargetPathDependsOn)">
    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificOutputsFromProject"
      ArgumentCount="1"
      Arguments="IntermediateSatelliteAssembliesWithTargetPath"
      LogType="Message" />
    <Message Text="@(IntermediateSatelliteAssembliesWithTargetPath->'$(OutDir)%(TargetPath) to bin\%(TargetPath)')"
             Condition="Exists('$(OutDir)\%(TargetPath)')"/>
    <ItemGroup>
      <FilesForPackagingFromProject
        Include="@(IntermediateSatelliteAssembliesWithTargetPath->'$(OutDir)%(TargetPath)')">
        <DestinationRelativePath>bin\%(IntermediateSatelliteAssembliesWithTargetPath.TargetPath)</DestinationRelativePath>
        <FromTarget>CollectFilesFromIntermediateSatelliteAssembliesWithTargetPath</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFromIntermediateSatelliteAssembliesWithTargetPath)"  RunEachTargetSeparately="false" />
  </Target>


  <!--********************************************************************-->
  <!-- Task CollectFilesFromReference -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFromReferenceDependsOn>
      $(OnBeforeCollectFilesFromReference);
      $(CollectFilesFromReferenceDependsOn);
      ResolveProjectReferences;
      ResolveAssemblyReferences;
    </CollectFilesFromReferenceDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFromReference"
          DependsOnTargets="$(CollectFilesFromReferenceDependsOn)">

    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificItemsFromProjectNoDetail"
      ArgumentCount="1"
      Arguments="ReferenceCopyLocalPaths,ReferenceComWrappersToCopyLocal,ResolvedIsolatedComModules,_DeploymentLooseManifestFile,NativeReferenceFile"
      LogType="Message" />

    <ItemGroup>
      <FilesForPackagingFromProject
        Include="@(ReferenceCopyLocalPaths)">
        <DestinationRelativePath>bin\%(ReferenceCopyLocalPaths.DestinationSubDirectory)%(FileName)%(Extension)</DestinationRelativePath>
        <FromTarget>CollectFilesFromReference</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>
    
    <ItemGroup>
      <FilesForPackagingFromProject
        Include="@(ReferenceComWrappersToCopyLocal); 
                @(ResolvedIsolatedComModules); 
                @(_DeploymentLooseManifestFile); 
                @(NativeReferenceFile)">
        <DestinationRelativePath>bin\%(FileName)%(Extension)</DestinationRelativePath>
        <FromTarget>CollectFilesFromReference</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFromReference)" RunEachTargetSeparately="false" />
  </Target>

  <!--********************************************************************-->
  <!-- Task CollectFilesFromAllExtraReferenceFiles -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFromAllExtraReferenceFilesDependsOn>
      $(OnBeforeCollectFilesFromAllExtraReferenceFiles);
      $(CollectFilesFromAllExtraReferenceFilesDependsOn);
    </CollectFilesFromAllExtraReferenceFilesDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFromAllExtraReferenceFiles"
          DependsOnTargets="$(CollectFilesFromAllExtraReferenceFilesDependsOn)">

    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificItemsFromProject"
      ArgumentCount="1"
      Arguments="AllExtraReferenceFiles"
      LogType="Message" />
    <Message Text="@(AllExtraReferenceFiles->'$(OutDir)%(FileName)%(Extension)')" />
    <ItemGroup>
      <FilesForPackagingFromProject  Include="@(AllExtraReferenceFiles->'$(OutDir)%(FileName)%(Extension)')">
        <DestinationRelativePath>bin\%(FileName)%(Extension)</DestinationRelativePath>
        <FromTarget>CollectFilesFromAllExtraReferenceFiles</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFromAllExtraReferenceFiles)" RunEachTargetSeparately="false"/>
  </Target>


  <!--********************************************************************-->
  <!-- Task CollectFilesFrom_SourceItemsToCopyToOutputDirectory -->
  <!--********************************************************************-->
  <PropertyGroup>
    
    <CollectFilesFrom_SourceItemsToCopyToOutputDirectoryDependsOn>
      $(OnBeforeCollectFilesFrom_SourceItemsToCopyToOutputDirectory);
      $(CollectFilesFrom_SourceItemsToCopyToOutputDirectoryDependsOn);
    </CollectFilesFrom_SourceItemsToCopyToOutputDirectoryDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFrom_SourceItemsToCopyToOutputDirectory"
          DependsOnTargets="$(CollectFilesFrom_SourceItemsToCopyToOutputDirectoryDependsOn)"
          Condition="'@(_SourceItemsToCopyToOutputDirectoryAlways)' != '' or '@(_SourceItemsToCopyToOutputDirectory)' != ''">

    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificItemsFromProject"
      ArgumentCount="1"
      Arguments="_SourceItemsToCopyToOutputDirectoryAlways,_SourceItemsToCopyToOutputDirectory"
      LogType="Message" />
    
    <ItemGroup>
      <_TempSourceItemsToCopyToOutputDirectory Include="@(_SourceItemsToCopyToOutputDirectoryAlways); @(_SourceItemsToCopyToOutputDirectory)" />
    </ItemGroup>

    <Message Text="@(_TempSourceItemsToCopyToOutputDirectory->'bin\%(TargetPath)')" />
    <ItemGroup>
      <FilesForPackagingFromProject
        Include="@(_TempSourceItemsToCopyToOutputDirectory)">
        <DestinationRelativePath>bin\%(_TempSourceItemsToCopyToOutputDirectory.TargetPath)</DestinationRelativePath>
        <FromTarget>CollectFilesFrom_SourceItemsToCopyToOutputDirectory</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFrom_SourceItemsToCopyToOutputDirectory)" RunEachTargetSeparately="false" />
  </Target>


  <!--********************************************************************-->
  <!-- Task CollectFilesFromDocFileItem -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFromDocFileItemDependsOn>
      $(OnBeforeCollectFilesFromDocFileItem);
      $(CollectFilesFromDocFileItemDependsOn);
    </CollectFilesFromDocFileItemDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFromDocFileItem"
          DependsOnTargets="$(CollectFilesFromDocFileItemDependsOn)"
          Condition="$(_DocumentationFileProduced)" >

    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificItemsFromProject"
      ArgumentCount="1"
      Arguments="DocFileItem"
      LogType="Message" />
    <Message Text="@(DocFileItem->'bin\%(FileName)%(Extension)')" />
    <ItemGroup>
      <FilesForPackagingFromProject
        Include="@(DocFileItem)">
        <DestinationRelativePath>bin\%(FileName)%(Extension)</DestinationRelativePath>
        <FromTarget>CollectFilesFromDocFileItem</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFromDocFileItem)" RunEachTargetSeparately="false" />
  </Target>

  <!--********************************************************************-->
  <!-- Task CollectFilesFrom_WebApplicationSilverlightXapFiles -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFrom_WebApplicationSilverlightXapFilesDependsOn>
      $(OnBeforeCollectFilesFrom_WebApplicationSilverlightXapFiles);
      $(CollectFilesFrom_WebApplicationSilverlightXapFilesDependsOn);
    </CollectFilesFrom_WebApplicationSilverlightXapFilesDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFrom_WebApplicationSilverlightXapFiles"
          DependsOnTargets="$(CollectFilesFrom_WebApplicationSilverlightXapFilesDependsOn)"
          Condition="'@(_WebApplicationSilverlightXapFiles)'!=''">
    
    <FilterByItems PipelineItems="@(_WebApplicationSilverlightXapFiles)"
                   Filter="@(Content)">
      <Output TaskParameter="OutFilter" ItemName="_WebApplicationSilverlightXapFilesNotInContent"/>
    </FilterByItems>

    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificItemsFromProject"
      ArgumentCount="1"
      Arguments="_WebApplicationSilverlightXapFilesNotInContent"
      LogType="Message" />
    <Message Text="@(_WebApplicationSilverlightXapFilesNotInContent)" />
    <ItemGroup>
      <FilesForPackagingFromProject Include="@(_WebApplicationSilverlightXapFilesNotInContent)">
        <DestinationRelativePath>%(_WebApplicationSilverlightXapFiles.Identity)</DestinationRelativePath>
        <FromTarget>CollectFilesFrom_WebApplicationSilverlightXapFiles</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFrom_WebApplicationSilverlightXapFiles)" RunEachTargetSeparately="false" />
  </Target>

  <!--********************************************************************-->
  <!-- Task CollectFilesFrom_binDeployableAssemblies -->
  <!--********************************************************************-->
  <PropertyGroup>
    <CollectFilesFrom_binDeployableAssembliesDependsOn>
      $(OnBeforeCollectFilesFrom_binDeployableAssemblies);
      $(CollectFilesFrom_binDeployableAssembliesDependsOn);
      _CopyBinDeployableAssemblies;
    </CollectFilesFrom_binDeployableAssembliesDependsOn>
  </PropertyGroup>
  <Target Name="CollectFilesFrom_binDeployableAssemblies"
          DependsOnTargets="$(CollectFilesFrom_binDeployableAssembliesDependsOn)">
    <!--Make sure binDeployableAssemblies is in None group.  This is mainly for the dev10-->
    <FilterByItems PipelineItems="@(_binDeployableAssemblies)"
                   SourceMetadataName="FullPath"
                   Filter="@(None)"
                   FilterMetadataName="FullPath">
      <Output TaskParameter="InFilter" ItemName="_binDeployableAssemblies_None"/>
    </FilterByItems>
    <!--Get Localized string before display message-->
    <GetPublishingLocalizedString
      ID="PublishLocalizedString_GatherSpecificItemsFromProject"
      ArgumentCount="1"
      Arguments="_binDeployableAssemblies"
      LogType="Message" />
    <Message Text="@(_binDeployableAssemblies_None)" />
    
    <ItemGroup>
      <FilesForPackagingFromProject Include="@(_binDeployableAssemblies_None)" Condition="'%(_binDeployableAssemblies_None.DestinationRelPath)'==''">
        <DestinationRelativePath>bin\%(RecursiveDir)%(FileName)%(Extension)</DestinationRelativePath>
        <FromTarget>CollectFilesFrom_binDeployableAssemblies_NoDestinationRelPath</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
      <!--VS11, _binDeployAssembly begin to have %(DestinationRelPath), we should honer that instead of %(recursiveDir)-->
      <FilesForPackagingFromProject Include="@(_binDeployableAssemblies_None)" Condition="'%(_binDeployableAssemblies_None.DestinationRelPath)'!=''">
        <DestinationRelativePath>bin\%(_binDeployableAssemblies_None.DestinationRelPath)%(FileName)%(Extension)</DestinationRelativePath>
        <FromTarget>CollectFilesFrom_binDeployableAssemblies_WithDestinationRelPath</FromTarget>
        <Category>Run</Category>
      </FilesForPackagingFromProject>
    </ItemGroup>
    <CallTarget Targets="$(OnAfterCollectFilesFrom_binDeployableAssemblies)" RunEachTargetSeparately="false" />
  </Target>

  <!--ImportAfter Extension-->
  <PropertyGroup>
    <ImportByWildcardAfterMicrosoftWebPublishingOnlyFilesToRunTheAppTargets Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingOnlyFilesToRunTheAppTargets)'==''">true</ImportByWildcardAfterMicrosoftWebPublishingOnlyFilesToRunTheAppTargets>
  </PropertyGroup>
  <Import Project="$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter\*" Condition="'$(ImportByWildcardAfterMicrosoftWebPublishingOnlyFilesToRunTheAppTargets)' == 'true' and exists('$(MSBuildThisFileDirectory)\$(MSBuildThisFileName)\ImportAfter')"/>

</Project>
