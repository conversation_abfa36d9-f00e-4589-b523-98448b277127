﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using Fragulizer.Common;
using Fragulizer.Models;
using Fragulizer.Models.Partials;
using ToolsFramework;
using ToolsFramework.Mvc;
using Fragulizer.SettingService;
using System.Text;
using System.Drawing;
using System.IO;

namespace Fragulizer.Controllers
{
    public class HomeController : ToolsFrameworkControllerBase
    {
        public bool IsExporting = false;

        public ActionResult Index()
        {
            TimeSpan offset = TimeZone.CurrentTimeZone.GetUtcOffset(DateTime.Now);
            ViewBag.ServerTimeZone = offset.Hours + (float)offset.Minutes / 60;
            ViewBag.PageTimeZone = Utility.GetPageTimeZoneOffSet();
            ViewBag.LatinNumber = Tool.Settings.uselatinnumbers.ToString().ToLower();
            InstrumentGroupToolCompanySetting setting = Services.Get<IInstrumentGroupSettingService>().GetSettings(RequestHelper.CompanyCode);
            string instrumentIds = String.Join(";", setting.InstrumentGroups.Select(p => p.InstrumentIds));
            ViewBag.MarketInfo = Services.Get<IFragulizerRepository>().GetMarketInfo(instrumentIds);

            ViewBag.IsExporting = this.IsExporting;
            string downloadPdfToolUrl = System.Configuration.ConfigurationManager.AppSettings["DownloadURL"];
            if (string.IsNullOrEmpty(downloadPdfToolUrl))
            {
                if(string.Equals(this.Request.Url.Host, "gamma.euroland.com"))
                {
                    downloadPdfToolUrl = string.Concat(this.Request.Url.Scheme, "://", this.Request.Url.Host, "/DownloadPdf2/");
                }
                else
                {
                    downloadPdfToolUrl = string.Concat(this.Request.Url.Scheme, "://www.euroland.com/DownloadPdf2/");
                }
            }

            ViewBag.DownloadPdfToolUrl = downloadPdfToolUrl;

            return View("~/Views/Home/Index.cshtml");
        }

        [HttpPost]
        public JsonResult GetData(bool firstTime, bool takeActivityData, string instrumentIDs, string period, Nullable<DateTime> startDate, Nullable<DateTime> endDate, Nullable<TimeSpan> openTime, Nullable<TimeSpan> closeTime)
        {

            // get datetime to query data rely on period in request
            Utility.GetPeriodDate(period, ref startDate, ref endDate);

            // determine whether continue getting live data
            if (firstTime)
            {
                List<MarketInfo> lstMarketInfo = Services.Get<IFragulizerRepository>().GetMarketInfo(instrumentIDs);
                if (lstMarketInfo.Count > 0)
                {
                    openTime = lstMarketInfo.Min(m => m.OpenTime);
                    closeTime = lstMarketInfo.Max(m => m.CloseTime);
                }
            }
            //DateTime closingTime = DateTime.UtcNow.Date.AddHours(closeTimeSpan.Hours).AddMinutes(closeTimeSpan.Minutes);
            TimeSpan nowTimeSpan = DateTime.UtcNow.Subtract(new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, DateTime.UtcNow.Day));
            bool continueGettingData = (period.ToUpper() == "LIVE" & nowTimeSpan <= closeTime.Value);

            // get data for table of instruments section
            List<InstrumentInfo> latestTradingData = new List<InstrumentInfo>();
            if (Tool.Settings.enableinstrumentstable)
                latestTradingData = Services.Get<IFragulizerRepository>().GetLatestTrading(instrumentIDs);

            LiveData data = Services.Get<IFragulizerRepository>().GetLiveData(instrumentIDs, startDate.Value, endDate.Value, openTime.Value);

            // get data for fragmentation section
            List<Fragmentation> fragmentationData = data.FragmentationData;

            // get data for market share section
            List<MarketShareData> marketShareData = new List<MarketShareData>();
            if (period.ToUpper() == "LIVE")
            {
                DateTime nowInPageTimezone = TimeZoneInfo.ConvertTime(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById(Utility.GetPageTimeZoneID()));
                foreach (InstrumentInfo instrumentInfo in latestTradingData)
                {
                    if (instrumentInfo.Time.Date == nowInPageTimezone.Date)
                    {
                        marketShareData.Add(new MarketShareData()
                        {
                            MarketId = instrumentInfo.MarketId,
                            MarketName = instrumentInfo.MarketName,
                            MarketTranslationID = instrumentInfo.MarketTranslationID,
                            Volume = instrumentInfo.Volume == null ? 0 : instrumentInfo.Volume.Value
                        });
                    }
                }
            }
            if (marketShareData.Count == 0)
            {
                marketShareData = data.MarketShareData;
            }

            // get data for activity trend section
            List<List<ActivityTrendData>> activityTrendData = new List<List<ActivityTrendData>>();
            if (takeActivityData && Tool.Settings.enableactivitytrend)
            {
                activityTrendData = Services.Get<IFragulizerRepository>().GetActivityTrendData(instrumentIDs, Tool.Settings.numberofyearoncolumnchart);
            }

            // message to user
            string message = "";
            bool isPreviousLive = false;
            DateTime liveDate = DateTime.Now.Date;
            if (period.ToLower() == "custom")
            {
                if (fragmentationData != null)
                {
                    foreach (Fragmentation fragmentation in fragmentationData)
                    {
                        if (fragmentation.Data.Count > 0)
                        {
                            if (fragmentation.Data[0].Time.Date == fragmentation.Data[fragmentation.Data.Count - 1].Time.Date)
                            {
                                isPreviousLive = true;
                                liveDate = startDate.Value.Date;
                            }
                            break;
                        }
                    }
                }
            }

            if (period.ToLower() == "live" || isPreviousLive)
            {
                if (fragmentationData != null)
                {
                    foreach (Fragmentation fragmentation in fragmentationData)
                    {
                        if (fragmentation.Data.Count > 0)
                        {
                            TimeSpan t = liveDate.Subtract(fragmentation.Data[0].Time.Date);
                            if (t.Days > 0)
                            {
                                message = string.Format(Translations.NO_DATA_FOUND.ToHtmlString(),
                                                        liveDate.ToString(
                                                            Tool.Culture.DateTimeFormat.ShortDatePattern),
                                                        fragmentation.Data[0].Time.ToString(
                                                            Tool.Culture.DateTimeFormat.ShortDatePattern));
                                continueGettingData = false;
                            }
                            break;
                        }
                    }
                }
                if (firstTime)
                {
                    foreach (InstrumentInfo info in latestTradingData)
                    {
                        info.MarketName = !string.IsNullOrEmpty(Utility.GetCustomMarketName(info.MarketId))
                                              ? Utility.GetCustomMarketName(info.MarketId)
                                              : info.MarketName;
                        info.CurrencyCode = Utility.GetCurrencySymbol(info.CurrencyCode);
                    }
                }
            }
            else
            {
                foreach (InstrumentInfo info in latestTradingData)
                {
                    info.MarketName = !string.IsNullOrEmpty(Utility.GetCustomMarketName(info.MarketId))
                                          ? Utility.GetCustomMarketName(info.MarketId)
                                          : info.MarketName;
                    info.CurrencyCode = Utility.GetCurrencySymbol(info.CurrencyCode);
                }
            }

            //Make notice for latestTradingData table
            string strLatestTradingNotice = string.Empty;
            List<InstrumentInfo> lstLatestTradingToday = new List<InstrumentInfo>();
            latestTradingData.ForEach((s) =>
            {
                TimeSpan t = liveDate.Subtract(s.Time.Date);
                if (t.Days > 0)
                {
                    lstLatestTradingToday.Add(s);
                }
            });

            if (lstLatestTradingToday.Count > 0 && lstLatestTradingToday.Count < latestTradingData.Count)
            {
                strLatestTradingNotice = string.Format(Translations.NO_DATA_FOUND.ToString(),
                    liveDate.ToString(Tool.Culture.DateTimeFormat.ShortDatePattern) + " (" +
                        string.Join(",", lstLatestTradingToday.Select(s => s.MarketName).ToList()) + ")",
                    lstLatestTradingToday.First().Time.ToString(Tool.Culture.DateTimeFormat.ShortDatePattern));
            }

            List<List<ActivityTrendData>> sortedActivititrend = new List<List<ActivityTrendData>>();
            List<short> lstMarket = latestTradingData.Select(t => t.MarketId).Distinct().ToList();
            foreach (short marketID in lstMarket)
            {
                foreach(List<ActivityTrendData> t in activityTrendData)
                {
                    if (t.Count>0 && t.First().MarketId == marketID)
                    {
                        sortedActivititrend.Add(t);
                        break;
                    }
                }
            }

            return Json(new
            {
                IsRetrieveLiveData = continueGettingData, // have next request in 60 seconds or not
                LatestTradingData = latestTradingData, // data for table of instruments section
                FragmentationData = fragmentationData, // data for fragumentation section
                OpenTime = openTime,
                CloseTime = closeTime,
                MarketShareData = marketShareData, // data for market share section
                ActivityTrendData = sortedActivititrend, // data  for activity trend section
                Message = message,
                LatestTradingMessage = strLatestTradingNotice// message send to user
            });
        }

        [HttpPost]
        public JsonResult GetLatestTradingData(string instrumentIDs)
        {
            List<InstrumentInfo> latestTradingData = new List<InstrumentInfo>();
            if (Tool.Settings.enableinstrumentstable)
                latestTradingData = Services.Get<IFragulizerRepository>().GetLatestTrading(instrumentIDs);

            foreach (InstrumentInfo info in latestTradingData)
            {
                info.MarketName = !string.IsNullOrEmpty(Utility.GetCustomMarketName(info.MarketId))
                                      ? Utility.GetCustomMarketName(info.MarketId)
                                      : info.MarketName;
                info.CurrencyCode = Utility.GetCurrencySymbol(info.CurrencyCode);
            }
            DateTime liveDate = DateTime.Now.Date;

            //Make notice for latestTradingData table
            string strLatestTradingNotice = string.Empty;
            List<InstrumentInfo> lstLatestTradingToday = new List<InstrumentInfo>();
            latestTradingData.ForEach((s) =>
            {
                TimeSpan t = liveDate.Subtract(s.Time.Date);
                if (t.Days > 0)
                {
                    lstLatestTradingToday.Add(s);
                }
            });

            if (lstLatestTradingToday.Count > 0 && lstLatestTradingToday.Count < latestTradingData.Count)
            {
                strLatestTradingNotice = string.Format(Translations.NO_DATA_FOUND.ToString(),
                    liveDate.ToString(Tool.Culture.DateTimeFormat.ShortDatePattern) + " (" +
                        string.Join(",", lstLatestTradingToday.Select(s => s.MarketName).ToList()) + ")",
                    lstLatestTradingToday.First().Time.ToString(Tool.Culture.DateTimeFormat.ShortDatePattern));
            }

            return Json(new
                {
                    LatestTradingData = latestTradingData,
                    LatestTradingMessage = strLatestTradingNotice
                });
        }

        public FileResult Wait()
        {
            // Wait for 30 seconds for all activities on the page are completely
            // Client might decide when to stop this request, but, at least wait for 20 sec
            System.Threading.Thread.Sleep(30000);

            string _1pixelBase64 = "R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";
            //System.IO.File.ReadAllBytes(Server.MapPath("~/Content/Images/ChinesXLSs.png"));
            return File(Convert.FromBase64String(_1pixelBase64), "image/png");
        }
    }
}
