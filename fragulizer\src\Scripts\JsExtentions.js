﻿/*
* Binh Nguyen
* 
* Email: <EMAIL>
* Created Date: 
* Last Update: 
* @change: [<EMAIL>][November 24, 2011]
*  - Add roundTo() function to Math object
*/

; (function ($) {
    $.isObject = function (v) {
        return !!v && Object.prototype.toString.call(v) === '[object Object]';
    };
    $.isDate = function (v) {
        return Object.prototype.toString.call(v) === '[object Date]';
    };
    $.isString = function (str) {
        return Object.prototype.toString.call(str) === '[object String]';
    }

    /**
 * jQuery JSON plugin v2.5.1
 * https://github.com/Krinkle/jquery-json
 *
 * <AUTHOR> 2009-2011
 * <AUTHOR> 2011-2014
 * @source This plugin is heavily influenced by MochiKit's serializeJSON, which is
 *         copyrighted 2005 by <PERSON>.
 * @source <PERSON><PERSON><PERSON> wrote this plugin. It is based somewhat on the JSON.org
 *         website's http://www.json.org/json2.js, which proclaims:
 *         "NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.", a sentiment that
 *         I uphold.
 * @license MIT License <http://opensource.org/licenses/MIT>
 */

    var escape = /["\\\x00-\x1f\x7f-\x9f]/g,
		meta = {
		    '\b': '\\b',
		    '\t': '\\t',
		    '\n': '\\n',
		    '\f': '\\f',
		    '\r': '\\r',
		    '"': '\\"',
		    '\\': '\\\\'
		},
		hasOwn = Object.prototype.hasOwnProperty;

    /**
	 * jQuery.toJSON
	 * Converts the given argument into a JSON representation.
	 *
	 * @param o {Mixed} The json-serializable *thing* to be converted
	 *
	 * If an object has a toJSON prototype, that will be used to get the representation.
	 * Non-integer/string keys are skipped in the object, as are keys that point to a
	 * function.
	 *
	 */
    $.toJSON = typeof JSON === 'object' && JSON.stringify ? JSON.stringify : function (o) {
        if (o === null) {
            return 'null';
        }

        var pairs, k, name, val,
			type = $.type(o);

        if (type === 'undefined') {
            return undefined;
        }

        // Also covers instantiated Number and Boolean objects,
        // which are typeof 'object' but thanks to $.type, we
        // catch them here. I don't know whether it is right
        // or wrong that instantiated primitives are not
        // exported to JSON as an {"object":..}.
        // We choose this path because that's what the browsers did.
        if (type === 'number' || type === 'boolean') {
            return String(o);
        }
        if (type === 'string') {
            return $.quoteString(o);
        }
        if (typeof o.toJSON === 'function') {
            return $.toJSON(o.toJSON());
        }
        if (type === 'date') {
            var month = o.getUTCMonth() + 1,
				day = o.getUTCDate(),
				year = o.getUTCFullYear(),
				hours = o.getUTCHours(),
				minutes = o.getUTCMinutes(),
				seconds = o.getUTCSeconds(),
				milli = o.getUTCMilliseconds();

            if (month < 10) {
                month = '0' + month;
            }
            if (day < 10) {
                day = '0' + day;
            }
            if (hours < 10) {
                hours = '0' + hours;
            }
            if (minutes < 10) {
                minutes = '0' + minutes;
            }
            if (seconds < 10) {
                seconds = '0' + seconds;
            }
            if (milli < 100) {
                milli = '0' + milli;
            }
            if (milli < 10) {
                milli = '0' + milli;
            }
            return '"' + year + '-' + month + '-' + day + 'T' +
				hours + ':' + minutes + ':' + seconds +
				'.' + milli + 'Z"';
        }

        pairs = [];

        if ($.isArray(o)) {
            for (k = 0; k < o.length; k++) {
                pairs.push($.toJSON(o[k]) || 'null');
            }
            return '[' + pairs.join(',') + ']';
        }

        // Any other object (plain object, RegExp, ..)
        // Need to do typeof instead of $.type, because we also
        // want to catch non-plain objects.
        if (typeof o === 'object') {
            for (k in o) {
                // Only include own properties,
                // Filter out inherited prototypes
                if (hasOwn.call(o, k)) {
                    // Keys must be numerical or string. Skip others
                    type = typeof k;
                    if (type === 'number') {
                        name = '"' + k + '"';
                    } else if (type === 'string') {
                        name = $.quoteString(k);
                    } else {
                        continue;
                    }
                    type = typeof o[k];

                    // Invalid values like these return undefined
                    // from toJSON, however those object members
                    // shouldn't be included in the JSON string at all.
                    if (type !== 'function' && type !== 'undefined') {
                        val = $.toJSON(o[k]);
                        pairs.push(name + ':' + val);
                    }
                }
            }
            return '{' + pairs.join(',') + '}';
        }
    };

    /**
	 * jQuery.evalJSON
	 * Evaluates a given json string.
	 *
	 * @param str {String}
	 */
    $.evalJSON = typeof JSON === 'object' && JSON.parse ? JSON.parse : function (str) {
        /*jshint evil: true */
        return eval('(' + str + ')');
    };

    /**
	 * jQuery.secureEvalJSON
	 * Evals JSON in a way that is *more* secure.
	 *
	 * @param str {String}
	 */
    $.secureEvalJSON = typeof JSON === 'object' && JSON.parse ? JSON.parse : function (str) {
        var filtered =
			str
			.replace(/\\["\\\/bfnrtu]/g, '@')
			.replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g, ']')
			.replace(/(?:^|:|,)(?:\s*\[)+/g, '');

        if (/^[\],:{}\s]*$/.test(filtered)) {
            /*jshint evil: true */
            return eval('(' + str + ')');
        }
        throw new SyntaxError('Error parsing JSON, source is not valid.');
    };

    /**
	 * jQuery.quoteString
	 * Returns a string-repr of a string, escaping quotes intelligently.
	 * Mostly a support function for toJSON.
	 * Examples:
	 * >>> jQuery.quoteString('apple')
	 * "apple"
	 *
	 * >>> jQuery.quoteString('"Where are we going?", she asked.')
	 * "\"Where are we going?\", she asked."
	 */
    $.quoteString = function (str) {
        if (str.match(escape)) {
            return '"' + str.replace(escape, function (a) {
                var c = meta[a];
                if (typeof c === 'string') {
                    return c;
                }
                c = a.charCodeAt();
                return '\\u00' + Math.floor(c / 16).toString(16) + (c % 16).toString(16);
            }) + '"';
        }
        return '"' + str + '"';
    };
})(jQuery);


(function ($) {
    var nonPrintingCharRe = /[^\x21-\x7E]+/g,
        whitespaceCharRe = /\s+/g;
    /**
    * Remove a css attribute like: 'border', 'font', etc. 
    * @param {Object} $p         A jQuery object of current element
    * @param {String} cssName    The name of css attribute
    */
    function removeCss($el, cssProperties) {
        if (!$el || !cssProperties || cssProperties.length <= 0)
            return;
        var classes = cssProperties.replace(nonPrintingCharRe, ' ').replace(whitespaceCharRe, '').split(',');
        var originalStyle = $el.attr('style');
        if (originalStyle && originalStyle.length > 0) {
            var newStyle = $.grep(originalStyle.split(';'), function (css) {
                return classes.getIndex(function (removed) {
                    return css.toLowerCase().indexOf(removed.toLowerCase()) != -1;
                }) < 0;
            }).join(';');

            if (newStyle == '')
                $el.removeAttr('style');
            else
                $el.attr('style', newStyle);
        }
    };

    $.removeCss = function () {
        removeCss.apply($, arguments);
    };
    $.fn.extend({
        removeCss: function (cssName) {
            // Make it chainable
            return this.each(function () {
                removeCss.apply($, [$(this), cssName]);
            });
        }
    });
})(jQuery);


(function ($) {
    /**
    * Detect whether current device is touch devices
    */
    if ($.touchDevices === undefined) {
        var touchDevices = {
            _match: function (regex) {
                return navigator.userAgent.toLowerCase().match(regex);
            },
            isIpad: function () {
                return this._match(/iPad/i);
            },
            isIphone: function () {
                return this._match(/iPhone/i);
            },
            isIpod: function () {
                return this._match(/iPod/i);
            },
            isAndroid: function () {
                return this._match(/Android/i);
            },
            isBlackBerry: function () {
                return this._match(/BlackBerry/i);
            },
            isWindowsPhone: function () {
                return this._match(/Windows Phone/i);
            },
            isZuneWP7: function () {
                return this._match(/ZuneWP7/i);
            },
            isTouchDevice: function () {
                return this.isIpad()
                    || this.isIphone()
                    || this.isIpod()
                    || this.isAndroid()
                    || this.isBlackBerry()
                    || this.isWindowsPhone()
                    || this.isZuneWP7();
            }
        };

        $.touchDevices = $.extend({}, touchDevices);
    }

    /**
    * Since jQuery verion 1.9, the jQuery.browser is no longer available as its changelog
    */

    // Useragent RegExp
    var rwebkit = /(webkit)[ \/]([\w.]+)/i,
	    ropera = /(opera)(?:.*version)?[ \/]([\w.]+)/i,
	    rmsie = /(msie) ([\w.]+)/i,
	    rmozilla = /(mozilla)(?:.*? rv:([\w.]+))?/i,
		safari = /Version\/([\d\.]+).*(Safari)/i;

    function uaMatch(ua) {
        ua = ua.toLowerCase();

        var match = rwebkit.exec(ua) ||
			ropera.exec(ua) ||
			rmsie.exec(ua) ||
			ua.indexOf("compatible") < 0 && rmozilla.exec(ua) ||
			[];

        if (match.length) {
            return { browser: (match[1] || "").toLowerCase(), version: match[2] || "0" };
        }
        else {
            match = safari.exec(ua);
            return { browser: (match[2] || "").toLowerCase(), version: match[1] || "0" };
        }
    }

    if ($.browser === undefined) {
        $.browser = {
            webkit: false,
            opera: false,
            msie: false,
            mozilla: false,
            safari: false
        };

        var browserMatch = uaMatch(navigator.userAgent);
        if (browserMatch.browser) {
            $.browser[browserMatch.browser] = true;
            $.browser.version = browserMatch.version;
        }

        // Deprecated, use jQuery.browser.webkit instead
        if (jQuery.browser.webkit) {
            jQuery.browser.safari = true;
        }
    }
})(jQuery);

/**
* A extension function for Math object to round a number with a specific number of decimal
* @param {Number} num           Number to round
* @param {Number} decimalNum    Number of decimal to round
*/
Math.roundTo = function (num, decimalNum) {
    if (typeof num === 'number') {
        if (typeof decimalNum !== 'number' || decimalNum === 0) {
            return Math.round(num);
        } else {
            decimalNum = Math.round(Math.abs(decimalNum));
            return Math.round(num * Math.pow(10, decimalNum)) / Math.pow(10, decimalNum);
        }
    }
    return num;
};

/**
* Extension functions for Array
*/
Array.prototype.copy = function () {
    return this.slice(0);
};
Array.prototype.min = function () {
    return Math.min.apply(Math, this);
};
Array.prototype.max = function () {
    return Math.max.apply(Math, this);
};
Array.prototype.sum = function () {
    var sum = 0;
    var length = this.length;
    for (var i = 0; i < length; i++)
        sum += this[i];
    return sum;
};
Array.prototype.getIndex = function (item, start) {
    if (typeof (item) === "undefined") return -1;
    var length = this.length,
        isFunc = typeof (item) === 'function';
    if (length !== 0) {
        start = start - 0;
        if (isNaN(start)) {
            start = 0;
        }
        else {
            if (isFinite(start)) {
                start = start - (start % 1);
            }
            if (start < 0) {
                start = Math.max(0, length + start);
            }
        }

        for (var i = start; i < length; i++) {
            if (typeof (this[i]) !== "undefined") {
                if (isFunc) {
                    var f = item.call(this[i], this[i]);
                    if ((typeof (f) !== 'undefined') && (f === true)) {
                        return i;
                    }
                }
                else {
                    if (this[i] === item)
                        return i;
                }
            }
        }
    }
    return -1;
};
Array.prototype.indexOf = Array.prototype.indexOf || Array.prototype.getIndex;

Array.prototype.clear = function () {
    this.length = 0;
};
Array.prototype.addRange = function (rangeArr) {
    if (rangeArr && rangeArr.constructor.toString().indexOf('Array') != -1) {
        var copy = rangeArr.copy(),
            i = 0,
            len = copy.length;
        for (; i < len; i++)
            this.add(copy[i]);
        copy.clear();
    }
};
Array.prototype.contains = function (item) {
    return this.getIndex(item) >= 0;
};
Array.prototype.remove = function (item) {
    var index = this.getIndex(item);
    if (index >= 0) {
        this.splice(index, 1);
    }
    return (index >= 0);
};
Array.prototype.removeAt = function (index) {
    this.splice(index, 1);
}
Array.prototype.add = Array.prototype.push;

Array.prototype.insert = function (item, index) {
    this.splice(index, 0, item);
};

Array.prototype.each = function (func) {
    if (!$.isFunction(func))
        return;
    for (var i = 0, len = this.length; i < len; i++) {
        //func(i, this[i]);
        if (func.call(this[i], i, this[i]) === false) {
            break;
        }
    }
};

//Array.prototype.forEach = Array.prototype.each;

/**
 * Parse ISO date string (yyyy-mm-ddThh:mm:ss) to Date object
 */
Date.parseIsoDate = function (dateStr) {
    var timebits = /^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2})(?::([0-9]*)(\.[0-9]*)?)?(?:([+-])([0-9]{2})([0-9]{2}))?/;
    var m = timebits.exec(dateStr);
    var resultDate;
    if (m) {
        /*var utcdate = Date.UTC(parseInt(m[1]),
                               parseInt(m[2])-1, // months are zero-offset (!)
                               parseInt(m[3]),
                               parseInt(m[4]), parseInt(m[5]), // hh:mm
                               (m[6] && parseInt(m[6]) || 0),  // optional seconds
                               (m[7] && parseFloat(m[7])*1000) || 0); // optional fraction
        // utcdate is milliseconds since the epoch
        if (m[9] && m[10]) {
            var offsetMinutes = parseInt(m[9]) * 60 + parseInt(m[10]);
            utcdate += (m[8] === '+' ? -1 : +1) * offsetMinutes * 60000;
        }
        resultDate = new Date(utcdate);*/
        resultDate = new Date(parseInt(m[1]),
                               parseInt(m[2]) - 1, // months are zero-offset (!)
                               parseInt(m[3]),
                               parseInt(m[4]), parseInt(m[5]), // hh:mm
                               (m[6] && parseInt(m[6]) || 0),  // optional seconds
                               (m[7] && parseFloat(m[7]) * 1000) || 0); // optional fraction
    } else {
        resultDate = null;
    }
    return resultDate;
};

/**/
/**
* Gets the number of days in the month, given a year and month value. Automatically corrects for LeapYear.
* @param {Number}   The year (0-9999).
* @param {Number}   The month (0-11).
* @return {Number}  The number of days in the month.
*/
Date.getDaysInMonth = function (year, month) {
    return [31, (Date.isLeapYear(year) ? 29 : 28), 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][month];
};
Date.prototype.getDaysInMonth = function () {
    return Date.getDaysInMonth(this.getFullYear(), this.getMonth());
};
Date.prototype.addMilliseconds = function (value) {
    this.setMilliseconds(this.getMilliseconds() + value);
    return this;
};
/**
* Determines if the current date instance is within a LeapYear.
* @param {Number}   The year (0-9999).
* @return {Boolean} true if date is within a LeapYear, otherwise false.
*/
Date.isLeapYear = function (year) {
    return (((year % 4 === 0) && (year % 100 !== 0)) || (year % 400 === 0));
};

Date.prototype.addSeconds = Date.prototype.addSecond || function (second) {
    this.addMilliseconds(second * 1000);
    return this;
};
Date.prototype.addMinutes = Date.prototype.addMinutes || function (minute) {
    this.addMilliseconds(minute * 60000); /* 60*1000 */
    return this;
};
Date.prototype.addHours = Date.prototype.addHours || function (hour) {
    this.addMilliseconds(hour * 3600000); /* 60*60*1000 */
    return this;
};
Date.prototype.addDay = Date.prototype.addDay || function (day) {
    this.addMilliseconds(day * 86400000); /* 60*60*24*1000 */
    return this;
};
Date.prototype.addYear = Date.prototype.addYear || function (year) {
    this.addMonth(year * 12);
    return this;
};
Date.prototype.addMonth = Date.prototype.addMonth || function (month) {
    var n = this.getDate();
    this.setDate(1);
    this.setMonth(this.getMonth() + month);
    this.setDate(Math.min(n, this.getDaysInMonth()));
    return this;
};
Date.prototype.addWeeks = Date.prototype.addWeeks || function (week) {
    return this.addMilliseconds(week * 604800000); /* 60*60*24*7*1000 */
};

Date.prototype.toJSON = function () {
    var month = this.getMonth() + 1;
    if (month < 10)
        month = '0' + month;
    var day = this.getDate();
    if (day < 10)
        day = '0' + day;
    var year = this.getFullYear();
    var hours = this.getHours();

    if (hours < 10)
        hours = '0' + hours;
    var minutes = this.getMinutes();
    if (minutes < 10)
        minutes = '0' + minutes;

    var seconds = this.getSeconds();
    if (seconds < 10)
        seconds = '0' + seconds;
    var milli = this.getMilliseconds();
    if (milli < 100)
        milli = '0' + milli;
    if (milli < 10)
        milli = '0' + milli;
    return year + '-' + month + '-' + day + 'T' + hours + ':' + minutes + ':' + seconds + '.' + milli;
};

/*
* Checks dates equality (ignores time) 
*/
Date.prototype.equalsTo = function (date) {
    return ((this.getFullYear() == date.getFullYear()) &&
		(this.getMonth() == date.getMonth()) &&
		(this.getDate() == date.getDate()) &&
		(this.getHours() == date.getHours()) &&
		(this.getMinutes() == date.getMinutes()));
};

//
// String's extension function
String.prototype.leftPad = function (l, c) { return new Array(l - this.length + 1).join(c || '0') + this; }

String.prototype.format = function () {
    arguments = arguments || [];
    var args = [];
    for (var i = 0, len = arguments.length; i < len; i++)
        args[i] = arguments[i];
    return this.replace(/\{(\d+)\}/g, function (m, i) {
        return args[i];
    });
};

String.prototype.toBase64 = function () {
    var _str = this;
    return StringBase64.encode(_str);
};

String.prototype.fromBase64 = function () {
    var _base64Str = this;
    return StringBase64.decode(_base64Str);
};
/**
*
*  StringBase64 encode / decode
*  http://www.webtoolkit.info/
*
**/

var StringBase64 = {

    // private property
    _keyStr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",

    // public method for encoding
    encode: function (input) {
        var output = "";
        var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
        var i = 0;

        input = StringBase64._utf8_encode(input);

        while (i < input.length) {

            chr1 = input.charCodeAt(i++);
            chr2 = input.charCodeAt(i++);
            chr3 = input.charCodeAt(i++);

            enc1 = chr1 >> 2;
            enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
            enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
            enc4 = chr3 & 63;

            if (isNaN(chr2)) {
                enc3 = enc4 = 64;
            } else if (isNaN(chr3)) {
                enc4 = 64;
            }

            output = output +
			this._keyStr.charAt(enc1) + this._keyStr.charAt(enc2) +
			this._keyStr.charAt(enc3) + this._keyStr.charAt(enc4);

        }

        return output;
    },

    // public method for decoding
    decode: function (input) {
        var output = "";
        var chr1, chr2, chr3;
        var enc1, enc2, enc3, enc4;
        var i = 0;

        input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");

        while (i < input.length) {

            enc1 = this._keyStr.indexOf(input.charAt(i++));
            enc2 = this._keyStr.indexOf(input.charAt(i++));
            enc3 = this._keyStr.indexOf(input.charAt(i++));
            enc4 = this._keyStr.indexOf(input.charAt(i++));

            chr1 = (enc1 << 2) | (enc2 >> 4);
            chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
            chr3 = ((enc3 & 3) << 6) | enc4;

            output = output + String.fromCharCode(chr1);

            if (enc3 != 64) {
                output = output + String.fromCharCode(chr2);
            }
            if (enc4 != 64) {
                output = output + String.fromCharCode(chr3);
            }

        }

        output = StringBase64._utf8_decode(output);

        return output;

    },

    // private method for UTF-8 encoding
    _utf8_encode: function (string) {
        string = string.replace(/\r\n/g, "\n");
        var utftext = "";

        for (var n = 0; n < string.length; n++) {

            var c = string.charCodeAt(n);

            if (c < 128) {
                utftext += String.fromCharCode(c);
            }
            else if ((c > 127) && (c < 2048)) {
                utftext += String.fromCharCode((c >> 6) | 192);
                utftext += String.fromCharCode((c & 63) | 128);
            }
            else {
                utftext += String.fromCharCode((c >> 12) | 224);
                utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                utftext += String.fromCharCode((c & 63) | 128);
            }

        }

        return utftext;
    },

    // private method for UTF-8 decoding
    _utf8_decode: function (utftext) {
        var string = "";
        var i = 0;
        var c = c1 = c2 = 0;

        while (i < utftext.length) {

            c = utftext.charCodeAt(i);

            if (c < 128) {
                string += String.fromCharCode(c);
                i++;
            }
            else if ((c > 191) && (c < 224)) {
                c2 = utftext.charCodeAt(i + 1);
                string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
                i += 2;
            }
            else {
                c2 = utftext.charCodeAt(i + 1);
                c3 = utftext.charCodeAt(i + 2);
                string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
                i += 3;
            }
        }
        return string;
    }
};

/**
* Returns an HTML fragment of html template with the specified values applied.
* @html: a html string (i.e. <div>{foo}</div> or <div>{0}</div>)
* @values
* The template values. Can be an array if the params are numeric (i.e. {0})
* or an object (i.e. {foo: 'bar'}).
* @return {String} The HTML fragment
*/
String.applyTemplate = function (html, values) {
    var reg = /\{([\w-]+)\}/g;
    if (typeof html == 'undefined')
        return '';

    if ($.isArray(html))
        html = html.join('');

    if (typeof (html) === 'string' && ($.isArray(values) || $.isObject(values))) {
        return html.replace(reg, function (m, name) {
            return values[name] !== undefined ? values[name] : "";
        });
    }
    return html;
};

String.formUnescape = function (formValue) {
    if (!$.isString(formValue))
        throw 'String.formUnescape(): formValue is not a string';
    return unescape(formValue);
}

/**
 * [14-Jul-2016 binh.nguyen] Added new utility function:
 * 
 * Escape a form value string to prevent posting "Potentially dangerous request" but whithout 
 * escaping unicode characters. This is to overcome default "escape()" function 
 * which escape all.
 * 
 * List of string will cause 'Potentially dangerous request'
 * 		<(any a-z character)  
 *		<!  
 *		</
 *		<?
 *		&#
 *
 */
String.formEscape = function (formValue) {
    if (!$.isString(formValue))
        throw 'String.formEscape(): formValue is not a string';
    return formValue.replace(/(\<[a-zA-Z!/?]|&#)/ig, function (match, capture) {
        return escape(capture);
    });
}

/**
* The pair of number between arabic-latin numbers
* @author: <EMAIL>
* @date: 18-June-2012
*/
String.arabic_latin_number = [
    ['0', '٠'],
    ['1', '١'], ['2', '٢'], ['3', '٣'],
    ['4', '٤'], ['5', '٥'], ['6', '٦'],
    ['7', '٧'], ['8', '٨'], ['9', '٩']
];

/**
* Convert a number (or string of number) to arabic digits
* @param {String, Number} value - The input value to convert
* @return {String}
* @author: <EMAIL>
* @date: 18-June-2012
*/
String.toArabicNumber = function (value) {
    var str = '' + value;
    var pair = String.arabic_latin_number.copy();
    for (var i = 0; i < pair.length; i++) {
        str = str.replace(new RegExp(pair[i][0], 'ig'), pair[i][1]);
    }

    return str;
};

/**
* Convert a number (or string of number) with arabic digits to latin digits
* @param {String, Number} value - The input value to convert
* @return {String}
* @author: <EMAIL>
* @date: 18-June-2012
*/
String.fromArabicNumber = function (value) {
    var str = '' + value;
    var pair = String.arabic_latin_number.copy();
    for (var i = 0; i < pair.length; i++) {
        str = str.replace(new RegExp(pair[i][1], 'ig'), pair[i][0]);
    }

    return str;
};

/**
* Replace all match characters match with new characters
* @param {String} oldString - Old string to be removed
* @param {String} newString - New string to replace with
* @return {String}
* @author: <EMAIL>
* @date: 18-June-2012
*/
String.prototype.replaceAll = function (oldString, newString) {
    newString = newString || '';
    return this.split(oldString).join(newString);
};

/**
* Convert a JSON Date string of Microsoft JavascriptSerializer to Date object
*/
String.prototype.toDate = function () {
    var usDate = null;
    var msDateString = this;
    var msDateRegex = new RegExp('\\/Date\\(([-+])?(\\d+)(?:[+-]\\d{4})?\\)\\/');
    if (msDateRegex.test(msDateString)) {
        var r = (msDateString || '').match(msDateRegex);
        usDate = r ? new Date(((r[1] || '') + r[2]) * 1) : null;
    }
    return usDate;
};

if (!String.prototype.endsWith) {
    String.prototype.endsWith = function (searchString, position) {
        var subjectString = this.toString();
        if (position === undefined || position > subjectString.length) {
            position = subjectString.length;
        }
        position -= searchString.length;
        var lastIndex = subjectString.indexOf(searchString, position);
        return lastIndex !== -1 && lastIndex === position;
    };
}

if (!String.prototype.startsWith) {
    String.prototype.startsWith = function (searchString, position) {
        position = position || 0;
        return this.lastIndexOf(searchString, position) === position;
    };
}