body {
    font-size: 16px;
    }
    @font-face {
    font-family: Montserrat;
    font-style: normal;
    font-weight: normal;
    src: url(https://staticpacific.blob.core.windows.net/opifex3-dev/font/s-volv/montserrat-regular.ttf);
    }
    .tabs-control .tabs {
    display: flex;
    }
    .tabs-control .tabs .tabItem {
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    border-top: unset;
    border-bottom: unset;
    border-left: unset;
    border-right: unset;
    }
    .tabs-control .tabs .active, .tabs-control .tabs .tabItem:hover {
    border-top: unset;
    border-bottom: unset;
    border-left: unset;
    border-right: unset;
    }
    .table-share tbody tr {
    border: unset;
    }
    .parameter-item-label label, .parameter-item label, .footer {
    font-size: 16px;
    }
    .share-selection .table-share {
    border-bottom-left-radius: unset;
    border-bottom-right-radius: unset;
    border-top-left-radius: unset;
    border-top-right-radius: unset;
    }
    a {
    text-decoration: unset;
    }
    .parameter-item input {
    padding-left: 9px;
    padding-right: 9px;
    padding-top: 11.5px;
    padding-bottom: 11.5px;
    border-bottom-left-radius: unset;
    border-bottom-right-radius: unset;
    border-top-left-radius: unset;
    border-top-right-radius: unset;
    }
    .currency-selection .ac_input {
    padding-left: 9px;
    padding-right: 9px;
    padding-top: 11.5px;
    padding-bottom: 11.5px;
    border-bottom-width: 1px;
    border-bottom-left-radius: unset;
    border-bottom-right-radius: unset;
    border-top-left-radius: unset;
    border-top-right-radius: unset;
    }
    .data-button-wrapper .data-button {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top: unset;
    border-bottom: unset;
    border-left: unset;
    border-right: unset;
    }
    .calculation-parameters .EUCalendar .EUCalendar-hover-date {
    border: unset;
    }
    .calculation-parameters .EUCalendar .EUCalendar-bottomBar .EUCalendar-bottomBar-today, .calculation-parameters .EUCalendar .EUCalendar-menu-today {
    border-bottom-left-radius: unset;
    border-bottom-right-radius: unset;
    border-top-left-radius: unset;
    border-top-right-radius: unset;
    border-top: unset;
    border-bottom: unset;
    border-left: unset;
    border-right: unset;
    }
    .calculation-parameters .EUCalendar .EUCalendar-bottomBar .EUCalendar-bottomBar-today-hover, .calculation-parameters .EUCalendar .EUCalendar-menu-today-hover {
    border-bottom-left-radius: unset;
    border-bottom-right-radius: unset;
    border-top-left-radius: unset;
    border-top-right-radius: unset;
    border-top: unset;
    border-bottom: unset;
    border-left: unset;
    border-right: unset;
    }
    