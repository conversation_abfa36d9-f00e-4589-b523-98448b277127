﻿using System.Linq;
using System.Web;
using System.Web.Mvc;
using Fragulizer.Common;
using System.Drawing;
using System.IO;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.HSSF.Util;
using NPOI.SS.Util;
using System.Text.RegularExpressions;
using Fragulizer.Models.Partials;
using ToolsFramework;
using Fragulizer.Models;
using System;
using System.Collections.Generic;
using System.Drawing.Imaging;
using System.Diagnostics;
using System.Configuration;
using Fragulizer.SettingService;

namespace Fragulizer.Controllers
{
	public class ExportController : Controller
	{
		public ActionResult FileType()
		{
			HomeController homeController = new HomeController();
			homeController.ControllerContext = new ControllerContext(this.HttpContext, this.RouteData, homeController);
			homeController.IsExporting = true;

			return homeController.Index();
		}
		public ActionResult Excel(string shareName, string instrumentIDs, string period, Nullable<DateTime> startDate, Nullable<DateTime> endDate)
		{
			if (string.IsNullOrEmpty(instrumentIDs))
				return new HttpStatusCodeResult(System.Net.HttpStatusCode.BadRequest);

			shareName = GetShareNameByInstrumentIDs(instrumentIDs);
			if (string.IsNullOrEmpty(shareName))
				return new HttpStatusCodeResult(System.Net.HttpStatusCode.BadRequest);

			Utility.GetPeriodDate(period, ref startDate, ref endDate);

			List<InstrumentInfo> latestTrading = Services.Get<IFragulizerRepository>().GetLatestTrading(instrumentIDs);
			List<MarketInfo> lstMarketInfo = Services.Get<IFragulizerRepository>().GetMarketInfo(instrumentIDs);
			TimeSpan openTime = lstMarketInfo.Min(m => m.OpenTime);
			List<MarketShareData> marketShareData = Services.Get<IFragulizerRepository>().GetLiveData(instrumentIDs, startDate.Value, endDate.Value, openTime).MarketShareData.ToList();
			List<List<ActivityTrendData>> activityTrendData = Services.Get<IFragulizerRepository>().GetActivityTrendData(instrumentIDs, Tool.Settings.numberofyearoncolumnchart);

			//Define workbook for data excel
			HSSFWorkbook workbook = new HSSFWorkbook(); ;
			//Create excel sheet 
			ISheet datasheet = workbook.CreateSheet("Fragulizer");
			IFont fontArial = workbook.CreateFont();
			fontArial.FontName = "Arial";
			fontArial.FontHeightInPoints = 10;

			IFont fontArialBold = workbook.CreateFont();
			fontArialBold.FontName = "Arial";
			fontArialBold.Boldweight = 700;
			fontArialBold.FontHeightInPoints = 10;

			IFont fontTitle = workbook.CreateFont();
			fontTitle.FontName = "Arial";
			fontTitle.Boldweight = 700;
			fontTitle.FontHeightInPoints = 12;

			IFont fontIncrease = workbook.CreateFont();
			fontIncrease.FontName = "Arial";
			fontIncrease.FontHeightInPoints = 10;
			fontIncrease.Color = HSSFColor.GREEN.index;

			IFont fontDecrease = workbook.CreateFont();
			fontDecrease.FontName = "Arial";
			fontDecrease.FontHeightInPoints = 10;
			fontDecrease.Color = HSSFColor.RED.index;

			//Define style for cell
			ICellStyle titleStyle = workbook.CreateCellStyle();
			titleStyle.SetFont(fontTitle);

			// Header row
			ICellStyle styleHeader = workbook.CreateCellStyle();
			styleHeader.SetFont(fontArialBold);
			styleHeader.Alignment = HorizontalAlignment.LEFT;
			styleHeader.VerticalAlignment = VerticalAlignment.CENTER;
			styleHeader.BorderTop = BorderStyle.THIN;
			styleHeader.BorderBottom = BorderStyle.THIN;
			styleHeader.BorderLeft = BorderStyle.THIN;
			styleHeader.BorderRight = BorderStyle.THIN;
			styleHeader.WrapText = true;

			ICellStyle styleTableHeader = workbook.CreateCellStyle();
			styleTableHeader.SetFont(fontArialBold);
			styleTableHeader.Alignment = HorizontalAlignment.CENTER;
			styleTableHeader.VerticalAlignment = VerticalAlignment.CENTER;
			styleTableHeader.BorderTop = BorderStyle.THIN;
			styleTableHeader.BorderBottom = BorderStyle.THIN;
			styleTableHeader.BorderLeft = BorderStyle.THIN;
			styleTableHeader.BorderRight = BorderStyle.THIN;
			styleTableHeader.FillPattern = FillPatternType.SOLID_FOREGROUND;
			styleTableHeader.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LIGHT_GREEN.index;
			styleTableHeader.WrapText = true;

			ICellStyle cellStyle = workbook.CreateCellStyle();
			cellStyle.VerticalAlignment = VerticalAlignment.CENTER;
			cellStyle.BorderTop = BorderStyle.THIN;
			cellStyle.BorderRight = BorderStyle.THIN;
			cellStyle.BorderBottom = BorderStyle.THIN;
			cellStyle.BorderLeft = BorderStyle.THIN;

			ICellStyle cellStyleAlignRight = workbook.CreateCellStyle();
			cellStyleAlignRight.VerticalAlignment = VerticalAlignment.CENTER;
			cellStyleAlignRight.Alignment = HorizontalAlignment.RIGHT;
			cellStyleAlignRight.BorderTop = BorderStyle.THIN;
			cellStyleAlignRight.BorderRight = BorderStyle.THIN;
			cellStyleAlignRight.BorderBottom = BorderStyle.THIN;
			cellStyleAlignRight.BorderLeft = BorderStyle.THIN;

			ICellStyle cellStyleAlignRightWithoutBorder = workbook.CreateCellStyle();
			cellStyleAlignRightWithoutBorder.VerticalAlignment = VerticalAlignment.CENTER;
			cellStyleAlignRightWithoutBorder.Alignment = HorizontalAlignment.RIGHT;

			ICellStyle cellBoldStyle = workbook.CreateCellStyle();
			cellBoldStyle.VerticalAlignment = VerticalAlignment.CENTER;
			cellBoldStyle.BorderTop = BorderStyle.THIN;
			cellBoldStyle.BorderRight = BorderStyle.THIN;
			cellBoldStyle.BorderBottom = BorderStyle.THIN;
			cellBoldStyle.BorderLeft = BorderStyle.THIN;
			cellBoldStyle.SetFont(fontArialBold);

			ICellStyle floatCellStyle = workbook.CreateCellStyle();
			floatCellStyle.VerticalAlignment = VerticalAlignment.CENTER;
			//floatCellStyle.DataFormat = (short)4;
			floatCellStyle.DataFormat = HSSFDataFormat.GetBuiltinFormat(Utility.GetExcelDecimalFormat());
			floatCellStyle.BorderTop = BorderStyle.THIN;
			floatCellStyle.BorderRight = BorderStyle.THIN;
			floatCellStyle.BorderBottom = BorderStyle.THIN;
			floatCellStyle.BorderLeft = BorderStyle.THIN;

			ICellStyle floatCellIncreaseStyle = workbook.CreateCellStyle();
			floatCellIncreaseStyle.VerticalAlignment = VerticalAlignment.CENTER;
			floatCellIncreaseStyle.SetFont(fontIncrease);
			//floatCellIncreaseStyle.DataFormat = (short)4;
			floatCellIncreaseStyle.DataFormat = HSSFDataFormat.GetBuiltinFormat(Utility.GetExcelDecimalFormat());
			floatCellIncreaseStyle.BorderTop = BorderStyle.THIN;
			floatCellIncreaseStyle.BorderRight = BorderStyle.THIN;
			floatCellIncreaseStyle.BorderBottom = BorderStyle.THIN;
			floatCellIncreaseStyle.BorderLeft = BorderStyle.THIN;

			ICellStyle floatCellDecreaseStyle = workbook.CreateCellStyle();
			floatCellDecreaseStyle.VerticalAlignment = VerticalAlignment.CENTER;
			floatCellDecreaseStyle.SetFont(fontDecrease);
			floatCellDecreaseStyle.DataFormat = HSSFDataFormat.GetBuiltinFormat(Utility.GetExcelDecimalFormat());
			//floatCellDecreaseStyle.DataFormat = (short)4;
			floatCellDecreaseStyle.BorderTop = BorderStyle.THIN;
			floatCellDecreaseStyle.BorderRight = BorderStyle.THIN;
			floatCellDecreaseStyle.BorderBottom = BorderStyle.THIN;
			floatCellDecreaseStyle.BorderLeft = BorderStyle.THIN;

			ICellStyle intCellStyle = workbook.CreateCellStyle();
			intCellStyle.VerticalAlignment = VerticalAlignment.CENTER;
			intCellStyle.DataFormat = HSSFDataFormat.GetBuiltinFormat(Utility.GetExcelIntFormat());
			//intCellStyle.DataFormat = HSSFDataFormat.GetBuiltinFormat("#,##0");
			intCellStyle.BorderTop = BorderStyle.THIN;
			intCellStyle.BorderRight = BorderStyle.THIN;
			intCellStyle.BorderBottom = BorderStyle.THIN;
			intCellStyle.BorderLeft = BorderStyle.THIN;

			datasheet.SetColumnWidth(0, 100 * 35);
			datasheet.SetColumnWidth(1, 100 * 35);
			datasheet.SetColumnWidth(2, 100 * 35);
			datasheet.SetColumnWidth(3, 100 * 35);
			datasheet.SetColumnWidth(4, 100 * 35);
			datasheet.SetColumnWidth(5, 100 * 35);
			datasheet.SetColumnWidth(6, 100 * 35);
			datasheet.SetColumnWidth(7, 100 * 45);

			//Header
			IRow titleRow = datasheet.CreateRow(0);
			titleRow.HeightInPoints = 20;
			titleRow.CreateCell(0).SetCellValue(Translations.HEADING_TEXT.ToHtmlString());
			titleRow.Cells[0].CellStyle = titleStyle;
			if (!Tool.Settings.showcurrencycolumn)
			{
				datasheet.AddMergedRegion(CellRangeAddress.ValueOf("A1:G1"));
			}
			else
			{
				datasheet.AddMergedRegion(CellRangeAddress.ValueOf("A1:H1"));
			}

			IRow spaceRow1 = datasheet.CreateRow(1);
			spaceRow1.HeightInPoints = 16;
			spaceRow1.CreateCell(0).SetCellValue("");

			//Share
			IRow shareRow = datasheet.CreateRow(2);
			shareRow.HeightInPoints = 16;
			shareRow.CreateCell(0).SetCellValue(Translations.SHARE.ToHtmlString());
			shareRow.Cells[0].CellStyle = styleHeader;
			shareRow.CreateCell(1).SetCellValue(shareName);
			shareRow.Cells[1].CellStyle = styleHeader;
			if (!Tool.Settings.showcurrencycolumn)
			{
				datasheet.AddMergedRegion(CellRangeAddress.ValueOf("B3:G3"));
				((HSSFSheet)datasheet).SetEnclosedBorderOfRegion(CellRangeAddress.ValueOf("B3:G3"), BorderStyle.THIN, NPOI.HSSF.Util.HSSFColor.BLACK.index);
			}
			else
			{
				datasheet.AddMergedRegion(CellRangeAddress.ValueOf("B3:H3"));
				((HSSFSheet)datasheet).SetEnclosedBorderOfRegion(CellRangeAddress.ValueOf("B3:H3"), BorderStyle.THIN, NPOI.HSSF.Util.HSSFColor.BLACK.index);
			}

			//Date
			IRow dateRow = datasheet.CreateRow(3);
			dateRow.HeightInPoints = 16;
			dateRow.CreateCell(0).SetCellValue(Translations.DATE.ToHtmlString());
			dateRow.Cells[0].CellStyle = styleHeader;
			dateRow.CreateCell(1).SetCellValue(
					TimeZoneInfo.ConvertTime(DateTime.UtcNow, TimeZoneInfo.Utc, TimeZoneInfo.FindSystemTimeZoneById(Utility.GetPageTimeZoneID()))
					.ToString("dddd, "
								+ Tool.Culture.DateTimeFormat.LongDatePattern + " "
								+ Tool.Culture.DateTimeFormat.ShortTimePattern) +
								" (" + Utility.GetCurrentTimezone() + ")"
				);
			dateRow.Cells[1].CellStyle = styleHeader;
			if (!Tool.Settings.showcurrencycolumn)
			{
				datasheet.AddMergedRegion(CellRangeAddress.ValueOf("B4:G4"));
				((HSSFSheet)datasheet).SetEnclosedBorderOfRegion(CellRangeAddress.ValueOf("B4:G4"), BorderStyle.THIN, NPOI.HSSF.Util.HSSFColor.BLACK.index);
			}
			else
			{
				datasheet.AddMergedRegion(CellRangeAddress.ValueOf("B4:H4"));
				((HSSFSheet)datasheet).SetEnclosedBorderOfRegion(CellRangeAddress.ValueOf("B4:H4"), BorderStyle.THIN, NPOI.HSSF.Util.HSSFColor.BLACK.index);
			}

			//Table header
			int cellIndex = 0;
			IRow fragulizerTableHeader = datasheet.CreateRow(4);
			fragulizerTableHeader.HeightInPoints = 16;
			fragulizerTableHeader.CreateCell(cellIndex).SetCellValue(Translations.MARKET.ToHtmlString());
			fragulizerTableHeader.Cells[cellIndex].CellStyle = styleTableHeader;
			if (Tool.Settings.showcurrencycolumn)
			{
				cellIndex++;
				fragulizerTableHeader.CreateCell(cellIndex).SetCellValue(Translations.CURRENCY.ToHtmlString());
				fragulizerTableHeader.Cells[cellIndex].CellStyle = styleTableHeader;
			}
			cellIndex++;
			fragulizerTableHeader.CreateCell(cellIndex).SetCellValue(Translations.LAST.ToHtmlString());
			fragulizerTableHeader.Cells[cellIndex].CellStyle = styleTableHeader;
			cellIndex++;
			fragulizerTableHeader.CreateCell(cellIndex).SetCellValue(Translations.CHANGE.ToHtmlString());
			fragulizerTableHeader.Cells[cellIndex].CellStyle = styleTableHeader;
			cellIndex++;
			fragulizerTableHeader.CreateCell(cellIndex).SetCellValue(Translations.HIGH.ToHtmlString());
			fragulizerTableHeader.Cells[cellIndex].CellStyle = styleTableHeader;
			cellIndex++;
			fragulizerTableHeader.CreateCell(cellIndex).SetCellValue(Translations.LOW.ToHtmlString());
			fragulizerTableHeader.Cells[cellIndex].CellStyle = styleTableHeader;
			cellIndex++;
			fragulizerTableHeader.CreateCell(cellIndex).SetCellValue(Translations.VOLUME.ToHtmlString());
			fragulizerTableHeader.Cells[cellIndex].CellStyle = styleTableHeader;
			cellIndex++;
			fragulizerTableHeader.CreateCell(cellIndex).SetCellValue(Translations.TIME.ToHtmlString());
			fragulizerTableHeader.Cells[cellIndex].CellStyle = styleTableHeader;

			int currentRowIndex = 5;
			cellIndex = 0;
			foreach (InstrumentInfo ins in latestTrading)
			{
				IRow fragulizerTableRow = datasheet.CreateRow(currentRowIndex);
				fragulizerTableRow.HeightInPoints = 16;
				ICell fraglizeMarketName = fragulizerTableRow.CreateCell(cellIndex);
				fraglizeMarketName.SetCellValue(string.IsNullOrEmpty(Utility.GetCustomMarketName(ins.MarketId)) ? ins.MarketName : Utility.GetCustomMarketName(ins.MarketId));
				fraglizeMarketName.CellStyle = cellStyle;
				if (Tool.Settings.showcurrencycolumn)
				{
					cellIndex++;
					ICell fraglizerCurrency = fragulizerTableRow.CreateCell(cellIndex);
					fraglizerCurrency.SetCellValue(ins.CurrencyCode);
					fraglizerCurrency.CellStyle = cellStyle;
				}
				cellIndex++;
				ICell fraglizerLast = fragulizerTableRow.CreateCell(cellIndex);
				fraglizerLast.SetCellValue((double)ins.Last);
				fraglizerLast.CellStyle = floatCellStyle;
				cellIndex++;
				ICell fraglizerChangePro = fragulizerTableRow.CreateCell(cellIndex);
				fraglizerChangePro.SetCellValue((double)ins.ChangePro);
				fraglizerChangePro.CellStyle = floatCellStyle;
				cellIndex++;
				ICell fragulizerHigh = fragulizerTableRow.CreateCell(cellIndex);
				fragulizerHigh.SetCellValue((double)ins.High);
				fragulizerHigh.CellStyle = floatCellStyle;
				cellIndex++;
				ICell fragulizerLow = fragulizerTableRow.CreateCell(cellIndex);
				fragulizerLow.SetCellValue((double)ins.Low);
				fragulizerLow.CellStyle = floatCellStyle;
				cellIndex++;
				ICell fragulizerVolume = fragulizerTableRow.CreateCell(cellIndex);
				fragulizerVolume.SetCellValue((double)ins.Volume);
				fragulizerVolume.CellStyle = intCellStyle;
				cellIndex++;
				ICell fragulizerTime = fragulizerTableRow.CreateCell(cellIndex);
				if (ins.Time.Date == DateTime.Now.Date)
				{
					fragulizerTime.SetCellValue(ins.Time.ToString(Tool.Culture.DateTimeFormat.ShortTimePattern));
				}
				else
				{
					fragulizerTime.SetCellValue(ins.Time.ToString(Tool.Culture.DateTimeFormat.ShortDatePattern) + " " + ins.Time.ToString(Tool.Culture.DateTimeFormat.ShortTimePattern));
				}
				fragulizerTime.CellStyle = cellStyle;
				currentRowIndex++;

				cellIndex = 0;
			}

			//Add space row
			IRow spaceRow2 = datasheet.CreateRow(currentRowIndex);
			spaceRow2.HeightInPoints = 16;
			spaceRow2.CreateCell(0).SetCellValue("");

			currentRowIndex++;

			// Market Share Table
			IRow marketShareTitleRow = datasheet.CreateRow(currentRowIndex);
			marketShareTitleRow.HeightInPoints = 16;
			marketShareTitleRow.CreateCell(0).SetCellValue(Translations.MARKET_SHARE.ToHtmlString());
			marketShareTitleRow.Cells[0].CellStyle = titleStyle;
			datasheet.AddMergedRegion(CellRangeAddress.ValueOf("A" + (currentRowIndex + 1) + ":C" + (currentRowIndex + 1)));

			currentRowIndex++;

			//Market share table header
			IRow periodRow = datasheet.CreateRow(currentRowIndex);
			periodRow.HeightInPoints = 16;
			periodRow.CreateCell(0).SetCellValue("Period");
			periodRow.Cells[0].CellStyle = styleHeader;
			periodRow.CreateCell(1).SetCellValue(startDate.Value.ToString(Tool.Culture.DateTimeFormat.ShortDatePattern) + " - " + endDate.Value.ToString(Tool.Culture.DateTimeFormat.ShortDatePattern));
			periodRow.Cells[1].CellStyle = styleHeader;
			datasheet.AddMergedRegion(CellRangeAddress.ValueOf("B" + (currentRowIndex + 1) + ":C" + (currentRowIndex + 1)));
			((HSSFSheet)datasheet).SetEnclosedBorderOfRegion(CellRangeAddress.ValueOf("B" + (currentRowIndex + 1) + ":C" + (currentRowIndex + 1)), BorderStyle.THIN, NPOI.HSSF.Util.HSSFColor.BLACK.index);
			currentRowIndex++;

			IRow marketShareTableHeader = datasheet.CreateRow(currentRowIndex);
			marketShareTableHeader.HeightInPoints = 16;
			marketShareTableHeader.CreateCell(0).SetCellValue(Translations.MARKET.ToHtmlString());
			marketShareTableHeader.Cells[0].CellStyle = styleTableHeader;
			marketShareTableHeader.CreateCell(1).SetCellValue(Translations.VOLUME.ToHtmlString());
			marketShareTableHeader.Cells[1].CellStyle = styleTableHeader;
			marketShareTableHeader.CreateCell(2).SetCellValue(Translations.PERCENTAGE.ToHtmlString());
			marketShareTableHeader.Cells[2].CellStyle = styleTableHeader;
			currentRowIndex++;

			//Build market share row
			long totalVolume = marketShareData.Sum(m => m.Volume);
			foreach (MarketShareData marketShare in marketShareData)
			{
				IRow marketShareTableRow = datasheet.CreateRow(currentRowIndex);
				marketShareTableRow.HeightInPoints = 16;
				marketShareTableRow.CreateCell(0).SetCellValue(string.IsNullOrEmpty(Utility.GetCustomMarketName(marketShare.MarketId)) ? marketShare.MarketName : Utility.GetCustomMarketName(marketShare.MarketId));
				marketShareTableRow.Cells[0].CellStyle = cellStyle;
				marketShareTableRow.CreateCell(1).SetCellValue(marketShare.Volume);
				marketShareTableRow.Cells[1].CellStyle = intCellStyle;
				marketShareTableRow.CreateCell(2).SetCellValue(Math.Round(((float)marketShare.Volume * 100 / totalVolume), Tool.Settings.sharepricepercentagedecimaldigits));
				marketShareTableRow.Cells[2].CellStyle = floatCellStyle;

				currentRowIndex++;
			}

			if (Tool.Settings.enableactivitytrend)
			{
				//Space
				IRow spaceRow3 = datasheet.CreateRow(currentRowIndex);
				spaceRow3.HeightInPoints = 16;
				spaceRow3.CreateCell(0).SetCellValue("");
				currentRowIndex++;

				//Activity trend header
				int startColumn = (int)'A';
				int endColumn = startColumn + Tool.Settings.numberofyearoncolumnchart;

				IRow activityTrendTitle = datasheet.CreateRow(currentRowIndex);
				activityTrendTitle.HeightInPoints = 16;
				activityTrendTitle.CreateCell(0).SetCellValue(Translations.ACTIVITY_TREND.ToHtmlString());
				activityTrendTitle.Cells[0].CellStyle = titleStyle;
				datasheet.AddMergedRegion(CellRangeAddress.ValueOf("A" + (currentRowIndex + 1) + ":" + ((char)endColumn).ToString() + (currentRowIndex + 1)));
				currentRowIndex++;

				//Activity trend table header
				IRow activityTrendTableHeader = datasheet.CreateRow(currentRowIndex);
				activityTrendTableHeader.HeightInPoints = 16;
				activityTrendTableHeader.CreateCell(0).SetCellValue(Translations.MARKET.ToHtmlString());
				activityTrendTableHeader.Cells[0].CellStyle = styleTableHeader;
				for (int i = Tool.Settings.numberofyearoncolumnchart; i > 0; i--)
				{
					ICell cell = activityTrendTableHeader.CreateCell(Tool.Settings.numberofyearoncolumnchart - i + 1);
					cell.SetCellValue(DateTime.Now.Year - i + 1);
					cell.CellStyle = styleTableHeader;

				}
				currentRowIndex++;

				foreach (List<ActivityTrendData> activityTrend in activityTrendData)
				{
					IRow activityTrendRow = datasheet.CreateRow(currentRowIndex);
					activityTrendRow.CreateCell(0).SetCellValue(activityTrend[0].MarketName);
					activityTrendRow.Cells[0].CellStyle = cellStyle;
					for (int i = 1; i <= activityTrend.Count; i++)
					{
						ICell currentCell = activityTrendRow.CreateCell(i);
						currentCell.SetCellValue((double)(Math.Round(((float)activityTrend[i - 1].Volume * 100 / (float)activityTrend[i - 1].TotalVolume), Tool.Settings.sharepricepercentagedecimaldigits)));
						currentCell.CellStyle = floatCellStyle;
					}
					currentRowIndex++;
				}
			}

			Random rd = new Random();
			var randomText = rd.Next(1000, 9999);
			System.IO.MemoryStream ms = new System.IO.MemoryStream();
			workbook.Write(ms);
			return File(ms.ToArray(), "application/vnd.ms-excel", "Fragulizer-" + randomText + ".xls");
		}

		private string GetShareNameByInstrumentIDs(string instrumentIDs)
		{
			string shareName = string.Empty;
			try
			{
				InstrumentGroupToolCompanySetting setting = Services.Get<IInstrumentGroupSettingService>().GetSettings(RequestHelper.CompanyCode);
				if (setting.InstrumentGroups.Count > 0)
				{
					for (int i = 0; i < setting.InstrumentGroups.Count; i++)
					{
						if (setting.InstrumentGroups[i].InstrumentIds.Trim().Replace(" ", "") == instrumentIDs.Trim().Replace(" ", ""))
						{
							shareName = setting.InstrumentGroups[i].Name;
                            if (!string.IsNullOrWhiteSpace(shareName))
                            {
                                shareName = shareName.Trim().Trim(new char[] { '\r', '\n' });
                            }
                            if (string.IsNullOrWhiteSpace(shareName))
                            {
                                shareName = RequestHelper.CompanyCode;
                            }
                            break;
						}
					}
				}
			}
			catch { }

			return shareName;
		}
	}
}
