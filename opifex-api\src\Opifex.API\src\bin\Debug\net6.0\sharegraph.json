{"AppSettings": {"ShareGraphToolSettingMappers": [{"XmlPath": "GoogleAnalyticsEnabled", "DefaultTagWithValue": "false"}, {"XmlPath": "GoogleTagEnabled", "DefaultTagWithValue": "false"}, {"XmlPath": "StyleURI", "DtoPath": "StyleUri"}, {"XmlPath": "Layout", "DtoPath": "Layout", "NotEmpty": true, "IsString": true, "ValidValues": ["FIXED", "FULL"]}, {"XmlPath": "Accessibilities:Enabled", "DtoPath": "AccessibilitiesEnabled", "NotNull": true, "IsBool": true}, {"XmlPath": "Ticker:EnabledFormat", "DtoPath": "Ticker.EnabledFormats", "NotNull": true, "IsList": true, "ValidValues": ["GRAPH", "TABLE"]}, {"XmlPath": "Ticker:TickerType", "DtoPath": "Ticker.TickerType", "NotEmpty": true, "IsString": true, "ValidValues": ["SINGLE", "MULTIPLE"]}, {"XmlPath": "Ticker:GraphTickerTemplate", "DtoPath": "Ticker.GraphTickerTemplate", "NotEmpty": true, "IsString": true, "ValidValues": ["SINGLE_TICKER_1", "SINGLE_TICKER_2", "MULTIPLE_TICKER_1", "MULTIPLE_TICKER_2", "{CustomTemplate}"]}, {"XmlPath": "Ticker:TableTickerTemplate", "DtoPath": "Ticker.TableTickerTemplate", "NotEmpty": true, "IsString": true, "ValidValues": ["TABLE_TICKER_SINGLE", "TABLE_TICKER_MULTIPLE"]}, {"XmlPath": "Ticker:GraphAnimation", "DtoPath": "Ticker.GraphAnimation", "NotEmpty": true, "IsString": true, "ValidValues": ["FADE", "BLINK_PRICE", "BLINK_MARKET"]}, {"XmlPath": "Ticker:TableAnimation", "DtoPath": "Ticker.TableAnimation", "NotEmpty": true, "IsString": true, "ValidValues": ["FADE", "TRANSFORM"]}, {"XmlPath": "Chart:DefaultTooltipType", "DtoPath": "Chart.DefaultTooltipType", "NotEmpty": true, "IsString": true, "ValidValues": ["DYNAMIC_CALLOUT", "TOOLTIP", "STATIC"]}, {"XmlPath": "Chart:DefaultChartType", "DtoPath": "Chart.DefaultChartType", "NotEmpty": true, "IsString": true, "ValidValues": ["CANDLE", "LINE", "VERTEX_LINE", "STEP", "MOUNTAIN", "HISTOGRAM", "BAR"]}, {"XmlPath": "Chart:EnabledChartTypes", "DtoPath": "Chart.EnabledChartTypes", "NotEmpty": true, "IsList": true, "ValidValues": ["CANDLE", "LINE", "VERTEX_LINE", "STEP", "MOUNTAIN", "HISTOGRAM", "BAR"]}, {"XmlPath": "Chart:DefaultPeriod", "DtoPath": "Chart.DefaultPeriod", "NotEmpty": true, "IsString": true, "ValidValues": ["1D", "5D", "1M", "3M", "6M", "YTD", "1Y", "3Y", "5Y", "ALL"]}, {"XmlPath": "Chart:EnabledPeriods", "DtoPath": "Chart.EnabledPeriods", "NotEmpty": true, "IsList": true, "ValidValues": ["1D", "5D", "1M", "3M", "6M", "YTD", "1Y", "3Y", "5Y", "CUSTOM_RANGE", "ALL"]}, {"XmlPath": "Chart:Hide<PERSON><PERSON><PERSON><PERSON>le", "DtoPath": "Chart.HideChartTitle", "NotNull": true, "IsBool": true}, {"XmlPath": "Chart:EnabledAdditionalOptions", "DtoPath": "Chart.EnabledAdditionalOptions", "NotNull": true, "IsList": true, "ValidValues": ["EXCEL", "PRINT", "EXPORT", "SHARE"]}, {"XmlPath": "Chart:EnabledSocialMedias", "DtoPath": "Chart.EnabledSocialMedias", "NotNull": true, "IsList": true, "ValidValues": ["FACEBOOK", "TWITTER", "LINKEDIN"]}, {"XmlPath": "Chart:ExcludeStudies", "DtoPath": "Chart.ExcludeStudies", "NotNull": true, "IsList": true, "ValidValues": ["BOLLINGER_BANDS", "ICHIMOKU_CLOUDS", "MACD", "RSI", "STOCHASTICS", "TYPICAL_PRICE", "VOLUME_CHART", "ADX_DMS", "TOTAL_RETURN", "MOVING_AVERAGE_CROSS"]}, {"XmlPath": "Chart:EnabledEvents", "DtoPath": "Chart.EnabledEvents", "NotNull": true, "IsList": true, "ValidValues": ["DIVIDEND", "EARNING", "PRESSRELEASES", "VIDEO"]}, {"XmlPath": "Chart:DefaultEvents", "DtoPath": "Chart.DefaultEvents", "NotNull": true, "IsList": true, "ValidValues": ["DIVIDEND", "EARNING", "PRESSRELEASES", "VIDEO"]}, {"XmlPath": "Chart:DefaultEvents", "DtoPath": "Chart.DefaultEvents", "NotNull": true, "IsList": true, "ValidValues": ["DIVIDEND", "EARNING", "PRESSRELEASES", "VIDEO"]}, {"XmlPath": "Chart:EnabledChartPreferences", "DtoPath": "Chart.EnabledChartPreferences", "NotNull": true, "IsList": true, "ValidValues": ["HIGH_LOW_VALUES", "RANGE_SELECTOR", "EXTENDED_HOURS"]}, {"XmlPath": "Chart:EnabledYAxisPreferences", "DtoPath": "Chart.EnabledYAxisPreferences", "NotNull": true, "IsList": true, "ValidValues": ["PERCENT_VIEW", "LOG_SCALE", "INVERT", "LINEAR"]}, {"XmlPath": "Chart:DefaultVolumeChart", "DtoPath": "Chart.DefaultVolumeChart"}, {"XmlPath": "Chart:EnabledHoverEvent", "DtoPath": "Chart.EnabledHoverEvent", "NotNull": true, "IsBool": true}, {"XmlPath": "Chart:HighPriceIndicatorColor", "DtoPath": "Chart.HighPriceIndicatorColor"}, {"XmlPath": "Chart:LowPriceIndicatorColor", "DtoPath": "Chart.LowPriceIndicatorColor"}, {"XmlPath": "Chart:IsStripedBackground", "DtoPath": "Chart.IsStripedBackground", "NotNull": true, "IsBool": true}, {"XmlPath": "Chart:Studies:MACD:MACD", "DtoPath": "Chart.MACD_MACD", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:MACD:Signal", "DtoPath": "Chart.MACD_Signal", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:MACD:IncreasingBar", "DtoPath": "Chart.MACD_IncreasingBar", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:MACD:DecreasingBar", "DtoPath": "Chart.MACD_DecreasingBar", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:RSI:RSI", "DtoPath": "Chart.RSI_RSI", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:BollingerBands:BollingerBandsTop", "DtoPath": "Chart.BollingerBands_BollingerBandsTop", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:BollingerBands:BollingerBandsMedian", "DtoPath": "Chart.BollingerBands_BollingerBandsMedian", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:BollingerBands:BollingerBandsBottom", "DtoPath": "Chart.BollingerBands_BollingerBandsBottom", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:IchimokuClouds:ConversionLine", "DtoPath": "Chart.IchimokuClouds_ConversionLine", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:IchimokuClouds:BaseLine", "DtoPath": "Chart.IchimokuClouds_BaseLine", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:IchimokuClouds:LeadingSpanA", "DtoPath": "Chart.IchimokuClouds_LeadingSpanA", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:IchimokuClouds:LeadingSpanB", "DtoPath": "Chart.IchimokuClouds_LeadingSpanB", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:IchimokuClouds:LeadingSpan", "DtoPath": "Chart.IchimokuClouds_LeadingSpan", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:Stochastics:K", "DtoPath": "Chart.Stochastics_K", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:Stochastics:D", "DtoPath": "Chart.Stochastics_D", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:Stochastics:OverBought", "DtoPath": "Chart.Stochastics_OverBought", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:Stochastics:OverSold", "DtoPath": "Chart.Stochastics_OverSold", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:TotalReturn:TTRT", "DtoPath": "Chart.TotalReturn_TTRT", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:ADX:DiPlus", "DtoPath": "Chart.ADX_DiPlus", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:ADX:DiMinus", "DtoPath": "Chart.ADX_DiMinus", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:ADX:ADX", "DtoPath": "Chart.ADX_ADX", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:ADX:PositiveBar", "DtoPath": "Chart.ADX_PositiveBar", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:ADX:NegativeBar", "DtoPath": "Chart.ADX_NegativeBar", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:Volume:DownVolumeColor", "DtoPath": "Chart.Volume_DownVolumeColor", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:Volume:UpVolumeColor", "DtoPath": "Chart.Volume_UpVolumeColor", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:VolumeUnderlay:DownVolumeColor", "DtoPath": "Chart.VolumeUnderlay_DownVolumeColor", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:VolumeUnderlay:UpVolumeColor", "DtoPath": "Chart.VolumeUnderlay_UpVolumeColor", "NotEmpty": true, "IsHexColorString": true}, {"XmlPath": "Chart:Studies:VolumeUnderlay:YAxisHeightFactor", "DtoPath": "Chart.VolumeUnderlay_YAxisHeightFactor", "NotNull": true}, {"XmlPath": "Peers:Enabled", "DtoPath": "Peers.Enabled", "NotNull": true, "IsBool": true}, {"XmlPath": "Peers:Animation", "DtoPath": "Peers.Animation", "NotEmpty": true, "IsString": true, "ValidValues": ["FADE", "BLINK_PRICE"]}, {"XmlPath": "Indices:Enabled", "DtoPath": "Indices.Enabled", "NotNull": true, "IsBool": true}, {"XmlPath": "Indices:Animation", "DtoPath": "Indices.Animation", "NotEmpty": true, "IsString": true, "ValidValues": ["FADE", "BLINK_PRICE"]}, {"XmlPath": "ShareDetails:Enabled", "DtoPath": "ShareDetails.Enabled", "NotNull": true, "IsBool": true}, {"XmlPath": "ShareDetails:ShareDataItems", "DtoPath": "ShareDetails.ShareDataItems", "NotNull": true, "IsList": true, "ValidValues": ["TIME", "CURRENCY", "MARKET", "MARKET_STATUS", "ISIN", "SYMBOL", "BID", "BID_SIZE", "ASK", "ASK_SIZE", "OPEN", "LAST", "CHANGE", "CHANGE_PERCENT", "HIGH", "LOW", "VOLUME", "TOTAL_TRADES", "PREVIOUS_CLOSE", "YTD_HIGH", "ALL_TIME_LOW", "YTD_LOW", "ALL_TIME_HIGH", "WEEKS_52_HIGH", "WEEKS_52_LOW", "YTD_PERCENT", "WEEKS_52_PERCENT", "LIST", "INDUSTRY", "NUMBER_OF_SHARES", "MARKET_CAP", "LOT_SIZE", "P_E", "AVERAGE_PRICE", "TURNOVER"]}, {"XmlPath": "ShareDetails:DisplayOnSelectedTicker", "DtoPath": "ShareDetails.DisplayOnSelectedTicker", "NotNull": true, "IsBool": true}, {"XmlPath": "ShareDetails:DisplayType", "DtoPath": "ShareDetails.DisplayType", "NotEmpty": true, "IsString": true, "ValidValues": ["Grid", "Flow"]}, {"XmlPath": "ShareDetails:PartialDisplay", "DtoPath": "ShareDetails.PartialDisplay", "NotNull": true, "IsBool": true}, {"XmlPath": "ShareDetails:Print", "DtoPath": "ShareDetails.Print", "NotEmpty": true, "IsString": true, "ValidValues": ["ALWAYS", "NEVER", "CONFIRM"]}, {"XmlPath": "ShareDetails:NumberItemsInCollapsedMode", "DtoPath": "ShareDetails.NumberItemsInCollapsedMode", "NotNull": true, "IsPositiveInteger": true}, {"XmlPath": "ShareDetails:NumberItemsInPrinting", "DtoPath": "ShareDetails.NumberItemsInPrinting", "NotNull": true, "IsPositiveInteger": true}, {"XmlPath": "Performance:EnabledFormat", "DtoPath": "Performance.EnabledFormats", "NotNull": true, "IsList": true, "ValidValues": ["GRAPH", "TABLE"]}, {"XmlPath": "Performance:PerformanceType", "DtoPath": "Performance.PerformanceTypes", "NotNull": true, "IsList": true, "ValidValues": ["SHARE_PRICE_DEVELOPMENT", "SHARE_PRICE_DEVELOPMENT_BY_YEARS", "52_WEEKS_HIGH_LOW"]}, {"XmlPath": "Performance:Enable52WTableColumn", "DtoPath": "Performance.Enable52WTableColumns", "NotNull": true, "IsList": true, "ValidValues": ["shareName", "low52W", "last", "high52W", "percent52WLow", "percent52WHigh"]}, {"XmlPath": "Performance:EnableSharePriceDevelopmentColumn", "DtoPath": "Performance.EnableSharePriceDevelopmentColumns", "NotNull": true, "IsList": true, "ValidValues": ["shareName", "last", "currencyCode", "change", "w52HighLow", "allTimeHighLow", "week", "month", "threeMonthChange", "sixMonthsChange", "yTD", "percent52W", "threeYearsChange", "fiveYearsChange", "tenYearsChange"]}, {"XmlPath": "Performance:NumberOfYearSPByYear", "DtoPath": "Performance.NumberOfYearSPByYear", "NotNull": true, "IsPositiveInteger": true, "LessThan": 6, "GreaterThan": 1}, {"XmlPath": "Performance:ShowEarliestYearFirstSPByYear", "DtoPath": "Performance.ShowEarliestYearFirstSPByYear", "NotNull": true, "IsBool": true}, {"XmlPath": "Trade:Enabled", "DtoPath": "Trade.Enabled", "NotNull": true, "IsBool": true}, {"XmlPath": "Trade:NumberOfTrades", "DtoPath": "Trade.NumberOfTrades", "NotNull": true, "IsPositiveInteger": true}, {"XmlPath": "Trade:TimeFormat", "DtoPath": "Trade.TimeFormat", "NotEmpty": true, "IsString": true}, {"XmlPath": "Trade:TradeRefreshSeconds", "DtoPath": "Trade.TradeRefreshSeconds", "NotNull": true, "IsPositiveInteger": true}, {"XmlPath": "OrderDepth:Enabled", "DtoPath": "OrderDepth.Enabled", "NotNull": true, "IsBool": true}, {"XmlPath": "OrderDepth:CalculateBy", "DtoPath": "OrderDepth.<PERSON>y"}, {"XmlPath": "OrderDepth:RefreshSeconds", "DtoPath": "OrderDepth.RefreshSeconds", "NotNull": true, "IsPositiveInteger": true}]}}