﻿<?xml version="1.0"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=152368
  -->
<configuration>
  <appSettings file="WebConfigs\appsettings.Debug.config">
    <add key="ClientValidationEnabled" value="true"/>
    <add key="UnobtrusiveJavaScriptEnabled" value="true"/>
    <add key="GeneralSettingsPath" value="../../Config/"/>
    <add key="BlobResourceContainer" value="Fragulizer"/>
    <add key="EnableCssJsDebuging" value="True"/>
    <add key="InkscapeEnviromentKey" value="Inkscape"/>
    <add key="DownloadURL" value="http://localhost/downloadpdf/Default.aspx"/>
  </appSettings>
  <connectionStrings configSource="WebConfigs\connections.Debug.config" />
  
  <system.web>
    <globalization culture="en-US"/>
    <customErrors mode="RemoteOnly"/>
    <compilation debug="true" targetFramework="4.8"/>
	<httpRuntime targetFramework="4.8" />
    <pages controlRenderingCompatibilityVersion="4.0">
      <namespaces>
        <add namespace="System.Web.Helpers"/>
        <add namespace="System.Web.Mvc"/>
        <add namespace="System.Web.Mvc.Ajax"/>
        <add namespace="System.Web.Mvc.Html"/>
        <add namespace="System.Web.Routing"/>
        <add namespace="System.Web.WebPages"/>
      </namespaces>
    </pages>
    <httpHandlers>
      <add verb="GET,HEAD" path="eresource.axd" validate="false" type="Euroland.Azure.Shared.ClientResource.CustomHttpHandler, Euroland.Azure.Shared"/>
      <add verb="GET" path="version.axd" validate="false" type="ToolsFramework.Mvc.DLLVersionHttpHandler, ToolsFramework"/>
    </httpHandlers>
  </system.web>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false"/>
    <modules runAllManagedModulesForAllRequests="true">
      <remove name="AuditRequestHttpModule"/>
      <add name="AuditRequestHttpModule" preCondition="integratedMode" type="ToolsFramework.Mvc.AuditHttpModule, ToolsFramework"/>
      <remove name="resouceBundle"/>
      <add name="resouceBundle" type="Euroland.Azure.Shared.ClientResource.ClientResourceBundleHttpModule"/>
    </modules>
    <handlers>
      <add name="JavascriptHandler" preCondition="integratedMode" verb="GET,HEAD" path="eresource.axd" type="Euroland.Azure.Shared.ClientResource.CustomHttpHandler, Euroland.Azure.Shared"/>
      <add name="ToolVersionHandler" preCondition="integratedMode" verb="GET" path="version.axd" type="ToolsFramework.Mvc.DLLVersionHttpHandler, ToolsFramework"/>
    </handlers>
    <!-- 
    Set "Expires" header to a far future date to "Response" header, 
    also turning on compression everything goes out of Web Server.
    Note: this functions are only available on IIS7 or above
    -->
    <staticContent>
      <clientCache httpExpires="Sun, 29 Mar 2020 00:00:00 GMT" cacheControlMode="UseExpires"/>
    </staticContent>
    <urlCompression doDynamicCompression="true" doStaticCompression="true" dynamicCompressionBeforeCache="true"/>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Configuration" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.WindowsAzure.StorageClient" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-1.7.0.0" newVersion="1.7.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.WindowsAzure.ServiceRuntime" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Data.Services.Client" publicKeyToken="b77a5c561934e089" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>