﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Euroland.NetCore.ToolsFramework.Setting" version="3.0.2" targetFramework="net48" />
  <package id="Microsoft.AspNetCore.Http" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.AspNetCore.Http.Abstractions" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.AspNetCore.Http.Features" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.AspNetCore.WebUtilities" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.FileProviders.Physical" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.FileSystemGlobbing" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.ObjectPool" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Options" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Primitives" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.Net.Http.Headers" version="2.0.0" targetFramework="net48" />
  <package id="MSBuild.Microsoft.VisualStudio.Web.targets" version="14.0.0.3" targetFramework="net40" />
  <package id="Newtonsoft.Json" version="10.0.3" targetFramework="net48" />
  <package id="SecurityCodeScan" version="3.5.3.0" targetFramework="net40" developmentDependency="true" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.4.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="4.4.0" targetFramework="net48" />
</packages>